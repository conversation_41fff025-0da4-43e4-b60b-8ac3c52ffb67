#!/usr/bin/env python3
"""
Usage Examples for Enhanced OSA Analysis with Timing Constraints
===============================================================

This file demonstrates how to use the new timing constraint features
in the main OSA analysis pipeline.
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and print the description."""
    print(f"\n{'='*60}")
    print(f"EXAMPLE: {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(cmd)}")
    print("Running...")
    
    try:
        # Change to the osa_analysis directory
        os.chdir('osa_analysis')
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Success!")
        if result.stdout:
            print("Output:")
            print(result.stdout[-1000:])  # Show last 1000 chars
    except subprocess.CalledProcessError as e:
        print("❌ Error:", e.stderr)
    except FileNotFoundError:
        print("❌ Script not found. Make sure you're in the correct directory.")
    finally:
        # Change back to original directory
        os.chdir('..')

def main():
    """Run various examples of the enhanced OSA analysis."""
    
    base_cmd = [sys.executable, "main.py"]
    
    examples = [
        # Example 1: Default behavior with strict timing constraints
        {
            "cmd": base_cmd,
            "description": "Complete OSA analysis with strict timing constraints (default)"
        },
        
        # Example 2: Disable timing constraints (traditional approach)
        {
            "cmd": base_cmd + ["--no-timing-constraints"],
            "description": "Traditional OSA analysis without timing constraints"
        },
        
        # Example 3: Moderate timing constraints
        {
            "cmd": base_cmd + ["--timing-mode", "moderate"],
            "description": "OSA analysis with moderate timing (6 months before, 1 year after)"
        },
        
        # Example 4: Custom timing constraints
        {
            "cmd": base_cmd + [
                "--timing-mode", "custom",
                "--days-before", "180",  # 6 months before
                "--days-after", "730"    # 2 years after
            ],
            "description": "OSA analysis with custom timing (6 months before, 2 years after)"
        },
        
        # Example 5: Very restrictive timing (after only)
        {
            "cmd": base_cmd + [
                "--timing-mode", "custom",
                "--days-after", "365"   # 1 year after only
            ],
            "description": "OSA analysis with restrictive timing (1 year after sleep study only)"
        }
    ]
    
    print("Enhanced OSA Analysis - Timing Constraints Examples")
    print("=" * 60)
    print("This script demonstrates different ways to use timing constraints")
    print("for diagnoses and medications around sleep studies in the main analysis.")
    print("\nTiming Modes:")
    print("- strict: No limit before sleep study, 1 year after")
    print("- moderate: 6 months before, 1 year after")
    print("- custom: Specify exact days before/after")
    print("- no-timing-constraints: Use all available data")
    print("\nAnalysis Components:")
    print("- Statistics generation")
    print("- STOP-BANG analysis")
    print("- Predictive modeling")
    print("- Model evaluation across demographic groups")
    print("- Comprehensive visualizations")
    
    # Ask user which examples to run
    print("\nAvailable examples:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
    
    choice = input(f"\nEnter example number to run (1-{len(examples)}) or 'all' for all examples: ").strip()
    
    if choice.lower() == 'all':
        for example in examples:
            run_command(example["cmd"], example["description"])
    elif choice.isdigit() and 1 <= int(choice) <= len(examples):
        example = examples[int(choice) - 1]
        run_command(example["cmd"], example["description"])
    else:
        print("Invalid choice. Please run the script again.")
        return
    
    print(f"\n{'='*60}")
    print("ANALYSIS EXAMPLES COMPLETE")
    print("="*60)
    print("Generated outputs can be found in:")
    print("- plots/ directory: Visualizations")
    print("- Console output: Statistics and analysis results")
    print("\nKey differences between timing modes:")
    print("- Patient counts: May differ significantly")
    print("- Comorbidity prevalence: More accurate with timing constraints")
    print("- Model performance: May improve with better data quality")
    print("- Medication patterns: More relevant temporal relationships")

if __name__ == "__main__":
    main()
