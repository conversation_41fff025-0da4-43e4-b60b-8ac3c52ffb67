#!/usr/bin/env python3
"""
Analysis of Pregnant Patients During Sleep Studies
================================================

This script identifies patients who:
1. Were pregnant during their sleep study (based on O09 pregnancy duration codes or O02 abortion codes)
2. Had a sleep study (OPS 1-790) around the time of pregnancy diagnosis

O09 codes indicate pregnancy duration:
- O09.1: 5-13 weeks (first trimester)
- O09.2: 14-19 weeks (second trimester)
- O09.3: 20-25 weeks (second trimester)
- O09.4: 26-33 weeks (third trimester)
- O09.5: 34-36 weeks (third trimester)
- O09.6: 37-41 weeks (term pregnancy)

O02 codes include abortion/miscarriage cases.

This helps identify sleep studies performed during active pregnancy.
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Import the data extractor
from osa_analysis.data.queries import DataExtractor

def analyze_pregnant_during_sleep_study(db_path="patient_management.db", days_window=35):
    """
    Analyze patients who were pregnant during their sleep study using pregnancy-specific time windows.
    
    Time windows used:
    - O09.1 (5-13 weeks): Sleep study within 5 weeks (35 days) before pregnancy diagnosis
    - O09.2-O09.6 (14+ weeks): Sleep study within days_window before pregnancy diagnosis
    - O02 (abortion): Sleep study within days_window of diagnosis (before or after)
    
    Args:
        db_path (str): Path to the SQLite database
        days_window (int): Days before pregnancy diagnosis to look for sleep study (default: 35)
    """
    print("Analyzing patients pregnant during sleep study...")
    print(f"Time windows: O09.1 (5-13w) = 35 days, O09.2+ (14+w) = {days_window} days, O02 = ±{days_window} days")
    print("=" * 80)
    
    # Connect to database
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        print("Available databases:")
        for file in os.listdir("."):
            if file.endswith(".db"):
                print(f"  - {file}")
        return
    
    conn = sqlite3.connect(db_path)
    extractor = DataExtractor(conn)
    
    try:
        # Get patients who were pregnant during sleep study
        pregnant_patients = extractor.get_pregnant_during_sleep_study_patients(days_window)
        
        if pregnant_patients.empty:
            print("No patients found who were pregnant during sleep study with current time windows.")
            return
        
        print(f"Found {len(pregnant_patients)} records of pregnancy during sleep studies.")
        print(f"Unique patients: {pregnant_patients['PID'].nunique()}")
        print()
        
        # Display time window statistics by pregnancy stage
        print("Time between pregnancy diagnosis and sleep study by pregnancy stage:")
        for week_range in ['5-13 weeks', '14-19 weeks', '20-25 weeks', '26-33 weeks', '34-36 weeks', '37-41 weeks']:
            stage_data = pregnant_patients[pregnant_patients['pregnancy_week_range'] == week_range]
            if not stage_data.empty:
                print(f"  {week_range}: {len(stage_data)} cases")
                print(f"    Mean days between: {stage_data['days_between'].mean():.1f}")
                print(f"    Range: {stage_data['days_between'].min():.0f} to {stage_data['days_between'].max():.0f} days")
        print()
        
        # Analyze pregnancy categories
        print("Pregnancy categories:")
        category_counts = pregnant_patients['pregnancy_category'].value_counts()
        for category, count in category_counts.items():
            print(f"  {category}: {count} cases ({count/len(pregnant_patients)*100:.1f}%)")
        print()
        
        # Analyze pregnancy week ranges
        print("Pregnancy week ranges during sleep study:")
        week_counts = pregnant_patients['pregnancy_week_range'].value_counts()
        for week_range, count in week_counts.items():
            if week_range != 'unknown':
                percentage = count/len(pregnant_patients)*100
                print(f"  {week_range}: {count} cases ({percentage:.1f}%)")
        print()
        
        # Show most common pregnancy diagnoses
        print("Most common pregnancy diagnoses:")
        primary_pregnancy = pregnant_patients[pregnant_patients['primary_icd'].str.startswith(('O09', 'O02'), na=False)]
        if not primary_pregnancy.empty:
            primary_counts = primary_pregnancy['primary_icd'].value_counts().head(10)
            for icd, count in primary_counts.items():
                text = primary_pregnancy[primary_pregnancy['primary_icd'] == icd]['primary_text'].iloc[0]
                print(f"  {icd}: {count} cases - {text}")
        print()
        
        # Show logic validation - which time window was used for each case
        print("Time window validation:")
        print("(Positive days_between = diagnosis after sleep study, Negative = diagnosis before sleep study)")
        for week_range in pregnant_patients['pregnancy_week_range'].unique():
            if week_range != 'unknown':
                stage_data = pregnant_patients[pregnant_patients['pregnancy_week_range'] == week_range]
                window_used = 35 if week_range == '5-13 weeks' else days_window
                print(f"  {week_range}: Used {window_used}-day window, {len(stage_data)} cases found")
                if not stage_data.empty:
                    within_window = stage_data[(stage_data['days_between'] >= 0) & (stage_data['days_between'] <= window_used)]
                    print(f"    Cases within window: {len(within_window)}/{len(stage_data)}")
        print()
        
        # Get patient demographics for this subcategory
        demographics = extractor.get_patient_demographics()
        pregnant_demographics = demographics[demographics['PID'].isin(pregnant_patients['PID'])]
        
        if not pregnant_demographics.empty:
            print("Demographics of pregnant patients during sleep study:")
            sex_dist = pregnant_demographics['Sex'].value_counts()
            print("  Gender distribution:")
            for sex, count in sex_dist.items():
                print(f"    {sex}: {count} ({count/len(pregnant_demographics)*100:.1f}%)")
            print()
        
        # Show sample cases by pregnancy stage
        print("Sample cases by pregnancy stage:")
        for week_range in ['5-13 weeks', '14-19 weeks', '20-25 weeks', '26-33 weeks', '34-36 weeks', '37-41 weeks']:
            stage_data = pregnant_patients[pregnant_patients['pregnancy_week_range'] == week_range]
            if not stage_data.empty:
                print(f"\n  {week_range} (first 2 cases):")
                for _, case in stage_data.head(2).iterrows():
                    print(f"    Patient {case['PID']}:")
                    print(f"      Pregnancy: {case['pregnancy_diagnosis_date']} - {case['primary_icd']}")
                    print(f"      Sleep Study: {case['sleep_study_date']} ({case['days_between']:.0f} days difference)")
        
        # Create visualization if we have data
        create_pregnancy_visualizations(pregnant_patients)
        
        # Save results to CSV
        output_file = f"pregnant_during_sleep_study_w{days_window}.csv"
        pregnant_patients.to_csv(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
    finally:
        conn.close()

def create_pregnancy_visualizations(pregnant_patients):
    """Create visualizations for pregnancy during sleep study analysis."""
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Pregnancy During Sleep Study Analysis', fontsize=16, fontweight='bold')
    
    # 1. Distribution of days between pregnancy diagnosis and sleep study
    axes[0, 0].hist(pregnant_patients['days_between'], bins=20, alpha=0.7, edgecolor='black')
    axes[0, 0].set_xlabel('Days between pregnancy diagnosis and sleep study')
    axes[0, 0].set_ylabel('Number of patients')
    axes[0, 0].set_title('Time Distribution: Pregnancy Diagnosis vs Sleep Study')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Same day')
    axes[0, 0].legend()
    
    # 2. Pregnancy week ranges
    week_counts = pregnant_patients['pregnancy_week_range'].value_counts()
    week_counts = week_counts[week_counts.index != 'unknown']
    if not week_counts.empty:
        axes[0, 1].pie(week_counts.values, labels=week_counts.index, autopct='%1.1f%%')
        axes[0, 1].set_title('Pregnancy Week Ranges During Sleep Study')
    else:
        axes[0, 1].text(0.5, 0.5, 'No pregnancy week data\navailable', 
                       ha='center', va='center', transform=axes[0, 1].transAxes)
        axes[0, 1].set_title('Pregnancy Week Ranges During Sleep Study')
    
    # 3. Most common pregnancy diagnoses
    primary_pregnancy = pregnant_patients[pregnant_patients['primary_icd'].str.startswith(('O09', 'O02'), na=False)]
    if not primary_pregnancy.empty:
        top_diagnoses = primary_pregnancy['primary_icd'].value_counts().head(8)
        axes[1, 0].barh(range(len(top_diagnoses)), top_diagnoses.values)
        axes[1, 0].set_yticks(range(len(top_diagnoses)))
        axes[1, 0].set_yticklabels(top_diagnoses.index)
        axes[1, 0].set_xlabel('Number of cases')
        axes[1, 0].set_title('Most Common Pregnancy Diagnoses')
        axes[1, 0].grid(True, alpha=0.3)
    else:
        axes[1, 0].text(0.5, 0.5, 'No pregnancy diagnosis\ndata available', 
                       ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 0].set_title('Most Common Pregnancy Diagnoses')
    
    # 4. Timeline scatter plot by pregnancy category
    categories = pregnant_patients['pregnancy_category'].unique()
    colors = sns.color_palette("husl", len(categories))
    
    for i, category in enumerate(categories):
        category_data = pregnant_patients[pregnant_patients['pregnancy_category'] == category]
        if not category_data.empty:
            category_data['pregnancy_date_numeric'] = pd.to_datetime(category_data['pregnancy_diagnosis_date']).map(pd.Timestamp.timestamp)
            axes[1, 1].scatter(category_data['pregnancy_date_numeric'], category_data['days_between'], 
                             alpha=0.6, label=category, color=colors[i])
    
    axes[1, 1].set_xlabel('Pregnancy Diagnosis Date')
    axes[1, 1].set_ylabel('Days between diagnosis and sleep study')
    axes[1, 1].set_title('Timeline: Pregnancy Category vs Days to Sleep Study')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5, label='Same day')
    axes[1, 1].legend()
    
    plt.tight_layout()
    
    # Save plot
    plot_filename = 'pregnant_during_sleep_study_analysis.png'
    plt.savefig(f'plots/{plot_filename}', dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: plots/{plot_filename}")
    
    plt.show()

def main():
    """Main function to run the analysis."""
    print("Pregnancy During Sleep Study Analysis")
    print("====================================")
    print()
    print("Time window logic:")
    print("- O09.1 (5-13 weeks): Sleep study within 35 days before pregnancy diagnosis")
    print("- O09.2+ (14+ weeks): Sleep study within specified days before pregnancy diagnosis") 
    print("- O02 (abortion): Sleep study within specified days of diagnosis (±)")
    print()
    
    # Get user input for time window
    try:
        days_input = input("Enter time window in days for O09.2+ and O02 codes (default: 35): ")
        days_window = int(days_input) if days_input.strip() else 35
    except ValueError:
        print("Invalid input. Using default value of 35 days.")
        days_window = 35
    
    # Get database path
    db_input = input("Enter database path (default: patient_management.db): ")
    db_path = db_input.strip() if db_input.strip() else "patient_management.db"
    
    # Run analysis
    analyze_pregnant_during_sleep_study(db_path, days_window)

if __name__ == "__main__":
    main()
