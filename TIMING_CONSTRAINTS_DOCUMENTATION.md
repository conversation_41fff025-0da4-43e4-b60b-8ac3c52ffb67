# Timing-Constrained Data Extraction Documentation

## Overview

The DataExtractor class now supports timing constraints for diagnoses and medications relative to sleep studies. This allows for more precise analysis by only including relevant diagnoses and medications that occurred within a reasonable timeframe around the sleep study.

## Key Features

### 1. Timing Parameters

All relevant functions now accept these timing parameters:

- `around_sleep_study` (bool): Enable/disable timing constraints (default: False)
- `days_before` (int): Days before sleep study to include (None = no limit)
- `days_after` (int): Days after sleep study to include (default: 365 = 1 year)

### 2. Updated Functions

The following functions now support timing constraints:

- `get_hypertension_patients()`
- `get_patients_on_antihypertensives()`
- `get_medication_by_atc_class()`
- `get_diabetes_patients()`
- `get_cardiovascular_patients()`

### 3. Enhanced Data Extraction

The main `extract_all_data()` function now accepts timing parameters:

```python
def extract_all_data(self, use_sleep_study_timing=False, 
                     days_before_sleep_study=None, 
                     days_after_sleep_study=365):
```

## Usage Examples

### Basic Usage

```python
from osa_analysis.data.queries import DataExtractor
import sqlite3

conn = sqlite3.connect('patient_management.db')
extractor = DataExtractor(conn)

# Get hypertension patients with strict timing (no limit before, 1 year after)
hypertension_patients = extractor.get_hypertension_patients(
    around_sleep_study=True,
    days_before=None,      # No limit before sleep study
    days_after=365         # 1 year after sleep study
)

# Get patients on antihypertensives within 6 months of sleep study
antihypertensive_patients = extractor.get_patients_on_antihypertensives(
    around_sleep_study=True,
    days_before=180,       # 6 months before
    days_after=180         # 6 months after
)
```

### Complete Data Extraction with Timing

```python
# Extract all data with custom timing constraints
df, top_meds, top_atc, atc_classes = extractor.extract_all_data(
    use_sleep_study_timing=True,
    days_before_sleep_study=180,    # 6 months before
    days_after_sleep_study=365      # 1 year after
)

# Use predefined strict timing (no limit before, 1 year after)
df_strict, _, _, _ = extractor.extract_all_data_with_strict_timing()

# Use predefined moderate timing (6 months before, 1 year after)
df_moderate, _, _, _ = extractor.extract_all_data_with_moderate_timing()
```

## Rationale

### Why Timing Constraints Matter

1. **Clinical Relevance**: Diagnoses made years before or after a sleep study may not be relevant to the sleep disorder assessment.

2. **Medication Timing**: Medications prescribed long after a sleep study may be for conditions unrelated to sleep disorders.

3. **Data Quality**: Reduces noise from unrelated medical events.

4. **Research Validity**: Ensures temporal relationship between sleep study and comorbidities.

### Recommended Time Windows

Based on clinical practice, the following time windows are recommended:

- **Hypertension**: No limit before, up to 1 year after sleep study
- **Medications**: 6 months before to 1 year after sleep study
- **Diabetes**: No limit before, up to 1 year after sleep study
- **Cardiovascular Disease**: No limit before, up to 1 year after sleep study

## Implementation Details

### Database Queries

The timing constraints are implemented using SQLite's `JULIANDAY()` function:

```sql
JULIANDAY(diagnosis_date) - JULIANDAY(sleep_study_date) BETWEEN -days_before AND days_after
```

### Medication Timing

For medications, the `Verordnungsdatum` (prescription date) is compared with the sleep study date (`Durchfuehrungsdatum`).

### Diagnosis Timing

For diagnoses, the `Feststellungsdatum` (diagnosis date) is compared with the sleep study date.

## Backward Compatibility

All functions maintain backward compatibility. When timing parameters are not specified or `around_sleep_study=False`, the functions behave exactly as before, returning all patients regardless of timing.

## Performance Considerations

- Timing-constrained queries may take longer as they involve JOIN operations between diagnosis/medication and procedure tables
- The generic function `get_patients_with_diagnosis_around_sleep_study()` provides detailed timing information but may be slower for large datasets
- Consider using appropriate indexes on date fields for better performance

## Future Enhancements

1. **Relative Timing**: Support for timing relative to diagnosis dates rather than sleep study dates
2. **Multiple Sleep Studies**: Handle patients with multiple sleep studies
3. **Medication Duration**: Consider medication duration rather than just prescription date
4. **Flexible Time Units**: Support for weeks, months in addition to days
