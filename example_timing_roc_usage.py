#!/usr/bin/env python3
"""
Usage Examples for Enhanced ROC Analysis with Timing Constraints
==============================================================

This file demonstrates how to use the new timing constraint features
in the create_model_gender_roc.py script.
"""

import subprocess
import sys

def run_command(cmd, description):
    """Run a command and print the description."""
    print(f"\n{'='*60}")
    print(f"EXAMPLE: {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(cmd)}")
    print("Running...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ Success!")
        if result.stdout:
            print("Output:", result.stdout[-500:])  # Show last 500 chars
    except subprocess.CalledProcessError as e:
        print("❌ Error:", e.stderr)
    except FileNotFoundError:
        print("❌ Script not found. Make sure you're in the correct directory.")

def main():
    """Run various examples of the enhanced ROC analysis."""
    
    base_cmd = [sys.executable, "create_model_gender_roc.py"]
    
    examples = [
        # Example 1: Default behavior with strict timing constraints
        {
            "cmd": base_cmd + ["--model", "random_forest"],
            "description": "Random Forest with strict timing constraints (default)"
        },
        
        # Example 2: Disable timing constraints (traditional approach)
        {
            "cmd": base_cmd + ["--model", "random_forest", "--no-timing-constraints"],
            "description": "Random Forest without timing constraints (traditional)"
        },
        
        # Example 3: Moderate timing constraints
        {
            "cmd": base_cmd + ["--model", "random_forest", "--timing-mode", "moderate"],
            "description": "Random Forest with moderate timing (6 months before, 1 year after)"
        },
        
        # Example 4: Custom timing constraints
        {
            "cmd": base_cmd + [
                "--model", "random_forest", 
                "--timing-mode", "custom",
                "--days-before", "90",  # 3 months before
                "--days-after", "730"   # 2 years after
            ],
            "description": "Random Forest with custom timing (3 months before, 2 years after)"
        },
        
        # Example 5: STOP-BANG with timing constraints
        {
            "cmd": base_cmd + ["--model", "stop_bang", "--timing-mode", "strict"],
            "description": "STOP-BANG with strict timing constraints"
        },
        
        # Example 6: Different font size
        {
            "cmd": base_cmd + [
                "--model", "random_forest", 
                "--timing-mode", "moderate",
                "--font-size", "20"
            ],
            "description": "Random Forest with moderate timing and large font size"
        }
    ]
    
    print("Enhanced ROC Analysis - Timing Constraints Examples")
    print("=" * 60)
    print("This script demonstrates different ways to use timing constraints")
    print("for diagnoses and medications around sleep studies.")
    print("\nTiming Modes:")
    print("- strict: No limit before sleep study, 1 year after")
    print("- moderate: 6 months before, 1 year after")
    print("- custom: Specify exact days before/after")
    print("- no-timing-constraints: Use all available data")
    
    # Ask user which examples to run
    print("\nAvailable examples:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
    
    choice = input(f"\nEnter example number to run (1-{len(examples)}) or 'all' for all examples: ").strip()
    
    if choice.lower() == 'all':
        for example in examples:
            run_command(example["cmd"], example["description"])
    elif choice.isdigit() and 1 <= int(choice) <= len(examples):
        example = examples[int(choice) - 1]
        run_command(example["cmd"], example["description"])
    else:
        print("Invalid choice. Please run the script again.")
        return
    
    print(f"\n{'='*60}")
    print("EXAMPLES COMPLETE")
    print("="*60)
    print("Generated plots can be found in the 'plots' directory.")
    print("\nKey differences to look for:")
    print("- Patient counts may differ between timing modes")
    print("- AUC values may change based on included data")
    print("- More restrictive timing = potentially better data quality")
    print("- Less restrictive timing = larger sample sizes")

if __name__ == "__main__":
    main()
