import sqlite3

# Connect to SQLite database (will create it if it doesn't exist)
conn = sqlite3.connect('../patient_management_pseudo.db')
cursor = conn.cursor()

# Create Patient table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Patient (
    PID TEXT PRIMARY KEY,
    Titel TEXT,
    Vorname TEXT,
    Nachname TEXT,
    Geburtsname TEXT,
    Strassenname TEXT,
    Hausnummer TEXT,
    Postleitzahl TEXT,
    Wohnort TEXT,
    AdministrativesGeschlecht TEXT,
    Geburtsdatum DATE,
    Todestag DATE,
    PatientVerstorben BOOLEAN,
    Laendercode TEXT,
    Familienstand TEXT,
    Krankenversicherungsnummer TEXT,
    Versicherungsname TEXT,
    InstitutionskennzeichenIK TEXT,
    Krankenkassentyp TEXT,
    Krankenkassengruppe TEXT,
    Versicherungsstatus TEXT,
    COVIDImpfung TEXT
)
''')

# Create Fall table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Fall (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    Falltyp TEXT,
    Aufenthaltsart TEXT,
    Fallkennzeichnung TEXT,
    VorstationaeresAufnahmedatum DATETIME,
    Aufnahmedatum DATETIME,
    Entlassdatum DATETIME,
    Entlassgrund TEXT,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create Diagnosen table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Diagnosen (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    Feststellungsdatum DATE,
    PrimaerdiagnoseICD TEXT,
    PrimaerdiagnoseText TEXT,
    SekundaerdiagnoseICD TEXT,
    SekundaerdiagnoseText TEXT,
    PrimaereICDVersion TEXT,
    UrsacheFuerOP TEXT,
    HauptOderNebendiagnose TEXT,
    Diagnosetyp TEXT,
    Diagnosesicherheit TEXT,
    Seitenlokalisation TEXT,
    Fachabteilung TEXT,
    Ambulanz TEXT,
    Station TEXT,
    Funktionsstelle TEXT,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create Prozeduren table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Prozeduren (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    Durchfuehrungsdatum DATE,
    OPSCode TEXT,
    OPSCodeText TEXT,
    OPSVersion TEXT,
    Seitenlokalisation TEXT,
    FallHauptOderNebenprozedur TEXT,
    OPHauptOderNebenprozedur TEXT,
    Fachabteilung TEXT,
    Ambulanz TEXT,
    Station TEXT,
    Funktionsstelle TEXT,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create Vitaldaten table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Vitaldaten (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    Vitalparameter TEXT,
    Vitalwert TEXT,
    Text TEXT,
    Masseinheit TEXT,
    ZeitpunktDerMessung DATETIME,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create Medikation table
cursor.execute('''
CREATE TABLE IF NOT EXISTS Medikation (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    ZeitpunktDerVergabe DATETIME,
    StartzeitpunktDerVergabe DATETIME,
    EndzeitpunktDerVergabe DATETIME,
    Wirkstoffrate TEXT,
    Dosierung TEXT,
    Einheit TEXT,
    Status TEXT,
    Medikationsbezeichnung TEXT,
    ATCCode TEXT,
    ATCBezeichnung TEXT,
    StartzeitpunktDerVerordnung DATETIME,
    EndzeitpunktDerVerordnung DATETIME,
    Verabreichungsform TEXT,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create LSTM table
cursor.execute('''
CREATE TABLE IF NOT EXISTS LSTM (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    PID TEXT,
    FallScheinnummer TEXT,
    Auftragsstatus TEXT,
    Auftragsnummer TEXT,
    Dringlichkeit TEXT,
    Leistungsanforderer1 TEXT,
    Leistungsanforderer2 TEXT,
    Zusatzempfaenger1 TEXT,
    Zusatzempfaenger2 TEXT,
    Leistungserbringer2 TEXT,
    ArtDesBefunds TEXT,
    Durchgefuehrt BOOLEAN,
    Termin DATETIME,
    AuftragsbezogenerHinweis TEXT,
    Befund TEXT,
    Beruteilung TEXT,
    Empfehlung TEXT,
    Cave TEXT,
    BefundetAm DATETIME,
    FOREIGN KEY (PID) REFERENCES Patient(PID)
)
''')

# Create indexes for better performance
cursor.execute('CREATE INDEX IF NOT EXISTS idx_fall_pid ON Fall(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_fall_scheinnummer ON Fall(FallScheinnummer)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_diagnosen_pid ON Diagnosen(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_diagnosen_scheinnummer ON Diagnosen(FallScheinnummer)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_prozeduren_pid ON Prozeduren(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_prozeduren_scheinnummer ON Prozeduren(FallScheinnummer)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_vitaldaten_pid ON Vitaldaten(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_vitaldaten_scheinnummer ON Vitaldaten(FallScheinnummer)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_medikation_pid ON Medikation(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_medikation_scheinnummer ON Medikation(FallScheinnummer)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_lstm_pid ON LSTM(PID)')
cursor.execute('CREATE INDEX IF NOT EXISTS idx_lstm_scheinnummer ON LSTM(FallScheinnummer)')

# Commit changes and close connection
conn.commit()
conn.close()

print("Database 'patient_management_pseudo.db' created successfully with all tables and relationships.")
