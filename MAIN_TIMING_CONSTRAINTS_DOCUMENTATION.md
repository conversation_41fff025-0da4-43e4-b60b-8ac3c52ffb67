# Enhanced OSA Analysis with Timing Constraints

## Overview

The main OSA analysis pipeline (`osa_analysis/main.py`) now supports timing constraints for diagnoses and medications relative to sleep studies. This enhancement provides more clinically relevant analysis by focusing on medical events that occurred within reasonable timeframes around the sleep study.

## New Features

### Command Line Options

```bash
python main.py [OPTIONS]

Options:
  --no-timing-constraints
      Disable timing constraints (use all diagnoses/medications regardless of timing)
      
  --timing-mode {strict,moderate,custom}
      Timing constraint mode (default: strict)
      - strict: No limit before sleep study, 1 year after
      - moderate: 6 months before sleep study, 1 year after  
      - custom: Specify custom day ranges
      
  --days-before N
      Days before sleep study to include (for custom mode)
      If not specified, no limit is applied
      
  --days-after N
      Days after sleep study to include (default: 365)
      Used with custom mode
```

## Usage Examples

### Default Behavior (Strict Timing)

```bash
# Run with strict timing constraints (default)
python main.py

# Equivalent to:
python main.py --timing-mode strict
```

This includes:
- All diagnoses/medications before sleep study (no time limit)
- Diagnoses/medications up to 1 year after sleep study

### Traditional Analysis (No Timing Constraints)

```bash
# Run without timing constraints (traditional approach)
python main.py --no-timing-constraints
```

This includes all available diagnoses and medications regardless of timing.

### Moderate Timing Constraints

```bash
# Run with moderate timing constraints
python main.py --timing-mode moderate
```

This includes:
- Diagnoses/medications from 6 months before sleep study
- Diagnoses/medications up to 1 year after sleep study

### Custom Timing Constraints

```bash
# Custom: 3 months before, 2 years after
python main.py --timing-mode custom --days-before 90 --days-after 730

# Custom: Only after sleep study (no before limit)
python main.py --timing-mode custom --days-after 365

# Custom: 1 year before and after
python main.py --timing-mode custom --days-before 365 --days-after 365
```

## Analysis Components Affected

All major analysis components are affected by timing constraints:

### 1. Data Extraction
- **Patient cohorts**: OSA, suspected OSA, unsuspected OSA patients
- **Comorbidities**: Hypertension, diabetes, cardiovascular disease
- **Medications**: Antihypertensive medications by ATC class
- **Demographics**: Age, sex, BMI (not time-constrained)

### 2. Statistical Analysis
- **Prevalence rates**: More accurate comorbidity prevalence
- **Medication patterns**: Temporally relevant medication usage
- **Top medications**: OSA-related medication patterns

### 3. STOP-BANG Analysis
- **Hypertension component**: Uses timing-constrained hypertension data
- **BMI component**: Not affected (calculated from vitals)
- **Age/Sex**: Not affected

### 4. Predictive Modeling
- **Features**: All comorbidity and medication features use timing constraints
- **Model training**: Cleaner feature data may improve performance
- **Cross-validation**: Applied to timing-constrained dataset

### 5. Model Evaluation
- **Demographic subgroups**: Analysis across age, sex, BMI groups
- **Performance metrics**: AUC, sensitivity, specificity
- **Subgroup comparisons**: More reliable with timing constraints

### 6. Visualizations
- **Distribution plots**: Reflect timing-constrained data
- **Correlation analysis**: More accurate temporal relationships
- **Model comparison**: Performance differences may be visible

## Expected Impact on Results

### With Timing Constraints
**Advantages:**
- **Higher data quality**: Only relevant medical events included
- **Better temporal relationships**: Clear timing between sleep study and conditions
- **Improved model performance**: Less noise in feature data
- **Clinical relevance**: Focus on OSA-related conditions

**Potential disadvantages:**
- **Smaller sample sizes**: Fewer patients meet timing criteria
- **Reduced statistical power**: May affect significance tests
- **Conservative estimates**: May underestimate true prevalence

### Without Timing Constraints (Traditional)
**Advantages:**
- **Larger sample sizes**: All patients with any relevant data
- **Higher statistical power**: More data for analysis
- **Comprehensive view**: Historical medical information included

**Potential disadvantages:**
- **Lower data quality**: Unrelated medical events included
- **Unclear temporal relationships**: Past conditions may not be OSA-related
- **Model noise**: Irrelevant features may reduce performance

## Output Differences

### Console Output
The analysis provides timing constraint information:

```
Starting OSA Analysis...
Using timing constraints mode: strict
Extracting data from database...
Using strict timing constraints: no limit before, 1 year after sleep study
Data extraction complete. Dataset contains 1,234 patients.
...
Analysis complete!

Timing constraints applied: strict mode
  - Only diagnoses/medications within specified timeframe were included
```

### Generated Files
- **Statistics**: Reflect timing-constrained prevalence rates
- **Visualizations**: Based on filtered dataset
- **Model outputs**: Performance metrics from clean data

## Clinical Interpretation

### Recommended Timing Modes

**For Clinical Research:**
- Use `--timing-mode strict` or `--timing-mode moderate`
- Ensures temporal relationship between sleep study and conditions
- More reliable for causal inference

**For Epidemiological Studies:**
- Use `--no-timing-constraints` for comprehensive prevalence data
- Include all historical medical information
- Better for population-level statistics

**For Model Development:**
- Use `--timing-mode strict` for cleaner training data
- Reduces noise from unrelated medical events
- May improve predictive performance

### Quality Considerations

1. **Sample Size**: Monitor patient counts with timing constraints
2. **Prevalence Changes**: Compare rates between timing modes
3. **Model Performance**: Evaluate if timing improves predictions
4. **Clinical Relevance**: Ensure timing windows match clinical practice

## Performance Considerations

- **Database queries**: Timing-constrained queries may take longer
- **Memory usage**: Similar regardless of timing mode
- **Processing time**: Marginally longer with timing constraints
- **Disk space**: Similar output file sizes

## Troubleshooting

### Small Sample Sizes
If timing constraints result in very small datasets:
1. Use `--timing-mode moderate` for more inclusive timing
2. Use `--no-timing-constraints` to see full dataset size
3. Consider custom timing with larger day ranges

### Performance Issues
If queries are slow:
1. Ensure database has proper indexes on date fields
2. Consider smaller timing windows
3. Run analysis during off-peak hours

### Comparison Studies
To compare approaches:
1. Run with and without timing constraints
2. Document differences in sample sizes and results
3. Choose approach based on research objectives

## Future Enhancements

1. **Multiple timing windows**: Compare different timing strategies
2. **Adaptive timing**: Optimize timing based on data characteristics
3. **Diagnosis weighting**: Weight recent diagnoses more heavily
4. **Medication duration**: Consider ongoing vs. discontinued medications
