import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, auc
import statsmodels.api as sm
from scipy.stats import chi2_contingency, ttest_ind
import os

# Create plots directory if it doesn't exist
os.makedirs('plots', exist_ok=True)

# Connect to the database
conn = sqlite3.connect('../patient_management.db')

# Load data
print("Loading data from database...")
# Get diagnoses data
diagnoses_query = """
SELECT p.PID, p.AdministrativesGeschlecht as Sex,
       d.<PERSON>erdiagnoseICD, d.Seku<PERSON>erdiagnoseICD
FROM Patient p
JOIN Diagnosen d ON p.PID = d.PID
"""
diagnoses_df = pd.read_sql_query(diagnoses_query, conn)

# Get procedures data
procedures_query = """
SELECT p.PID, pr.OPSCode
FROM Patient p
JOIN Prozeduren pr ON p.PID = pr.PID
"""
procedures_df = pd.read_sql_query(procedures_query, conn)

# Get vital data (for BMI)
vitals_query = """
SELECT p.PID, v.Vitalparameter, v.Vitalwert
FROM Patient p
JOIN Vitaldaten v ON p.PID = v.PID
WHERE v.Vitalparameter IN ('Größe', 'Gewicht')
"""
vitals_df = pd.read_sql_query(vitals_query, conn)

# Get medication data
meds_query = """
SELECT p.PID, m.ATCCode, m.Medikationsbezeichnung
FROM Patient p
JOIN Medikation m ON p.PID = m.PID
"""
meds_df = pd.read_sql_query(meds_query, conn)

# Close connection
conn.close()

# Process data
print("Processing data...")

# Identify OSA patients (G47.31)
diagnoses_df['has_osa'] = (
    (diagnoses_df['PrimaerdiagnoseICD'] == 'G47.31') |
    (diagnoses_df['SekundaerdiagnoseICD'] == 'G47.31')
).astype(int)

# Get unique patients with OSA status
patients = diagnoses_df[['PID', 'Sex', 'has_osa']].drop_duplicates()
patients['has_osa'] = patients.groupby('PID')['has_osa'].transform('max')
patients = patients.drop_duplicates()

# Process vital data to calculate BMI
height_df = vitals_df[vitals_df['Vitalparameter'] == 'Größe'].copy()
# Handle null values before conversion
height_df = height_df[height_df['Vitalwert'] != '(null)']
height_df['height'] = pd.to_numeric(height_df['Vitalwert'], errors='coerce') / 100  # Convert cm to m
height_df = height_df.groupby('PID')['height'].mean().reset_index()

weight_df = vitals_df[vitals_df['Vitalparameter'] == 'Gewicht'].copy()
# Handle null values before conversion
weight_df = weight_df[weight_df['Vitalwert'] != '(null)']
weight_df['weight'] = pd.to_numeric(weight_df['Vitalwert'], errors='coerce')  # kg
weight_df = weight_df.groupby('PID')['weight'].mean().reset_index()

# Calculate BMI
bmi_df = pd.merge(height_df, weight_df, on='PID', how='inner')
bmi_df['BMI'] = bmi_df['weight'] / (bmi_df['height'] ** 2)
bmi_df = bmi_df[['PID', 'BMI']]

# Identify hypertension patients (I10-I15)
diagnoses_df['has_diagnosed_hypertension'] = (
    diagnoses_df['PrimaerdiagnoseICD'].str.startswith(('I10', 'I11', 'I12', 'I13', 'I14', 'I15'), na=False) |
    diagnoses_df['SekundaerdiagnoseICD'].str.startswith(('I10', 'I11', 'I12', 'I13', 'I14', 'I15'), na=False)
).astype(int)

hypertension_df = diagnoses_df[['PID', 'has_diagnosed_hypertension']].drop_duplicates()
hypertension_df['has_diagnosed_hypertension'] = hypertension_df.groupby('PID')['has_diagnosed_hypertension'].transform('max')
hypertension_df = hypertension_df.drop_duplicates()

# Identify diabetes patients (E10-E14)
diagnoses_df['has_diabetes'] = (
    diagnoses_df['PrimaerdiagnoseICD'].str.startswith(('E10', 'E11', 'E12', 'E13', 'E14'), na=False) |
    diagnoses_df['SekundaerdiagnoseICD'].str.startswith(('E10', 'E11', 'E12', 'E13', 'E14'), na=False)
).astype(int)

diabetes_df = diagnoses_df[['PID', 'has_diabetes']].drop_duplicates()
diabetes_df['has_diabetes'] = diabetes_df.groupby('PID')['has_diabetes'].transform('max')
diabetes_df = diabetes_df.drop_duplicates()

# Identify cardiovascular disease patients (I20-I25, I50)
diagnoses_df['has_cardiovascular'] = (
    diagnoses_df['PrimaerdiagnoseICD'].str.startswith(('I20', 'I21', 'I22', 'I23', 'I24', 'I25', 'I50'), na=False) |
    diagnoses_df['SekundaerdiagnoseICD'].str.startswith(('I20', 'I21', 'I22', 'I23', 'I24', 'I25', 'I50'), na=False)
).astype(int)

cardiovascular_df = diagnoses_df[['PID', 'has_cardiovascular']].drop_duplicates()
cardiovascular_df['has_cardiovascular'] = cardiovascular_df.groupby('PID')['has_cardiovascular'].transform('max')
cardiovascular_df = cardiovascular_df.drop_duplicates()

# Identify patients on antihypertensive medications
# ATC codes for antihypertensives: C02, C03, C07, C08, C09
meds_df['on_antihypertensives'] = meds_df['ATCCode'].str.startswith(('C02', 'C03', 'C07', 'C08', 'C09'), na=False).astype(int)
antihypertensives_df = meds_df[['PID', 'on_antihypertensives']].drop_duplicates()
antihypertensives_df['on_antihypertensives'] = antihypertensives_df.groupby('PID')['on_antihypertensives'].transform('max')
antihypertensives_df = antihypertensives_df.drop_duplicates()

# Merge all data
df = patients.copy()
df = pd.merge(df, bmi_df, on='PID', how='left')
df = pd.merge(df, hypertension_df, on='PID', how='left')
df = pd.merge(df, diabetes_df, on='PID', how='left')
df = pd.merge(df, cardiovascular_df, on='PID', how='left')
df = pd.merge(df, antihypertensives_df, on='PID', how='left')

# Fill NaN values with 0 for binary variables
binary_cols = ['has_diagnosed_hypertension', 'has_diabetes','has_cardiovascular', 'on_antihypertensives']
df[binary_cols] = df[binary_cols].fillna(0)

# Combined hypertension definition (diagnosed or on medications)
df['has_hypertension'] = ((df['has_diagnosed_hypertension'] == 1) | (df['on_antihypertensives'] == 1)).astype(int)

# Extract age from PID (assuming PID contains birth date in format YYYYMMDD at positions 7-14)
try:
    df['birthdate'] = pd.to_datetime(df['PID'].str[6:14], format='%Y%m%d', errors='coerce')
    current_date = pd.Timestamp.now()
    df['Age'] = ((current_date - df['birthdate']).dt.days / 365.25).astype(float)
except:
    print("Could not extract age from PID. Age will not be available for modeling.")

# Convert sex to dummy variable
if 'Sex' in df.columns and df['Sex'].notna().any():
    df['is_male'] = (df['Sex'] == 'männlich').astype(int)

# Prepare data for modeling
print("\nPreparing data for Random Forest model...")
model_cols = ['Age', 'has_hypertension', 'has_diabetes',
             'has_cardiovascular', 'on_antihypertensives']

# Add sex as dummy variable if available
if 'is_male' in df.columns:
    model_cols.append('is_male')

# Add BMI if available
if 'BMI' in df.columns and df['BMI'].notna().any():
    model_cols.append('BMI')

# Filter rows with complete data
model_df = df[['has_osa'] + model_cols].dropna()

print(f"Number of patients with complete data: {len(model_df)}")
print(f"Number of OSA patients: {model_df['has_osa'].sum()}")
print(f"Number of non-OSA patients: {len(model_df) - model_df['has_osa'].sum()}")

if len(model_df) > 5:  # Only proceed if we have enough data
    print(f"\nBuilding Random Forest model with {len(model_df)} patients...")
    X = model_df[model_cols]
    y = model_df['has_osa']

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    # Train Random Forest model
    rf = RandomForestClassifier(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)

    # Evaluate model
    y_pred = rf.predict(X_test)
    y_prob = rf.predict_proba(X_test)[:, 1]

    print("\nRandom Forest Model Performance:")
    print(classification_report(y_test, y_pred))

    # Calculate AUC
    auc_score = roc_auc_score(y_test, y_prob)
    print(f"\nArea Under the ROC Curve (AUC): {auc_score:.3f}")

    # Plot ROC curve
    fpr, tpr, thresholds = roc_curve(y_test, y_prob)
    roc_auc = auc(fpr, tpr)

    plt.figure(figsize=(8, 8))
    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve for Random Forest Model')
    plt.legend(loc="lower right")
    plt.tight_layout()
    plt.savefig('plots/random_forest_roc_curve.png')
    plt.close()

    print("\nROC curve saved to 'plots/random_forest_roc_curve.png'")

    # Feature importance
    feature_importance = pd.DataFrame({
        'Feature': model_cols,
        'Importance': rf.feature_importances_
    }).sort_values('Importance', ascending=False)

    print("\nFeature Importance for Predicting OSA:")
    for i, row in feature_importance.iterrows():
        print(f"  {row['Feature']}: {row['Importance']:.4f}")

    # Plot feature importance
    plt.figure(figsize=(10, 8))
    sns.barplot(data=feature_importance, x='Importance', y='Feature')
    plt.title('Feature Importance for Predicting OSA')
    plt.xlabel('Importance')
    plt.ylabel('Feature')
    plt.tight_layout()
    plt.savefig('plots/random_forest_feature_importance.png')
    plt.close()

    print("\nFeature importance plot saved to 'plots/random_forest_feature_importance.png'")

    # If sex information is available, calculate AUC by sex
    if 'Sex' in df.columns and df['Sex'].notna().any():
        print("\nCalculating AUC by sex:")
        for sex in ['männlich', 'weiblich']:
            sex_model_df = model_df[df['Sex'] == sex]
            print(f"  Number of {sex} patients with complete data: {len(sex_model_df)}")
            if len(sex_model_df) > 3:
                sex_X = sex_model_df[model_cols]
                sex_y = sex_model_df['has_osa']

                # Split data
                sex_X_train, sex_X_test, sex_y_train, sex_y_test = train_test_split(
                    sex_X, sex_y, test_size=0.3, random_state=42
                )

                # Train Random Forest model
                sex_rf = RandomForestClassifier(n_estimators=100, random_state=42)
                sex_rf.fit(sex_X_train, sex_y_train)

                # Calculate AUC
                sex_y_prob = sex_rf.predict_proba(sex_X_test)[:, 1]
                sex_auc = roc_auc_score(sex_y_test, sex_y_prob)
                print(f"  AUC for {sex} patients: {sex_auc:.3f}")

                # Plot ROC curve
                sex_fpr, sex_tpr, _ = roc_curve(sex_y_test, sex_y_prob)
                sex_roc_auc = auc(sex_fpr, sex_tpr)

                plt.figure(figsize=(8, 8))
                plt.plot(sex_fpr, sex_tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {sex_roc_auc:.3f})')
                plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('False Positive Rate')
                plt.ylabel('True Positive Rate')
                plt.title(f'ROC Curve for Random Forest Model - {sex} Patients')
                plt.legend(loc="lower right")
                plt.tight_layout()
                plt.savefig(f'plots/random_forest_roc_curve_{sex}.png')
                plt.close()

                print(f"  ROC curve for {sex} patients saved to 'plots/random_forest_roc_curve_{sex}.png'")
            else:
                print(f"  Insufficient data for {sex}-specific model (n={len(sex_model_df)})")
else:
    print("\nInsufficient data for predictive modeling.")
