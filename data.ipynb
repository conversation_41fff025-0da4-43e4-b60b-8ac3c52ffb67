{"cells": [{"cell_type": "code", "execution_count": 2, "id": "82263ee2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to database: ../patient_management.db\n", "Extracting data from database...\n"]}], "source": ["\n", "from osa_analysis.data.database import DatabaseConnector\n", "from osa_analysis.data.queries import DataExtractor\n", "\n", "# Connect to the database\n", "db_connector = DatabaseConnector('../patient_management.db')\n", "conn = db_connector.get_connection()\n", "    \n", "# Extract data\n", "print(\"Extracting data from database...\")\n", "extractor = DataExtractor(conn)"]}, {"cell_type": "code", "execution_count": 3, "id": "91fa14d3", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [], "source": ["diags_grouped = extractor.sql(\"\"\"\n", "    SELECT \n", "        SUBSTR(icd, 1, 2) as icd_category,\n", "        COUNT(*) as count,\n", "        COUNT(DISTINCT icd) as unique_codes\n", "    FROM (\n", "        SELECT PrimaerdiagnoseICD as icd, PrimaerdiagnoseText as text\n", "        FROM Diagnosen\n", "        WHERE PrimaerdiagnoseICD != '(null)'\n", "        \n", "        UNION ALL\n", "        \n", "        SELECT SekundaerdiagnoseICD as icd, SekundaerdiagnoseText as text\n", "        FROM Diagnosen\n", "        WHERE SekundaerdiagnoseICD != '(null)'\n", "    ) combined_diagnoses\n", "    GROUP BY SUBSTR(icd, 1, 2)\n", "    ORDER BY count DESC\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 4, "id": "4ea1072e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ICD Codes grouped by first 2 characters:\n", "    icd_category  count  unique_codes\n", "0             G4  20134            58\n", "1             G2   6207            37\n", "2             U5   6195            16\n", "3             Z9   5898            69\n", "4             I1   5835            22\n", "..           ...    ...           ...\n", "202           P9      4             1\n", "203           A8      3             1\n", "204           A3      2             1\n", "205           A9      1             1\n", "206           A2      1             1\n", "\n", "[207 rows x 3 columns]\n", "\n", "Detailed breakdown by category:\n", "\n", "=== Category G4 ===\n", "  G47.31: <PERSON><PERSON><PERSON><PERSON><PERSON> Schlafapnoe-Syndrom (count: 11113)\n", "  G47.8: <PERSON><PERSON><PERSON>ö<PERSON> (count: 1911)\n", "  G47.39: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nicht n<PERSON><PERSON> be<PERSON> (count: 1519)\n", "  G47.0: Ein<PERSON> <PERSON> Durchschlafstörungen (count: 922)\n", "  G47.1: <PERSON><PERSON><PERSON> geste<PERSON><PERSON>ü<PERSON> (count: 796)\n", "\n", "=== Category G2 ===\n", "  G25.81: Syndrom der unruhigen Beine [Restless-Legs-Syndrom] (count: 1753)\n", "  G20.90: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nicht näher bezeichnet: <PERSON><PERSON> Wirkungsfluktuation (count: 929)\n", "  G20.10: <PERSON><PERSON><PERSON><PERSON>-Syndrom mit mäßiger bis schwerer Beeinträchtigung: Ohne Wirkungsfluktuation (count: 906)\n", "  G20.11: <PERSON><PERSON><PERSON><PERSON>-Syndrom mit mäßiger bis schwerer Beeinträchtigung: Mit Wirkungsfluktuation (count: 756)\n", "  G25.80: Periodische Beinbewegungen im Schlaf (count: 540)\n", "\n", "=== Category U5 ===\n", "  U52.0: Frührehabilitations-Barthel-Index: 31 und mehr Punkte (count: 1281)\n", "  U50.00: <PERSON><PERSON> oder geringe motorische Funktionseinschränkung: Barthel-Index: 100 Punkte (count: 1141)\n", "  U50.10: Leichte motorische Funktionseinschränkung: Barthel-Index: 80-95 <PERSON><PERSON> (count: 828)\n", "  U50.30: Mittelschwere motorische Funktionseinschränkung: Barthel-Index: 40-55 <PERSON><PERSON> (count: 598)\n", "  U50.40: Schwere motorische Funktionseinschränkung: Barthel-Index: 20-35 <PERSON><PERSON> (count: 539)\n", "\n", "=== Category Z9 ===\n", "  Z92.1: <PERSON><PERSON><PERSON><PERSON><PERSON> (gegenwärtig) mit Antikoagulanzien in der Eigenanamnese (count: 1023)\n", "  Z92.2: <PERSON><PERSON><PERSON><PERSON><PERSON> (gegenwärtig) mit anderen Arzneimitteln in der Eigenanamnese (count: 486)\n", "  Z95.0: <PERSON><PERSON><PERSON><PERSON><PERSON> eines kardialen elektronischen Geräts (count: 436)\n", "  Z94.81: <PERSON><PERSON><PERSON> nach hämatopoetischer Stammzelltransplantation mit gegenwärtiger Immunsuppression (count: 409)\n", "  Z95.5: Vorhandensein eines Implantates oder Transplantates nach koronarer Gefäßplastik (count: 239)\n", "\n", "=== Category I1 ===\n", "  I10.00: <PERSON><PERSON><PERSON> essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven <PERSON> (count: 2510)\n", "  I10.90: <PERSON><PERSON><PERSON><PERSON>ypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise (count: 2400)\n", "  I10.91: <PERSON><PERSON><PERSON><PERSON>ie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise (count: 272)\n", "  I10.01: <PERSON><PERSON><PERSON> essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven <PERSON> (count: 257)\n", "  I11.00: Hypertensive Herzkrankheit mit (kongestiver) Herzinsuffizienz: <PERSON><PERSON> einer hypertensiven Kris<PERSON> (count: 152)\n", "\n", "=== Category Z0 ===\n", "  Z01.7: <PERSON><PERSON><PERSON><PERSON><PERSON> (count: 3686)\n", "  Z01.9: <PERSON><PERSON><PERSON><PERSON>, nicht n<PERSON><PERSON> be<PERSON> (count: 431)\n", "  Z08.0: Nachuntersuchung nach chirurgischem Eingriff wegen bösartiger Neubildung (count: 155)\n", "  Z01.5: Diagnostische Haut- und Sensibilisierungstestung (count: 86)\n", "  Z03.4: Beobachtung bei Verdacht auf Herzinfarkt (count: 64)\n", "\n", "=== Category E7 ===\n", "  E78.0: <PERSON><PERSON> (count: 1056)\n", "  E78.5: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nicht nä<PERSON> be<PERSON> (count: 988)\n", "  E79.0: Hyperurikämie ohne Zeichen von entzündlicher Arthritis oder tophischer Gicht (count: 447)\n", "  E78.2: <PERSON><PERSON><PERSON><PERSON> Hyperlipidämie (count: 355)\n", "  E78.4: <PERSON><PERSON><PERSON> (count: 251)\n", "\n", "=== Category E1 ===\n", "  E11.90: <PERSON><PERSON><PERSON> melli<PERSON>, Typ 2: <PERSON><PERSON> Komplikationen: <PERSON><PERSON> als entgle<PERSON> bezeichnet (count: 1474)\n", "  E11.74: <PERSON><PERSON><PERSON> mellitus, Typ 2: <PERSON><PERSON> multiplen Komplikationen: <PERSON><PERSON> diabetis<PERSON><PERSON>, nicht als entgleist bezeichnet (count: 317)\n", "  E11.40: <PERSON><PERSON><PERSON> mellitus, Typ 2: <PERSON>t neurologischen Komplikationen: <PERSON><PERSON> als entgleist bezeichnet (count: 278)\n", "  E11.72: <PERSON><PERSON><PERSON> mellitus, Typ 2: <PERSON>t multiplen Komplikationen: <PERSON>t sonstigen multiplen Komplikationen, nicht als entgleist bezeichnet (count: 238)\n", "  E11.91: <PERSON><PERSON><PERSON> melli<PERSON>, Typ 2: <PERSON><PERSON> Komplikationen: <PERSON><PERSON> en<PERSON>gle<PERSON> beze<PERSON> (count: 213)\n", "\n", "=== Category F4 ===\n", "  F45.41: Chronische Schmerzstörung mit somatischen und psychischen Faktoren (count: 1071)\n", "  F43.1: Posttraumatische Belastungsstörung (count: 337)\n", "  F43.2: Anpassungsstörungen (count: 242)\n", "  F41.0: Panikstörung [episodisch paroxysmale Angst] (count: 198)\n", "  F45.1: Undifferenzierte Somatisierungsstörung (count: 160)\n", "\n", "=== Category G3 ===\n", "  G35.10: Multiple Sklerose mit vorherrschend schubförmigem Verlauf: <PERSON><PERSON>abe einer akuten Exazerbation oder Progression (count: 1528)\n", "  G30.1: Alzheimer-Krankhe<PERSON> mit spätem Beginn (count: 351)\n", "  G31.82: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (count: 215)\n", "  G35.30: Multiple Sklerose mit sekundär-chronischem Verlauf: <PERSON><PERSON> einer akuten Exazerbation oder Progression (count: 214)\n", "  G30.0: <PERSON>-Krankhe<PERSON> mit fr<PERSON><PERSON>ginn (count: 95)\n"]}], "source": ["# Display the grouped results\n", "print(\"ICD Codes grouped by first 2 characters:\")\n", "print(diags_grouped)\n", "\n", "# Also create a detailed breakdown showing individual codes within each category\n", "diags_detailed = extractor.sql(\"\"\"\n", "    SELECT \n", "        SUBSTR(icd, 1, 2) as icd_category,\n", "        icd,\n", "        text,\n", "        COUNT(*) as count\n", "    FROM (\n", "        SELECT PrimaerdiagnoseICD as icd, PrimaerdiagnoseText as text\n", "        FROM Diagnosen\n", "        WHERE PrimaerdiagnoseICD != '(null)'\n", "        \n", "        UNION ALL\n", "        \n", "        SELECT SekundaerdiagnoseICD as icd, SekundaerdiagnoseText as text\n", "        FROM Diagnosen\n", "        WHERE SekundaerdiagnoseICD != '(null)'\n", "    ) combined_diagnoses\n", "    GROUP BY icd, text\n", "    ORDER BY SUBSTR(icd, 1, 2), count DESC\n", "\"\"\")\n", "\n", "print(\"\\nDetailed breakdown by category:\")\n", "for category in diags_grouped['icd_category'].head(10):  # Show top 10 categories\n", "    print(f\"\\n=== Category {category} ===\")\n", "    category_codes = diags_detailed[diags_detailed['icd_category'] == category].head(5)\n", "    for _, row in category_codes.iterrows():\n", "        print(f\"  {row['icd']}: {row['text']} (count: {row['count']})\")"]}, {"cell_type": "code", "execution_count": 5, "id": "e0d6fa54", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "icd_category", "rawType": "object", "type": "string"}, {"name": "count", "rawType": "int64", "type": "integer"}, {"name": "unique_codes", "rawType": "int64", "type": "integer"}], "ref": "3dd2bdc8-8989-4e9f-b34e-3091b0954a29", "rows": [["0", "G4", "20134", "58"], ["1", "G2", "6207", "37"], ["2", "U5", "6195", "16"], ["3", "Z9", "5898", "69"], ["4", "I1", "5835", "22"], ["5", "Z0", "4735", "28"], ["6", "E7", "3582", "20"], ["7", "E1", "3111", "42"], ["8", "F4", "2968", "45"], ["9", "G3", "2964", "21"], ["10", "F3", "2943", "26"], ["11", "I2", "2720", "37"], ["12", "J9", "2706", "36"], ["13", "F0", "2397", "31"], ["14", "Z1", "2388", "6"], ["15", "U9", "2303", "2"], ["16", "E0", "2029", "22"], ["17", "I4", "1979", "39"], ["18", "E8", "1906", "35"], ["19", "G1", "1857", "12"], ["20", "I6", "1728", "54"], ["21", "T8", "1721", "82"], ["22", "G7", "1617", "21"], ["23", "M5", "1607", "53"], ["24", "N1", "1584", "33"], ["25", "R4", "1544", "30"], ["26", "I5", "1491", "17"], ["27", "F5", "1465", "26"], ["28", "S0", "1377", "71"], ["29", "J4", "1362", "43"], ["30", "M1", "1332", "69"], ["31", "R1", "1320", "22"], ["32", "G6", "1295", "20"], ["33", "N3", "1293", "26"], ["34", "C7", "1292", "42"], ["35", "R5", "1260", "21"], ["36", "F1", "1222", "32"], ["37", "D6", "1211", "36"], ["38", "R0", "1209", "28"], ["39", "E6", "1199", "34"], ["40", "G8", "1165", "45"], ["41", "M4", "1145", "80"], ["42", "C4", "1068", "27"], ["43", "J3", "1063", "38"], ["44", "I8", "1054", "39"], ["45", "B9", "1034", "22"], ["46", "C6", "997", "21"], ["47", "H4", "994", "23"], ["48", "Z4", "993", "30"], ["49", "N4", "962", "23"]], "shape": {"columns": 3, "rows": 207}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>icd_category</th>\n", "      <th>count</th>\n", "      <th>unique_codes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>G4</td>\n", "      <td>20134</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>G2</td>\n", "      <td>6207</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>U5</td>\n", "      <td>6195</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Z9</td>\n", "      <td>5898</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>I1</td>\n", "      <td>5835</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>P9</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>A8</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>A3</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>A9</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>A2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>207 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    icd_category  count  unique_codes\n", "0             G4  20134            58\n", "1             G2   6207            37\n", "2             U5   6195            16\n", "3             Z9   5898            69\n", "4             I1   5835            22\n", "..           ...    ...           ...\n", "202           P9      4             1\n", "203           A8      3             1\n", "204           A3      2             1\n", "205           A9      1             1\n", "206           A2      1             1\n", "\n", "[207 rows x 3 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["diags_grouped"]}, {"cell_type": "code", "execution_count": 6, "id": "21254a17", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'diags' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 13\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# G47.31: a<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m# Z01.7: Laboruntersuchung\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# I10.90: used\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# F51.0: Nichtorganische Insomnie\u001b[39;00m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# G47.0: Ein- und Durchschlafstörungen\u001b[39;00m\n\u001b[0;32m---> 13\u001b[0m \u001b[43mdiags\u001b[49m\u001b[38;5;241m.\u001b[39mquery(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124micd not in [\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG47.31\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mZ01.7\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mI10.90\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mI10.00\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mZ11\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mU99.0!\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG47.8\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG25.81\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG47.39\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mE11.90\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mF51.0\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mG47.0\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'diags' is not defined"]}], "source": ["\n", "# G47.31: ap<PERSON>e\n", "# Z01.7: Labor<PERSON><PERSON><PERSON><PERSON>\n", "# I10.90: used\n", "# I10.00: used\n", "# Z11: Spezielle Verfahren zur Untersuchung auf infektiöse und parasitäre Krankheiten\n", "# U99.0!: Spezielle Verfahren zur Untersuchung auf SARS-CoV-2\n", "# G47.8: Sons<PERSON>ge Schlafstörungen\n", "# G25.81: Syndrom der unruhigen Beine [Restless-Legs-Syndrom]\n", "# G47.39: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nicht näher beze<PERSON>net\n", "# E11.90: used\n", "# F51.0: Nichtorganische Insomnie\n", "# G47.0: Ein- und Durchschlafstörungen\n", "diags.query(\"icd not in ['G47.31', 'Z01.7', 'I10.90', 'I10.00', 'Z11', 'U99.0!', 'G47.8', 'G25.81', 'G47.39', 'E11.90', 'F51.0', 'G47.0']\")"]}, {"cell_type": "code", "execution_count": null, "id": "103b<PERSON><PERSON>", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "icd", "rawType": "object", "type": "string"}, {"name": "text", "rawType": "object", "type": "string"}, {"name": "count", "rawType": "int64", "type": "integer"}], "ref": "da879909-b529-4117-9bb1-2271c9856da0", "rows": [["288", "O09.6!", "Schwangerschaftsdauer: 37. <PERSON><PERSON><PERSON> bis 41 vollendete Wochen", "95"], ["1211", "O09.3!", "Schwangerschaftsdauer: 20. Woche bis 25 vollendete Wochen", "19"], ["1212", "O09.5!", "Schwangerschaftsdauer: 34. <PERSON><PERSON><PERSON> bis 36 vollendete Wochen", "19"], ["1375", "O09.1!", "Schwangerschaftsdauer: 5 bis 13 vollendete Wochen", "16"], ["1447", "O09.4!", "Schwangerschaftsdauer: 26. W<PERSON>e bis 33 vollendete Wochen", "15"], ["2227", "O02.1", "Missed abortion [<PERSON><PERSON><PERSON><PERSON>bur<PERSON>]", "7"], ["2395", "O09.2!", "Schwangerschaftsdauer: 14. Woche bis 19 vollendete Wochen", "6"], ["3234", "O00.1", "Tubargravidität", "3"], ["3235", "O04.1", "Ärztlich eingeleiteter Abort: Inkomplett, kompliziert durch Spätblutung oder verstärkte Blutung", "3"], ["4224", "O00.9", "Extrauteringravidität, nicht näher bezeichnet", "1"], ["4225", "O06.9", "Nicht näher bezeichneter Abort: Komplett oder nicht näher bezeichnet, ohne Komplikation", "1"]], "shape": {"columns": 3, "rows": 11}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>icd</th>\n", "      <th>text</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>288</th>\n", "      <td>O09.6!</td>\n", "      <td>Schwangerschaftsdauer: 37. Woche bis 41 vollen...</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1211</th>\n", "      <td>O09.3!</td>\n", "      <td>Schwangerschaftsdauer: 20. Woche bis 25 vollen...</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1212</th>\n", "      <td>O09.5!</td>\n", "      <td>Schwangerschaftsdauer: 34. Woche bis 36 vollen...</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1375</th>\n", "      <td>O09.1!</td>\n", "      <td>Schwangerschaftsdauer: 5 bis 13 vollendete Wochen</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1447</th>\n", "      <td>O09.4!</td>\n", "      <td>Schwangerschaftsdauer: 26. Woche bis 33 vollen...</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2227</th>\n", "      <td>O02.1</td>\n", "      <td>Missed abortion [<PERSON>erhalt<PERSON> Fehlgeburt]</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2395</th>\n", "      <td>O09.2!</td>\n", "      <td>Schwangerschaftsdauer: 14. Woche bis 19 vollen...</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3234</th>\n", "      <td>O00.1</td>\n", "      <td>Tubargravidität</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3235</th>\n", "      <td>O04.1</td>\n", "      <td>Ärztlich eingeleiteter Abort: Inkomplett, komp...</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4224</th>\n", "      <td>O00.9</td>\n", "      <td>Extrauteringravidität, nicht näher bezeichnet</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4225</th>\n", "      <td>O06.9</td>\n", "      <td>Nicht näher bezeichneter Abort: Komplett oder ...</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         icd                                               text  count\n", "288   O09.6!  Schwangerschaftsdauer: 37. <PERSON><PERSON><PERSON> bis 41 vollen...     95\n", "1211  O09.3!  Schwangerschaftsdauer: 20. Woche bis 25 vollen...     19\n", "1212  O09.5!  Schwangerschaftsdauer: 34. <PERSON><PERSON><PERSON> bis 36 vollen...     19\n", "1375  O09.1!  Schwangerschaftsdauer: 5 bis 13 vollendete Wochen     16\n", "1447  O09.4!  Schwangerschaftsdauer: 26. <PERSON><PERSON><PERSON> bis 33 vollen...     15\n", "2227   O02.1            Missed abortion [Verhaltene Fehlgeburt]      7\n", "2395  O09.2!  Schwangerschaftsdauer: 14. W<PERSON><PERSON> bis 19 vollen...      6\n", "3234   O00.1                                    Tubargravidität      3\n", "3235   O04.1  Ärztlich eingeleiteter Abort: Inkomplett, komp...      3\n", "4224   O00.9      Extrauteringravidität, nicht näher bezeichnet      1\n", "4225   O06.9  <PERSON>cht näher bezeichneter Abort: Komplett oder ...      1"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["diags.query(\"icd.str.startswith('O0', na=False)\")"]}, {"cell_type": "code", "execution_count": null, "id": "57a65b9b", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PID", "rawType": "object", "type": "string"}, {"name": "pregnancy_diagnosis_date", "rawType": "object", "type": "string"}, {"name": "primary_icd", "rawType": "object", "type": "string"}, {"name": "primary_text", "rawType": "object", "type": "string"}, {"name": "secondary_icd", "rawType": "object", "type": "string"}, {"name": "secondary_text", "rawType": "object", "type": "string"}, {"name": "sleep_study_date", "rawType": "object", "type": "string"}, {"name": "days_between", "rawType": "float64", "type": "float"}, {"name": "pregnancy_category", "rawType": "object", "type": "string"}, {"name": "pregnancy_week_range", "rawType": "object", "type": "string"}], "ref": "1a8efd24-912d-4183-bc63-e7312f4d8e2e", "rows": [["0", "1544458", "2019-02-05 11:14:00", "Z34", "Überwachung einer normalen Schwangerschaft", "O09.1!", "Schwangerschaftsdauer: 5 bis 13 vollendete Wochen", "2019-02-04 19:00:00", "0.6763888890855014", "pregnancy_duration", "5-13 weeks"], ["1", "1544458", "2019-02-06 18:08:00", "Z34", "Überwachung einer normalen Schwangerschaft", "O09.1!", "Schwangerschaftsdauer: 5 bis 13 vollendete Wochen", "2019-02-04 19:00:00", "1.9638888891786337", "pregnancy_duration", "5-13 weeks"], ["2", "1544458", "2019-02-06 18:08:00", "Z34", "Überwachung einer normalen Schwangerschaft", "O09.1!", "Schwangerschaftsdauer: 5 bis 13 vollendete Wochen", "2019-02-05 19:00:00", "0.9638888891786337", "pregnancy_duration", "5-13 weeks"], ["3", "1620736", "2020-08-04 10:41:00", "O36.5", "Betreuung der Mutter wegen fetaler Wachstumsretardierung", "O09.5!", "Schwangerschaftsdauer: 34. <PERSON><PERSON><PERSON> bis 36 vollendete Wochen", "2020-07-14 19:00:00", "20.65347222238779", "pregnancy_duration", "34-36 weeks"], ["4", "485785", "2021-02-15 11:23:00", "O35.8", "Betreuung der Mutter bei (Verdacht auf) sonstige Anomalie oder Schädigung des Fetus", "O09.3!", "Schwangerschaftsdauer: 20. Woche bis 25 vollendete Wochen", "2021-02-03 19:00:00", "11.682638889178634", "pregnancy_duration", "20-25 weeks"], ["5", "485785", "2021-02-15 11:23:00", "O35.8", "Betreuung der Mutter bei (Verdacht auf) sonstige Anomalie oder Schädigung des Fetus", "O09.3!", "Schwangerschaftsdauer: 20. Woche bis 25 vollendete Wochen", "2021-02-04 19:00:00", "10.682638889178634", "pregnancy_duration", "20-25 weeks"]], "shape": {"columns": 10, "rows": 6}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PID</th>\n", "      <th>pregnancy_diagnosis_date</th>\n", "      <th>primary_icd</th>\n", "      <th>primary_text</th>\n", "      <th>secondary_icd</th>\n", "      <th>secondary_text</th>\n", "      <th>sleep_study_date</th>\n", "      <th>days_between</th>\n", "      <th>pregnancy_category</th>\n", "      <th>pregnancy_week_range</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1544458</td>\n", "      <td>2019-02-05 11:14:00</td>\n", "      <td>Z34</td>\n", "      <td>Überwachung einer normalen Schwangerschaft</td>\n", "      <td>O09.1!</td>\n", "      <td>Schwangerschaftsdauer: 5 bis 13 vollendete Wochen</td>\n", "      <td>2019-02-04 19:00:00</td>\n", "      <td>0.676389</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>5-13 weeks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1544458</td>\n", "      <td>2019-02-06 18:08:00</td>\n", "      <td>Z34</td>\n", "      <td>Überwachung einer normalen Schwangerschaft</td>\n", "      <td>O09.1!</td>\n", "      <td>Schwangerschaftsdauer: 5 bis 13 vollendete Wochen</td>\n", "      <td>2019-02-04 19:00:00</td>\n", "      <td>1.963889</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>5-13 weeks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1544458</td>\n", "      <td>2019-02-06 18:08:00</td>\n", "      <td>Z34</td>\n", "      <td>Überwachung einer normalen Schwangerschaft</td>\n", "      <td>O09.1!</td>\n", "      <td>Schwangerschaftsdauer: 5 bis 13 vollendete Wochen</td>\n", "      <td>2019-02-05 19:00:00</td>\n", "      <td>0.963889</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>5-13 weeks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1620736</td>\n", "      <td>2020-08-04 10:41:00</td>\n", "      <td>O36.5</td>\n", "      <td>Betreuung der Mutter wegen fetaler Wachstumsre...</td>\n", "      <td>O09.5!</td>\n", "      <td>Schwangerschaftsdauer: 34. Woche bis 36 vollen...</td>\n", "      <td>2020-07-14 19:00:00</td>\n", "      <td>20.653472</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>34-36 weeks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>485785</td>\n", "      <td>2021-02-15 11:23:00</td>\n", "      <td>O35.8</td>\n", "      <td>Betreuung der Mutter bei (Verdacht auf) sonsti...</td>\n", "      <td>O09.3!</td>\n", "      <td>Schwangerschaftsdauer: 20. Woche bis 25 vollen...</td>\n", "      <td>2021-02-03 19:00:00</td>\n", "      <td>11.682639</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>20-25 weeks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>485785</td>\n", "      <td>2021-02-15 11:23:00</td>\n", "      <td>O35.8</td>\n", "      <td>Betreuung der Mutter bei (Verdacht auf) sonsti...</td>\n", "      <td>O09.3!</td>\n", "      <td>Schwangerschaftsdauer: 20. Woche bis 25 vollen...</td>\n", "      <td>2021-02-04 19:00:00</td>\n", "      <td>10.682639</td>\n", "      <td>pregnancy_duration</td>\n", "      <td>20-25 weeks</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       PID pregnancy_diagnosis_date primary_icd  \\\n", "0  1544458      2019-02-05 11:14:00         Z34   \n", "1  1544458      2019-02-06 18:08:00         Z34   \n", "2  1544458      2019-02-06 18:08:00         Z34   \n", "3  1620736      2020-08-04 10:41:00       O36.5   \n", "4   485785      2021-02-15 11:23:00       O35.8   \n", "5   485785      2021-02-15 11:23:00       O35.8   \n", "\n", "                                        primary_text secondary_icd  \\\n", "0         Überwachung einer normalen Schwangerschaft        O09.1!   \n", "1         Überwachung einer normalen Schwangerschaft        O09.1!   \n", "2         Überwachung einer normalen Schwangerschaft        O09.1!   \n", "3  Betreuung der Mutter wegen fetaler Wachstumsre...        O09.5!   \n", "4  Betreuung der Mutter bei (Verdacht auf) sonsti...        O09.3!   \n", "5  Betreuung der Mutter bei (Verdacht auf) sonsti...        O09.3!   \n", "\n", "                                      secondary_text     sleep_study_date  \\\n", "0  Schwangerschaftsdauer: 5 bis 13 vollendete Wochen  2019-02-04 19:00:00   \n", "1  Schwangerschaftsdauer: 5 bis 13 vollendete Wochen  2019-02-04 19:00:00   \n", "2  Schwangerschaftsdauer: 5 bis 13 vollendete Wochen  2019-02-05 19:00:00   \n", "3  Schwangerschaftsdauer: 34. Woche bis 36 vollen...  2020-07-14 19:00:00   \n", "4  Schwangerschaftsdauer: 20. Woche bis 25 vollen...  2021-02-03 19:00:00   \n", "5  Schwangerschaftsdauer: 20. Woche bis 25 vollen...  2021-02-04 19:00:00   \n", "\n", "   days_between  pregnancy_category pregnancy_week_range  \n", "0      0.676389  pregnancy_duration           5-13 weeks  \n", "1      1.963889  pregnancy_duration           5-13 weeks  \n", "2      0.963889  pregnancy_duration           5-13 weeks  \n", "3     20.653472  pregnancy_duration          34-36 weeks  \n", "4     11.682639  pregnancy_duration          20-25 weeks  \n", "5     10.682639  pregnancy_duration          20-25 weeks  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["extractor.get_pregnant_during_sleep_study_patients()"]}, {"cell_type": "code", "execution_count": null, "id": "8169604e", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataExtractor' object has no attribute 'get_patients_with_diagnosis_around_sleep_study'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[14], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m o0_patients \u001b[38;5;241m=\u001b[39m \u001b[43mextractor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_patients_with_diagnosis_around_sleep_study\u001b[49m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'DataExtractor' object has no attribute 'get_patients_with_diagnosis_around_sleep_study'"]}], "source": ["o0_patients = extractor.get_patients_with_diagnosis_around_sleep_study(\"\")"]}, {"cell_type": "code", "execution_count": 9, "id": "81093055", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PID", "rawType": "object", "type": "string"}, {"name": "FallScheinnummer", "rawType": "object", "type": "string"}, {"name": "Medikationsbezeichnung", "rawType": "object", "type": "string"}, {"name": "ATCCode", "rawType": "object", "type": "string"}, {"name": "ATCBezeichnung", "rawType": "object", "type": "string"}, {"name": "sleep_study_date", "rawType": "object", "type": "string"}, {"name": "medication_date", "rawType": "object", "type": "string"}, {"name": "days_between", "rawType": "float64", "type": "float"}], "ref": "1e77359b-5b7e-4860-8d04-54d8f458fe68", "rows": [["0", "1001252", "62511138", "Zopiclon 7,5 mg", "(null)", "(null)", "2021-08-02 19:00:00", "2015-10-08 13:00:00", "-2125.25"], ["1", "1001783", "4791283", "ESCITALOPRAM 5 mg Filmtabletten | (Escitalopram)", "(null)", "(null)", "2020-05-14 19:00:00", "2019-12-31 23:59:59", "-134.79167824052274"], ["2", "1004166", "62204022", "Cellcept", "(null)", "(null)", "2017-04-26 21:00:00", "2014-07-29 10:33:00", "-1002.4354166667908"], ["3", "1010802", "62109848", "<PERSON><PERSON><PERSON>", "(null)", "(null)", "2024-05-06 19:00:00", "2014-02-26 16:30:00", "-3722.1041666665114"], ["4", "1012161", "62099452", "Cetirizin Tbl.", "(null)", "(null)", "2015-12-07 20:26:00", "2014-04-10 12:37:00", "-606.3256944445893"], ["5", "1015556", "63816017", "PENICILLIN G INFECTOPHARM 5 Mega P.z.H.e.IIL Dsfl. | (Benzylpenicillin, Natrium-Ion)", "J01CE01", "Benzylpenicillin", "2022-03-18 22:00:00", "2020-08-13 10:43:00", "-582.4701388888061"], ["6", "1016910", "62878041", "Bitte BE: CK, CK-MB, Myoglobin, TropT", "(null)", "(null)", "2017-04-07 22:00:00", "2017-01-13 10:30:00", "-84.47916666651145"], ["7", "1019270", "62704749", "E153", "(null)", "(null)", "2022-03-30 19:00:00", "2016-05-27 17:00:00", "-2133.083333333023"], ["8", "1021130", "62854523", "ENALAPRIL ratiopharm 10 mg Tabletten | (Enalapril)", "C09AA02", "Enalapril", "2017-02-06 21:00:00", "2016-12-21 10:04:00", "-47.45555555541068"], ["9", "1021402", "4574203", "PARACETAMOL Kabi 10 mg/ml 1.000 mg Infusionslsg. | (Paracetamol)", "N02BE01", "Paracetamol", "2020-05-13 19:00:00", "2019-06-30 23:59:59", "-317.79167824052274"], ["10", "1022401", "63506876", "RESTEX 100 mg/25 mg Hartkapseln retardiert | (Benserazid)", "N04BA11", "Levod<PERSON>a in Kombination mit Benserazid", "2019-05-08 19:00:00", "2019-05-10 09:37:00", "1.6090277777984738"], ["11", "1023509", "62999430", "RAMIPRIL HEXAL 5 mg Tabletten | (Ramipril)", "(null)", "(null)", "2016-11-17 21:00:00", "2017-07-06 11:00:00", "230.58333333348855"], ["12", "102526", "63399129", "Desmopressin i.v.", "H01BA02", "Desmopressin", "2022-06-27 19:00:00", "2019-01-10 12:45:00", "-1264.2604166665114"], ["13", "1027126", "63766083", "ALLOPURINOL-ratiopharm 300 mg Tabletten | (Allopurinol)", "M04AA01", "Allopurinol", "2022-06-07 19:00:00", "2020-07-31 09:54:00", "-676.3791666664183"], ["14", "1028400", "63788733", "Magnesium p.o.", "(null)", "(null)", "2019-10-23 19:00:00", "2020-06-26 11:45:00", "246.6979166669771"], ["15", "1029772", "63219598", "MONO EMBOLEX 3.000 I.E.Prophyl.Sicherh.Spr. | (Certoparin natrium)", "B01AB13", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-03-04 19:00:00", "2018-04-19 11:48:00", "-319.29999999981374"], ["16", "1030982", "64075169", "VALDOXAN 25 mg Filmtabletten | (Agomelatin)", "N06AX22", "Agomelatin", "2021-03-29 19:00:00", "2021-08-20 11:21:00", "143.68125000037253"], ["17", "1031159", "63665835", "!BGA kapillär", "(null)", "(null)", "2021-06-25 22:00:00", "2019-12-23 12:57:00", "-550.3770833332092"], ["18", "1034488", "63816804", "Valsacor 80/12,5 mg Tbl. | (Valsartan/Hydrochlorothiazid)", "(null)", "(null)", "2022-04-04 19:00:00", "2020-08-21 14:40:00", "-591.1805555555038"], ["19", "1035392", "62404501", "STALEVO 75 mg/18,75 mg/200 mg Filmtabletten | (Levodopa)", "(null)", "(null)", "2019-03-13 19:00:00", "2015-07-20 12:56:00", "-1332.2527777776122"], ["20", "1037941", "64180007", "TRAZODON-neuraxpharm 100 mg Tabletten | (Trazodon)", "N06AX05", "Trazodon", "2021-12-01 19:00:00", "2021-12-03 14:35:00", "1.815972222480923"], ["21", "1038005", "62880041", "SIMVAHEXAL 20 mg Filmtabletten | (Simvastatin)", "C10AA01", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2017-08-16 21:00:00", "2017-01-25 17:58:00", "-203.1263888888061"], ["22", "1040368", "62801141", "!Urin<PERSON><PERSON>, sediment, kultur", "(null)", "(null)", "2018-04-11 19:00:00", "2016-09-28 09:37:00", "-560.3909722222015"], ["23", "1040589", "62409901", "UFH", "(null)", "(null)", "2018-04-13 22:00:00", "2015-04-21 08:54:00", "-1088.5458333333954"], ["24", "1041460", "63990726", "AMLODIPIN HEXAL 5 mg Tabletten | (Amlodipin)", "C08CA01", "Amlodipin", "2021-03-22 19:00:00", "2021-03-24 13:25:00", "1.7673611114732921"], ["25", "1042117", "63871795", "HCT HEXAL 12,5 mg Tabletten | (Hydrochlorothiazid)", "C03AA03", "Hydrochlorothiazid", "2023-04-17 19:00:00", "2020-12-19 13:04:00", "-849.2472222219221"], ["26", "1042322", "62481326", "NOVAMINSULFON ratioph.500 mg Tabletten | (Metamizol)", "(null)", "(null)", "2017-05-02 22:00:00", "2015-08-07 21:19:00", "-634.0284722219221"], ["27", "1045090", "62890001", "TAVOR 0,5 Tabletten | (Lorazepam)", "N05BA06", "Lorazepam", "2019-11-04 19:00:00", "2017-03-24 13:49:00", "-955.2159722219221"], ["28", "1045175", "62934412", "NaCl", "(null)", "(null)", "2021-02-19 22:00:00", "2017-04-05 11:00:00", "-1416.458333333023"], ["29", "1047848", "63594260", "ATORVASTATIN ratiopharm 20 mg Filmtabletten | (Atorvastatin)", "C10AA05", "Atorvas<PERSON><PERSON>", "2025-01-20 19:00:00", "2019-09-11 09:59:00", "-1958.375694444403"], ["30", "1051751", "64538253", "CLEXANE 4.000 I.E. 40mg Klinik ILO F.Sp.+Sich-Sys. | (Enoxaparin natrium)", "B01AB05", "Enoxaparin", "2024-01-10 19:00:00", "2023-03-31 10:47:00", "-285.34236111072823"], ["31", "1052626", "63794157", "RESTEX Tabletten | (Benserazid), ca. 1h vor dem Schlaf", "(null)", "(null)", "2020-06-29 19:00:00", "2020-07-01 13:34:00", "1.773611111100763"], ["32", "1052835", "62723223", "Vitamin B1 200 mg ", "(null)", "(null)", "2018-12-05 19:00:00", "2016-08-16 13:49:00", "-841.2159722219221"], ["33", "1054332", "63254828", "RIVOTRIL 0,5 mg Tabletten | (Clonazepam)", "N03AE01", "Clonazepam", "2018-06-04 19:00:00", "2018-06-06 15:12:00", "1.841666666790843"], ["34", "1054383", "64153438", "TOUJEO 300 E/ml SoloStar Inj.-Lsg.i.e.Fertigpen | (In<PERSON><PERSON> glargin)", "A10AE04", "In<PERSON>in glargin", "2021-10-27 19:00:00", "2021-10-29 13:51:00", "1.7854166668839753"], ["35", "105555", "62154137", "Tramadol (Tramal, Tramagit...)", "(null)", "(null)", "2024-01-03 19:00:00", "2014-05-05 11:46:00", "-3530.30138888862"], ["36", "1058874", "62472539", "Medikinet", "(null)", "(null)", "2019-02-20 20:00:00", "2015-09-07 14:56:00", "-1262.2111111111008"], ["37", "1068078", "64719429", "TRAZODON-neuraxpharm 100 mg Tabletten | (Trazodon hydrochlorid)", "N06AX05", "Trazodon", "2024-03-25 19:00:00", "2024-03-27 14:14:00", "1.8013888890855014"], ["38", "1068618", "63012612", "! Ruhe-EKG", "(null)", "(null)", "2021-05-28 22:00:00", "2017-07-14 12:00:00", "-1414.4166666665114"], ["39", "1070421", "62609832", "CITALOPRAM HEXAL 20 mg Filmtabletten | (Citalopram)", "(null)", "(null)", "2021-11-22 19:00:00", "2016-01-17 16:30:00", "-2136.1041666665114"], ["40", "1076916", "64114343", "ZOPICLON-ratiopharm 7,5 mg Filmtabletten | (Zopiclon)", "N05CF01", "Zopiclon", "2023-06-05 19:00:00", "2021-10-15 15:25:00", "-598.1493055555038"], ["41", "1077629", "64248885", "IBUPROFEN STADA 600 mg Zäpfchen | (Ibuprofen natrium, Ibuprofen)", "M01AE01", "Ibuprofen", "2022-08-08 19:00:00", "2022-03-11 11:08:00", "-150.32777777779847"], ["42", "1078462", "63074530", "ELEKTROLYT Inf.-Lsg. 153 | (<PERSON>, Ka, Ca, Mg)\rje nach Pat.-<PERSON><PERSON><PERSON><PERSON><PERSON>, Zieleinfuhr: 2000ml", "(null)", "(null)", "2018-09-24 19:00:00", "2017-11-02 11:07:00", "-326.3284722222015"], ["43", "1079680", "64401231", "Itulazax 12 SQBet Lyphilisat", "(null)", "(null)", "2023-02-13 19:30:00", "2023-02-15 13:30:00", "1.75"], ["44", "1082480", "4923272", "UNACID 3 g z. Infusion Trockensubst.o.Lösungsm. | (Ampicillin, Sulbactam, Natrium-Ion)", "J01CR01", "Ampicillin und Beta-Lactamase-Inhibitoren", "2019-09-25 19:00:00", "2020-03-31 23:59:59", "188.20832175947726"], ["45", "1083711", "63826523", "Crestor 20 mg ", "(null)", "(null)", "2021-06-16 19:00:00", "2020-08-19 10:00:00", "-301.375"], ["46", "1086211", "62093207", "Motilium", "(null)", "(null)", "2014-09-04 19:49:00", "2014-01-29 16:31:00", "-218.13750000018626"], ["47", "108868", "63945723", "CLEXANE 8.000 I.E. 80mg/0,8ml ILO F.Sp.+Sich-Sys. | (Enoxaparin natrium)", "B01AB05", "Enoxaparin", "2020-06-03 19:00:00", "2021-01-19 16:01:00", "229.87569444440305"], ["48", "1088762", "63001507", "PRAMIPEXOL    retard   1,05 mg Tabletten | (Pramipexol)", "(null)", "(null)", "2018-05-07 19:00:00", "2017-08-24 15:40:00", "-256.1388888885267"], ["49", "1090409", "63034267", "NOVOPULMON 200 µg Novolizer Inhalat.+Patr.10x100ED | (Budesonid)", "R03BA02", "<PERSON><PERSON><PERSON>", "2023-08-07 19:00:00", "2017-09-06 09:00:00", "-2161.4166666665114"]], "shape": {"columns": 8, "rows": 783}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PID</th>\n", "      <th>FallScheinnummer</th>\n", "      <th>Medikationsbezeichnung</th>\n", "      <th>ATCCode</th>\n", "      <th>ATCBezeichnung</th>\n", "      <th>sleep_study_date</th>\n", "      <th>medication_date</th>\n", "      <th>days_between</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1001252</td>\n", "      <td>62511138</td>\n", "      <td>Zopiclon 7,5 mg</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2021-08-02 19:00:00</td>\n", "      <td>2015-10-08 13:00:00</td>\n", "      <td>-2125.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1001783</td>\n", "      <td>4791283</td>\n", "      <td>ESCITALOPRAM 5 mg Filmtabletten | (Escitalopram)</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2020-05-14 19:00:00</td>\n", "      <td>2019-12-31 23:59:59</td>\n", "      <td>-134.791678</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1004166</td>\n", "      <td>62204022</td>\n", "      <td>Cellcept</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2017-04-26 21:00:00</td>\n", "      <td>2014-07-29 10:33:00</td>\n", "      <td>-1002.435417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1010802</td>\n", "      <td>62109848</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2024-05-06 19:00:00</td>\n", "      <td>2014-02-26 16:30:00</td>\n", "      <td>-3722.104167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1012161</td>\n", "      <td>62099452</td>\n", "      <td>Cetirizin Tbl.</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2015-12-07 20:26:00</td>\n", "      <td>2014-04-10 12:37:00</td>\n", "      <td>-606.325694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>778</th>\n", "      <td>873084</td>\n", "      <td>63107221</td>\n", "      <td>Xyrem 500mg/ml</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2018-11-05 19:00:00</td>\n", "      <td>2018-01-30 13:35:00</td>\n", "      <td>-279.225694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>779</th>\n", "      <td>875325</td>\n", "      <td>62627074</td>\n", "      <td>NATRIUMCHLORID Inf.Lsg.154 | (Natrium-Ion, Chl...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2018-06-22 22:00:00</td>\n", "      <td>2016-02-10 12:41:00</td>\n", "      <td>-863.388194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>780</th>\n", "      <td>878081</td>\n", "      <td>63766392</td>\n", "      <td>ZOPICLON HEXAL 7,5 mg Filmtabletten | (Zopiclon)</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>2020-10-21 19:00:00</td>\n", "      <td>2020-06-13 10:16:00</td>\n", "      <td>-130.363889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>781</th>\n", "      <td>880559</td>\n", "      <td>64061230</td>\n", "      <td>BISOPROLOL ratiopharm 2,5 mg Filmtabletten | (...</td>\n", "      <td>C07AB07</td>\n", "      <td>Bisoprolol</td>\n", "      <td>2021-02-01 19:00:00</td>\n", "      <td>2021-06-28 12:32:00</td>\n", "      <td>146.730556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>782</th>\n", "      <td>898817</td>\n", "      <td>63052255</td>\n", "      <td>L-THYROXIN 100 Henning Tabletten | (Levothyroxin)</td>\n", "      <td>H03AA01</td>\n", "      <td>Levothyroxin-Natrium</td>\n", "      <td>2022-07-08 22:00:00</td>\n", "      <td>2017-09-14 12:00:00</td>\n", "      <td>-1758.416667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>783 rows × 8 columns</p>\n", "</div>"], "text/plain": ["         PID FallScheinnummer  \\\n", "0    1001252         62511138   \n", "1    1001783          4791283   \n", "2    1004166         62204022   \n", "3    1010802         62109848   \n", "4    1012161         62099452   \n", "..       ...              ...   \n", "778   873084         63107221   \n", "779   875325         62627074   \n", "780   878081         63766392   \n", "781   880559         64061230   \n", "782   898817         63052255   \n", "\n", "                                Medikationsbezeichnung  ATCCode  \\\n", "0                                      Zopiclon 7,5 mg   (null)   \n", "1     ESCITALOPRAM 5 mg Filmtabletten | (Escitalopram)   (null)   \n", "2                                             Cellcept   (null)   \n", "3                                             <PERSON><PERSON><PERSON>   (null)   \n", "4                                       Cetirizin Tbl.   (null)   \n", "..                                                 ...      ...   \n", "778                                     Xyrem 500mg/ml   (null)   \n", "779  NATRIUMCHLORID Inf.Lsg.154 | (Natrium-Ion, Chl...   (null)   \n", "780   ZOPICLON HEXAL 7,5 mg Filmtabletten | (Zopiclon)   (null)   \n", "781  BISOPROLOL ratiopharm 2,5 mg Filmtabletten | (...  C07AB07   \n", "782  L-THYROXIN 100 Henning Tabletten | (Levothyroxin)  H03AA01   \n", "\n", "           ATCBezeichnung     sleep_study_date      medication_date  \\\n", "0                  (null)  2021-08-02 19:00:00  2015-10-08 13:00:00   \n", "1                  (null)  2020-05-14 19:00:00  2019-12-31 23:59:59   \n", "2                  (null)  2017-04-26 21:00:00  2014-07-29 10:33:00   \n", "3                  (null)  2024-05-06 19:00:00  2014-02-26 16:30:00   \n", "4                  (null)  2015-12-07 20:26:00  2014-04-10 12:37:00   \n", "..                    ...                  ...                  ...   \n", "778                (null)  2018-11-05 19:00:00  2018-01-30 13:35:00   \n", "779                (null)  2018-06-22 22:00:00  2016-02-10 12:41:00   \n", "780                (null)  2020-10-21 19:00:00  2020-06-13 10:16:00   \n", "781            Bisoprolol  2021-02-01 19:00:00  2021-06-28 12:32:00   \n", "782  Levothyroxin-Natrium  2022-07-08 22:00:00  2017-09-14 12:00:00   \n", "\n", "     days_between  \n", "0    -2125.250000  \n", "1     -134.791678  \n", "2    -1002.435417  \n", "3    -3722.104167  \n", "4     -606.325694  \n", "..            ...  \n", "778   -279.225694  \n", "779   -863.388194  \n", "780   -130.363889  \n", "781    146.730556  \n", "782  -1758.416667  \n", "\n", "[783 rows x 8 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["extractor.sql(\"\"\"\n", "SELECT \n", "    m.<PERSON>,\n", "    <PERSON><PERSON>,\n", "    <PERSON><PERSON>,\n", "    m.<PERSON>,\n", "    m.ATC<PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    p.<PERSON> as sleep_study_date,\n", "    f.<PERSON> as medication_date,\n", "    JULIANDAY(f.<PERSON>) - JULIANDAY(p.<PERSON>fuehrungsdatum) as days_between\n", "FROM Medikation m\n", "JOIN Prozeduren p ON m.PID = p.PID\n", "JOIN Fall f ON f.<PERSON>  = m.<PERSON>\n", "WHERE \n", "    p.OPSCode LIKE '1-790%'\n", "    AND m.Status <> 'nicht verabreicht'\n", "    AND f.<PERSON> IS NOT NULL\n", "    AND p.<PERSON>rungsdatum IS NOT NULL\n", "    AND JULIANDAY(f.<PERSON>) - JULIANDAY(p.<PERSON>rungsda<PERSON>) BETWEEN -999999 AND 365\n", "GROUP BY m.PID \n", "ORDER BY m.P<PERSON>, p.<PERSON>, f.<PERSON>\n", "\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 17, "id": "65bc10cc", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PID", "rawType": "object", "type": "string"}, {"name": "diagnosis_date", "rawType": "object", "type": "string"}, {"name": "primary_icd", "rawType": "object", "type": "string"}, {"name": "primary_text", "rawType": "object", "type": "string"}, {"name": "secondary_icd", "rawType": "object", "type": "string"}, {"name": "secondary_text", "rawType": "object", "type": "string"}, {"name": "diagnosis_type", "rawType": "object", "type": "string"}, {"name": "diagnosis_certainty", "rawType": "object", "type": "string"}, {"name": "sleep_study_date", "rawType": "object", "type": "string"}, {"name": "days_between", "rawType": "float64", "type": "float"}, {"name": "timing_category", "rawType": "object", "type": "string"}, {"name": "diagnosis_position", "rawType": "object", "type": "string"}], "ref": "94250683-375a-4ae3-b6ae-6d260a345fa3", "rows": [["0", "1004166", "2014-07-29 10:32:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2017-04-26 21:00:00", "-1002.4361111111939", "before_sleep_study", "primary"], ["1", "1005179", "2015-03-05 11:57:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2018-08-13 19:00:00", "-1257.2937499997206", "before_sleep_study", "primary"], ["2", "1005269", "2014-04-08 13:21:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2014-11-25 17:55:00", "-231.19027777807787", "before_sleep_study", "primary"], ["3", "1010589", "2014-05-21 15:25:00", "I11.00", "Hypertensive Herzkrankheit mit (kongestiver) Herzinsuffizienz: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2014-11-13 18:57:00", "-176.14722222229466", "before_sleep_study", "primary"], ["4", "1010802", "2022-02-13 16:26:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2024-05-06 19:00:00", "-813.1069444441237", "before_sleep_study", "primary"], ["5", "1015556", "2020-08-13 10:42:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2022-03-18 22:00:00", "-582.4708333332092", "before_sleep_study", "primary"], ["6", "101602", "2024-06-19 10:33:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2024-05-06 19:00:00", "43.64791666669771", "after_sleep_study", "primary"], ["7", "1016910", "2014-04-12 12:56:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2017-04-07 22:00:00", "-1091.3777777776122", "before_sleep_study", "primary"], ["8", "1021130", "2016-12-21 10:03:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2017-02-06 21:00:00", "-47.456249999813735", "before_sleep_study", "primary"], ["9", "1023509", "2017-06-28 12:41:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2017-06-26 21:00:00", "1.6534722223877907", "after_sleep_study", "primary"], ["10", "102526", "2018-12-19 20:12:00", "I10.01", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2022-06-27 19:00:00", "-1285.9499999997206", "before_sleep_study", "primary"], ["11", "1027126", "2020-07-31 09:53:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2022-06-07 19:00:00", "-676.3798611108214", "before_sleep_study", "primary"], ["12", "102910", "2014-04-02 09:37:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2015-06-22 20:00:00", "-446.43263888917863", "before_sleep_study", "primary"], ["13", "1031159", "2019-12-23 11:36:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2021-06-25 22:00:00", "-550.433333333116", "before_sleep_study", "primary"], ["14", "1034488", "2021-04-15 13:37:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2022-04-04 19:00:00", "-354.2243055552244", "before_sleep_study", "primary"], ["15", "1035392", "2015-07-17 08:18:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2019-03-13 19:00:00", "-1335.4458333333023", "before_sleep_study", "primary"], ["16", "1040368", "2018-04-13 12:56:00", "I10.01", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2018-04-11 19:00:00", "1.7472222223877907", "after_sleep_study", "primary"], ["17", "1040589", "2015-04-15 15:06:00", "I10.01", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2018-04-13 22:00:00", "-1094.2874999996275", "before_sleep_study", "primary"], ["18", "1042117", "2020-12-19 13:03:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2023-04-17 19:00:00", "-849.2479166663252", "before_sleep_study", "primary"], ["19", "1042322", "2017-08-24 14:29:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2017-08-21 21:00:00", "2.728472222108394", "after_sleep_study", "primary"], ["20", "1045175", "2014-09-09 10:18:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2021-02-19 22:00:00", "-2355.4874999998137", "before_sleep_study", "primary"], ["21", "1047848", "2020-01-28 09:59:00", "I12.90", "Hypertensive Nierenkrankheit ohne Niereninsuffizienz: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2025-01-20 19:00:00", "-1819.375694444403", "before_sleep_study", "primary"], ["22", "1066864", "2014-04-25 10:27:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2014-12-31 23:58:59", "-250.5638773147948", "before_sleep_study", "primary"], ["23", "1076916", "2021-10-15 15:24:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2023-06-05 19:00:00", "-598.1499999999069", "before_sleep_study", "primary"], ["24", "1078462", "2017-11-02 11:06:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2018-09-24 19:00:00", "-326.3291666666046", "before_sleep_study", "primary"], ["25", "1080646", "2015-04-30 13:59:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2016-03-09 21:00:00", "-314.2923611109145", "before_sleep_study", "primary"], ["26", "108868", "2018-05-25 09:59:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2020-06-03 19:00:00", "-740.375694444403", "before_sleep_study", "primary"], ["27", "1094519", "2014-12-05 09:55:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2015-11-04 21:30:00", "-334.48263888899237", "before_sleep_study", "primary"], ["28", "110031", "2018-08-02 17:29:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2018-10-17 19:00:00", "-76.06319444440305", "before_sleep_study", "primary"], ["29", "1109694", "2020-03-16 07:00:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Aufnahmediagnose", "(null)", "2020-09-09 19:00:00", "-177.5", "before_sleep_study", "primary"], ["30", "1109775", "2017-08-16 10:44:00", "I10.91", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2022-12-09 22:00:00", "-1941.469444444403", "before_sleep_study", "primary"], ["31", "111870", "2018-01-12 11:09:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2019-05-02 19:00:00", "-475.3270833333954", "before_sleep_study", "primary"], ["32", "1120928", "2014-05-14 12:06:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2015-01-30 14:21:00", "-261.09375", "before_sleep_study", "primary"], ["33", "1124252", "2014-04-28 10:19:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2015-01-31 07:29:00", "-277.8819444444962", "before_sleep_study", "primary"], ["34", "1127809", "2014-02-04 11:59:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2014-09-28 18:30:00", "-236.2715277778916", "before_sleep_study", "primary"], ["35", "1129149", "2017-08-23 10:01:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2017-05-31 21:00:00", "83.5423611109145", "after_sleep_study", "primary"], ["36", "1130017", "2014-02-20 15:27:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2014-06-03 19:00:00", "-103.14791666669771", "before_sleep_study", "primary"], ["37", "1131606", "2016-10-14 12:56:00", "I10.91", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2016-11-01 10:59:00", "-17.918750000186265", "before_sleep_study", "primary"], ["38", "1131756", "2015-05-15 13:04:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2015-05-15 13:04:00", "0.0", "same_day", "primary"], ["39", "1134694", "2015-05-23 09:36:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2019-08-05 19:00:00", "-1535.3916666666046", "before_sleep_study", "primary"], ["40", "1136967", "2022-03-11 09:07:00", "I10.91", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2022-06-01 19:00:00", "-82.41180555522442", "before_sleep_study", "primary"], ["41", "1139658", "2020-01-17 12:55:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2019-11-18 19:00:00", "59.74652777798474", "after_sleep_study", "primary"], ["42", "1139849", "2019-07-24 09:00:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Aufnahmediagnose", "(null)", "2019-10-07 19:00:00", "-75.41666666651145", "before_sleep_study", "primary"], ["43", "1141337", "2019-06-04 12:39:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2019-06-05 19:00:00", "-1.2645833333954215", "before_sleep_study", "primary"], ["44", "1149188", "2014-05-28 07:33:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2014-04-27 18:56:00", "30.52569444430992", "after_sleep_study", "primary"], ["45", "1152313", "2020-01-26 20:27:00", "I10.91", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Behandlungsdiagnose", "<PERSON><PERSON><PERSON><PERSON>", "2024-09-23 19:00:00", "-1701.9395833332092", "before_sleep_study", "primary"], ["46", "1153319", "2019-01-23 10:53:00", "I10.91", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Aufnahmediagnose", "(null)", "2023-08-14 19:00:00", "-1664.33819444431", "before_sleep_study", "primary"], ["47", "1156998", "2017-10-16 11:53:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Fachabteilung Entlassdiagnose", "(null)", "2021-10-27 19:00:00", "-1472.2965277777985", "before_sleep_study", "primary"], ["48", "1157997", "2014-10-15 09:43:00", "I10.00", "Benigne essentielle Hypertonie: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Aufnahmediagnose", "(null)", "2014-12-02 14:34:00", "-48.20208333339542", "before_sleep_study", "primary"], ["49", "1158225", "2015-11-14 09:59:00", "I10.90", "Essentielle Hypertonie, nicht näher bezeichnet: <PERSON><PERSON> einer hypertensiven Krise", "(null)", "(null)", "Entlassdiagnose", "(null)", "2022-07-15 22:00:00", "-2435.500694444403", "before_sleep_study", "primary"]], "shape": {"columns": 12, "rows": 477}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PID</th>\n", "      <th>diagnosis_date</th>\n", "      <th>primary_icd</th>\n", "      <th>primary_text</th>\n", "      <th>secondary_icd</th>\n", "      <th>secondary_text</th>\n", "      <th>diagnosis_type</th>\n", "      <th>diagnosis_certainty</th>\n", "      <th>sleep_study_date</th>\n", "      <th>days_between</th>\n", "      <th>timing_category</th>\n", "      <th>diagnosis_position</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1004166</td>\n", "      <td>2014-07-29 10:32:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Fachabteilung Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2017-04-26 21:00:00</td>\n", "      <td>-1002.436111</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1005179</td>\n", "      <td>2015-03-05 11:57:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2018-08-13 19:00:00</td>\n", "      <td>-1257.293750</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1005269</td>\n", "      <td>2014-04-08 13:21:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Behandlungsdiagnose</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2014-11-25 17:55:00</td>\n", "      <td>-231.190278</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1010589</td>\n", "      <td>2014-05-21 15:25:00</td>\n", "      <td>I11.00</td>\n", "      <td>Hypertensive Herzkrankheit mit (kongestiver) H...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2014-11-13 18:57:00</td>\n", "      <td>-176.147222</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1010802</td>\n", "      <td>2022-02-13 16:26:00</td>\n", "      <td>I10.00</td>\n", "      <td>Benigne essentielle Hypertonie: <PERSON><PERSON> ei...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2024-05-06 19:00:00</td>\n", "      <td>-813.106944</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>870133</td>\n", "      <td>2017-08-17 09:48:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2022-07-22 22:00:00</td>\n", "      <td>-1800.508333</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>473</th>\n", "      <td>871277</td>\n", "      <td>2018-02-08 10:53:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Fachabteilung Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2020-06-03 19:00:00</td>\n", "      <td>-846.338194</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>875325</td>\n", "      <td>2014-04-19 11:07:00</td>\n", "      <td>I10.90</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> Hypertonie, nicht näher bezeichnet...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Entlassdiagnose</td>\n", "      <td>(null)</td>\n", "      <td>2018-06-22 22:00:00</td>\n", "      <td>-1525.453472</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>875567</td>\n", "      <td>2015-08-28 14:15:00</td>\n", "      <td>I11.00</td>\n", "      <td>Hypertensive Herzkrankheit mit (kongestiver) H...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Behandlungsdiagnose</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2022-03-30 19:00:00</td>\n", "      <td>-2406.197917</td>\n", "      <td>before_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>879906</td>\n", "      <td>2015-10-07 15:09:00</td>\n", "      <td>I10.01</td>\n", "      <td>Benigne essentielle Hypertonie: <PERSON><PERSON> ein...</td>\n", "      <td>(null)</td>\n", "      <td>(null)</td>\n", "      <td>Aufnahmediagnose</td>\n", "      <td>(null)</td>\n", "      <td>2015-07-15 20:07:00</td>\n", "      <td>83.793056</td>\n", "      <td>after_sleep_study</td>\n", "      <td>primary</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>477 rows × 12 columns</p>\n", "</div>"], "text/plain": ["         PID       diagnosis_date primary_icd  \\\n", "0    1004166  2014-07-29 10:32:00      I10.90   \n", "1    1005179  2015-03-05 11:57:00      I10.90   \n", "2    1005269  2014-04-08 13:21:00      I10.90   \n", "3    1010589  2014-05-21 15:25:00      I11.00   \n", "4    1010802  2022-02-13 16:26:00      I10.00   \n", "..       ...                  ...         ...   \n", "472   870133  2017-08-17 09:48:00      I10.90   \n", "473   871277  2018-02-08 10:53:00      I10.90   \n", "474   875325  2014-04-19 11:07:00      I10.90   \n", "475   875567  2015-08-28 14:15:00      I11.00   \n", "476   879906  2015-10-07 15:09:00      I10.01   \n", "\n", "                                          primary_text secondary_icd  \\\n", "0    Essentielle Hypertonie, nicht näher bezeichnet...        (null)   \n", "1    E<PERSON><PERSON><PERSON> H<PERSON>ie, nicht näher bezeichnet...        (null)   \n", "2    <PERSON><PERSON><PERSON><PERSON>, nicht näher beze<PERSON>...        (null)   \n", "3    Hypertensive Herzkrankheit mit (kongestiver) H...        (null)   \n", "4    Benigne essentielle Hypertonie: <PERSON><PERSON> ei...        (null)   \n", "..                                                 ...           ...   \n", "472  <PERSON><PERSON><PERSON><PERSON>, nicht näher bezeich<PERSON>...        (null)   \n", "473  <PERSON><PERSON><PERSON><PERSON>, nicht näher bezeich<PERSON>...        (null)   \n", "474  <PERSON><PERSON><PERSON><PERSON>, nicht näher bezeich<PERSON>...        (null)   \n", "475  Hypertensive Herzkrankheit mit (kongestiver) H...        (null)   \n", "476  Benigne essentielle Hypertonie: <PERSON><PERSON> ein...        (null)   \n", "\n", "    secondary_text                 diagnosis_type diagnosis_certainty  \\\n", "0           (null)  Fachabteilung Entlassdiagnose              (null)   \n", "1           (null)                Entlassdiagnose              (null)   \n", "2           (null)            Behandlungsdiagnose           G<PERSON>t   \n", "3           (null)                Entlassdiagnose              (null)   \n", "4           (null)                Entlassdiagnose              (null)   \n", "..             ...                            ...                 ...   \n", "472         (null)                Entlassdiagnose              (null)   \n", "473         (null)  Fachabteilung Entlassdiagnose              (null)   \n", "474         (null)                Entlassdiagnose              (null)   \n", "475         (null)            Behandlungsdiagnose           Gesichert   \n", "476         (null)               Aufnahmediagnose              (null)   \n", "\n", "        sleep_study_date  days_between     timing_category diagnosis_position  \n", "0    2017-04-26 21:00:00  -1002.436111  before_sleep_study            primary  \n", "1    2018-08-13 19:00:00  -1257.293750  before_sleep_study            primary  \n", "2    2014-11-25 17:55:00   -231.190278  before_sleep_study            primary  \n", "3    2014-11-13 18:57:00   -176.147222  before_sleep_study            primary  \n", "4    2024-05-06 19:00:00   -813.106944  before_sleep_study            primary  \n", "..                   ...           ...                 ...                ...  \n", "472  2022-07-22 22:00:00  -1800.508333  before_sleep_study            primary  \n", "473  2020-06-03 19:00:00   -846.338194  before_sleep_study            primary  \n", "474  2018-06-22 22:00:00  -1525.453472  before_sleep_study            primary  \n", "475  2022-03-30 19:00:00  -2406.197917  before_sleep_study            primary  \n", "476  2015-07-15 20:07:00     83.793056   after_sleep_study            primary  \n", "\n", "[477 rows x 12 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["extractor.sql(\"\"\"\n", "SELECT DISTINCT \n", "            <PERSON><PERSON>,\n", "            <PERSON>.<PERSON> as diagnosis_date,\n", "            <PERSON><PERSON>seICD as primary_icd,\n", "            <PERSON><PERSON>Text as primary_text,\n", "            d.<PERSON>seICD as secondary_icd,\n", "            <PERSON>.<PERSON>seText as secondary_text,\n", "            d.DiagnoseTyp as diagnosis_type,\n", "            d.Diagnosesicherheit as diagnosis_certainty,\n", "            p.<PERSON> as sleep_study_date,\n", "            JULIANDAY(d.<PERSON>stellungsdatum) - JULIANDAY(p.<PERSON>fuehrungsdatum) as days_between,\n", "            CASE \n", "                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.<PERSON>rungsda<PERSON>) < 0 THEN 'before_sleep_study'\n", "                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.<PERSON>fuehrungsda<PERSON>) = 0 THEN 'same_day'\n", "                ELSE 'after_sleep_study'\n", "            END as timing_category,\n", "            CASE \n", "                WHEN d.<PERSON>seICD LIKE 'I1%' THEN 'primary'\n", "                WHEN d.<PERSON>gnoseICD LIKE 'I1%' THEN 'secondary'\n", "                ELSE 'unknown'\n", "            END as diagnosis_position\n", "        FROM Diagnosen d\n", "        JOIN Prozeduren p ON d.PID = p.PID\n", "        WHERE \n", "            (d.<PERSON>gnoseICD LIKE 'I1%' OR d.<PERSON><PERSON>gnoseICD LIKE 'I1%')\n", "            AND p.OPSCode LIKE '1-790%'\n", "            AND d.Feststellungsdatum IS NOT NULL\n", "            AND p.<PERSON>rungsdatum IS NOT NULL\n", "            AND JULIANDAY(d.<PERSON>stellungsdatum) - JULIANDAY(p.<PERSON>fuehrungsda<PERSON>) BETWEEN -99999999 AND 90\n", "        GROUP BY d.PID\n", "        ORDER BY d<PERSON>, p.<PERSON>, d.<PERSON>llungsdatum\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 13, "id": "3a549ada", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [{"ename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evalue": "Error tokenizing data. C error: Expected 14 fields in line 7, saw 15\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                               <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[13], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdatabase/200325_SOM_WP3_Lab.csv\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m;\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/miniconda/envs/sleepy/lib/python3.13/site-packages/pandas/io/parsers/readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[1;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m   1014\u001b[0m     dialect,\n\u001b[1;32m   1015\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[1;32m   1023\u001b[0m )\n\u001b[1;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/miniconda/envs/sleepy/lib/python3.13/site-packages/pandas/io/parsers/readers.py:626\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    623\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m parser\n\u001b[1;32m    625\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m parser:\n\u001b[0;32m--> 626\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mparser\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/miniconda/envs/sleepy/lib/python3.13/site-packages/pandas/io/parsers/readers.py:1923\u001b[0m, in \u001b[0;36mTextFileReader.read\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m   1916\u001b[0m nrows \u001b[38;5;241m=\u001b[39m validate_integer(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnrows\u001b[39m\u001b[38;5;124m\"\u001b[39m, nrows)\n\u001b[1;32m   1917\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1918\u001b[0m     \u001b[38;5;66;03m# error: \"ParserBase\" has no attribute \"read\"\u001b[39;00m\n\u001b[1;32m   1919\u001b[0m     (\n\u001b[1;32m   1920\u001b[0m         index,\n\u001b[1;32m   1921\u001b[0m         columns,\n\u001b[1;32m   1922\u001b[0m         col_dict,\n\u001b[0;32m-> 1923\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[attr-defined]\u001b[39;49;00m\n\u001b[1;32m   1924\u001b[0m \u001b[43m        \u001b[49m\u001b[43mnrows\u001b[49m\n\u001b[1;32m   1925\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1926\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m   1927\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/miniconda/envs/sleepy/lib/python3.13/site-packages/pandas/io/parsers/c_parser_wrapper.py:234\u001b[0m, in \u001b[0;36mCParserWrapper.read\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m    232\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    233\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlow_memory:\n\u001b[0;32m--> 234\u001b[0m         chunks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_reader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_low_memory\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    235\u001b[0m         \u001b[38;5;66;03m# destructive to chunks\u001b[39;00m\n\u001b[1;32m    236\u001b[0m         data \u001b[38;5;241m=\u001b[39m _concatenate_chunks(chunks)\n", "File \u001b[0;32mparsers.pyx:838\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader.read_low_memory\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:905\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._read_rows\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:874\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._tokenize_rows\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:891\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._check_tokenize_status\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:2061\u001b[0m, in \u001b[0;36mpandas._libs.parsers.raise_parser_error\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mParserError\u001b[0m: Error tokenizing data. C error: Expected 14 fields in line 7, saw 15\n"]}], "source": ["import pandas as pd\n", "pd.read_csv(\"database/200325_SOM_WP3_Lab.csv\", sep=\";\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4b86125", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sleepy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}