
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor

# Connect to the database
db_connector = DatabaseConnector('../patient_management.db')
conn = db_connector.get_connection()
    
# Extract data
print("Extracting data from database...")
extractor = DataExtractor(conn)

diags_grouped = extractor.sql("""
    SELECT 
        SUBSTR(icd, 1, 2) as icd_category,
        COUNT(*) as count,
        COUNT(DISTINCT icd) as unique_codes
    FROM (
        SELECT PrimaerdiagnoseICD as icd, PrimaerdiagnoseText as text
        FROM Diagnosen
        WHERE PrimaerdiagnoseICD != '(null)'
        
        UNION ALL
        
        SELECT SekundaerdiagnoseICD as icd, SekundaerdiagnoseText as text
        FROM Diagnosen
        WHERE SekundaerdiagnoseICD != '(null)'
    ) combined_diagnoses
    GROUP BY SUBSTR(icd, 1, 2)
    ORDER BY count DESC
""")

# Display the grouped results
print("ICD Codes grouped by first 2 characters:")
print(diags_grouped)

# Also create a detailed breakdown showing individual codes within each category
diags_detailed = extractor.sql("""
    SELECT 
        SUBSTR(icd, 1, 2) as icd_category,
        icd,
        text,
        COUNT(*) as count
    FROM (
        SELECT PrimaerdiagnoseICD as icd, PrimaerdiagnoseText as text
        FROM Diagnosen
        WHERE PrimaerdiagnoseICD != '(null)'
        
        UNION ALL
        
        SELECT SekundaerdiagnoseICD as icd, SekundaerdiagnoseText as text
        FROM Diagnosen
        WHERE SekundaerdiagnoseICD != '(null)'
    ) combined_diagnoses
    GROUP BY icd, text
    ORDER BY SUBSTR(icd, 1, 2), count DESC
""")

print("\nDetailed breakdown by category:")
for category in diags_grouped['icd_category'].head(10):  # Show top 10 categories
    print(f"\n=== Category {category} ===")
    category_codes = diags_detailed[diags_detailed['icd_category'] == category].head(5)
    for _, row in category_codes.iterrows():
        print(f"  {row['icd']}: {row['text']} (count: {row['count']})")

diags_grouped


# G47.31: apnoe
# Z01.7: Laboruntersuchung
# I10.90: used
# I10.00: used
# Z11: Spezielle Verfahren zur Untersuchung auf infektiöse und parasitäre Krankheiten
# U99.0!: Spezielle Verfahren zur Untersuchung auf SARS-CoV-2
# G47.8: Sonstige Schlafstörungen
# G25.81: Syndrom der unruhigen Beine [Restless-Legs-Syndrom]
# G47.39: Schlafapnoe, nicht näher bezeichnet
# E11.90: used
# F51.0: Nichtorganische Insomnie
# G47.0: Ein- und Durchschlafstörungen
diags.query("icd not in ['G47.31', 'Z01.7', 'I10.90', 'I10.00', 'Z11', 'U99.0!', 'G47.8', 'G25.81', 'G47.39', 'E11.90', 'F51.0', 'G47.0']")

diags.query("icd.str.startswith('O0', na=False)")

extractor.get_pregnant_during_sleep_study_patients()

o0_patients = extractor.get_patients_with_diagnosis_around_sleep_study("")

extractor.sql("""
SELECT 
    m.PID,
    f.Fallscheinnummer,
    m.Medikationsbezeichnung,
    m.ATCCode,
    m.ATCBezeichnung,
    p.Durchfuehrungsdatum as sleep_study_date,
    f.Entlassdatum as medication_date,
    JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between
FROM Medikation m
JOIN Prozeduren p ON m.PID = p.PID
JOIN Fall f ON f.Fallscheinnummer  = m.Fallscheinnummer
WHERE 
    p.OPSCode LIKE '1-790%'
    AND m.Status <> 'nicht verabreicht'
    AND f.Entlassdatum IS NOT NULL
    AND p.Durchfuehrungsdatum IS NOT NULL
    AND JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -999999 AND 365
GROUP BY m.PID 
ORDER BY m.PID, p.Durchfuehrungsdatum, f.Entlassdatum

""")

extractor.sql("""
SELECT DISTINCT 
            d.PID,
            d.Feststellungsdatum as diagnosis_date,
            d.PrimaerdiagnoseICD as primary_icd,
            d.PrimaerdiagnoseText as primary_text,
            d.SekundaerdiagnoseICD as secondary_icd,
            d.SekundaerdiagnoseText as secondary_text,
            d.DiagnoseTyp as diagnosis_type,
            d.Diagnosesicherheit as diagnosis_certainty,
            p.Durchfuehrungsdatum as sleep_study_date,
            JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between,
            CASE 
                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) < 0 THEN 'before_sleep_study'
                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) = 0 THEN 'same_day'
                ELSE 'after_sleep_study'
            END as timing_category,
            CASE 
                WHEN d.PrimaerdiagnoseICD LIKE 'I1%' THEN 'primary'
                WHEN d.SekundaerdiagnoseICD LIKE 'I1%' THEN 'secondary'
                ELSE 'unknown'
            END as diagnosis_position
        FROM Diagnosen d
        JOIN Prozeduren p ON d.PID = p.PID
        WHERE 
            (d.PrimaerdiagnoseICD LIKE 'I1%' OR d.SekundaerdiagnoseICD LIKE 'I1%')
            AND p.OPSCode LIKE '1-790%'
            AND d.Feststellungsdatum IS NOT NULL
            AND p.Durchfuehrungsdatum IS NOT NULL
            AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -99999999 AND 90
        GROUP BY d.PID
        ORDER BY d.PID, p.Durchfuehrungsdatum, d.Feststellungsdatum
""")

import pandas as pd
pd.read_csv("database/200325_SOM_WP3_Lab.csv", sep=";")

