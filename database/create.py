import sqlite3
import os
import glob

def create_sqlite_from_folder(folder_path, db_path):
    """
    Create an SQLite database by executing all .sql files in the given folder.

    :param folder_path: Path to the folder containing .sql files
    :param db_path: Path to the output SQLite database file
    """
    # Connect to (or create) the SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Get all .sql files in the folder, sorted for consistency
    sql_files = sorted(glob.glob(os.path.join(folder_path, "*.sql")))

    if not sql_files:
        print("No .sql files found in the folder.")
        return

    for sql_file in sql_files:
        print(f"Executing {sql_file}...")
        with open(sql_file, "r", encoding="utf-8") as f:
            sql_script = f.read()

        try:
            cursor.executescript(sql_script)
            print(f"✅ Successfully executed {sql_file}")
        except Exception as e:
            print(f"❌ Error executing {sql_file}: {e}")

    # Commit changes and close connection
    conn.commit()
    conn.close()
    print(f"\nSQLite database created at: {db_path}")


if __name__ == "__main__":
    # Example usage:
    folder_with_sql = "sql_files"   # folder containing .sql files
    output_db = "output.db"         # name of SQLite database to create
    create_sqlite_from_folder(folder_with_sql, output_db)
