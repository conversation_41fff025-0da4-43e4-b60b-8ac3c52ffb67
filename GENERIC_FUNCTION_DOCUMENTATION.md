# Generic Diagnosis Around Sleep Study Function

## Overview

The `get_patients_with_diagnosis_around_sleep_study()` function is a flexible, generic tool that allows you to find patients with any diagnosis pattern around their sleep study with configurable time windows.

## Function Signature

```python
def get_patients_with_diagnosis_around_sleep_study(self, diagnosis_pattern, days_before=30, days_after=30, 
                                                   diagnosis_field='both', diagnosis_type=None):
```

## Parameters

### Required Parameters
- **`diagnosis_pattern`** (str): SQL LIKE pattern for diagnosis codes
  - Examples: `'O09%'`, `'G47%'`, `'I1%'`, `'E10%'`
  - Use `%` as wildcard for multiple characters
  - Use `_` as wildcard for single character

### Optional Parameters
- **`days_before`** (int, default=30): Number of days before sleep study to look for diagnosis
- **`days_after`** (int, default=30): Number of days after sleep study to look for diagnosis  
- **`diagnosis_field`** (str, default='both'): Which diagnosis field to search
  - `'primary'`: Search only primary diagnosis field
  - `'secondary'`: Search only secondary diagnosis field
  - `'both'`: Search both primary and secondary fields
- **`diagnosis_type`** (str, default=None): Filter by diagnosis type
  - Examples: `'Aufnahmediagnose'`, `'Entlassdiagnose'`
  - `None`: No filter (all diagnosis types)

## Return Value

Returns a pandas DataFrame with the following columns:

### Core Information
- `PID`: Patient ID
- `diagnosis_date`: Date when diagnosis was made
- `sleep_study_date`: Date of sleep study
- `days_between`: Days between diagnosis and sleep study (negative = before, positive = after)

### Diagnosis Details
- `primary_icd`: Primary diagnosis ICD code
- `primary_text`: Primary diagnosis description
- `secondary_icd`: Secondary diagnosis ICD code  
- `secondary_text`: Secondary diagnosis description
- `diagnosis_type`: Type of diagnosis (e.g., Aufnahmediagnose, Entlassdiagnose)
- `diagnosis_certainty`: Diagnosis certainty level

### Derived Fields
- `timing_category`: Categorizes timing relative to sleep study
  - `'before_sleep_study'`: Diagnosis made before sleep study
  - `'same_day'`: Diagnosis made on same day as sleep study
  - `'after_sleep_study'`: Diagnosis made after sleep study
- `diagnosis_position`: Whether matching diagnosis was primary or secondary

## Usage Examples

### Basic Usage

```python
from osa_analysis.data.queries import DataExtractor
import sqlite3

conn = sqlite3.connect("patient_management.db")
extractor = DataExtractor(conn)

# Find pregnancy diagnoses within ±30 days of sleep study
pregnancy_patients = extractor.get_patients_with_diagnosis_around_sleep_study(
    diagnosis_pattern="O09%"
)
```

### Advanced Usage Examples

#### 1. Sleep Disorders with Extended Window
```python
# Find sleep disorder diagnoses within ±90 days
sleep_disorders = extractor.get_patients_with_diagnosis_around_sleep_study(
    diagnosis_pattern="G47%",
    days_before=90,
    days_after=90
)
```

#### 2. Hypertension - Primary Diagnoses Only
```python
# Find hypertension in primary diagnosis field only, ±180 days
hypertension = extractor.get_patients_with_diagnosis_around_sleep_study(
    diagnosis_pattern="I1%",
    days_before=180,
    days_after=180,
    diagnosis_field="primary"
)
```

#### 3. Admission Diagnoses with Specific Pattern
```python
# Find any O-codes in admission diagnoses within ±14 days
admission_o_codes = extractor.get_patients_with_diagnosis_around_sleep_study(
    diagnosis_pattern="O%",
    days_before=14,
    days_after=14,
    diagnosis_type="Aufnahmediagnose"
)
```

#### 4. Asymmetric Time Windows
```python
# Look far back but only short time forward
diabetes_patients = extractor.get_patients_with_diagnosis_around_sleep_study(
    diagnosis_pattern="E1%",
    days_before=365,  # 1 year before
    days_after=30     # 1 month after
)
```

## Common Diagnosis Patterns

### Pregnancy and Obstetrics
- `O%`: All pregnancy, childbirth, and puerperium codes
- `O09%`: Supervision of high risk pregnancy  
- `O0%`: Pregnancy with abortive outcome
- `O10%`: Pre-existing hypertension complicating pregnancy

### Sleep Disorders
- `G47%`: All sleep disorders
- `G47.3%`: Sleep apnea
- `G47.31%`: Primary central sleep apnea

### Cardiovascular
- `I%`: All diseases of circulatory system
- `I1%`: Hypertensive diseases (I10-I15)
- `I2%`: Ischemic heart diseases (I20-I25)

### Metabolic
- `E1%`: Diabetes mellitus (E10-E14)
- `E66%`: Overweight and obesity

### Respiratory
- `J%`: All diseases of respiratory system
- `J44%`: Other chronic obstructive pulmonary disease

## Analysis Features

### Time Window Flexibility
- **Symmetric windows**: Same number of days before and after (±30 days)
- **Asymmetric windows**: Different periods before/after sleep study
- **One-sided analysis**: Set one window to 0 for unidirectional analysis

### Diagnosis Field Targeting
- Search specific diagnosis fields for more precise results
- Useful when you know the diagnosis hierarchy

### Diagnosis Type Filtering
- Focus on specific care stages (admission vs discharge diagnoses)
- Understand diagnostic timing in care pathway

## Clinical Applications

### Research Questions
1. **Comorbidity Analysis**: What conditions are diagnosed around sleep studies?
2. **Care Pathway Analysis**: How do diagnoses change from admission to discharge?
3. **Temporal Patterns**: When are related conditions typically diagnosed relative to sleep studies?
4. **Screening Effectiveness**: Are relevant conditions being identified during sleep study episodes?

### Quality Improvement
1. **Diagnostic Completeness**: Ensure related conditions are documented
2. **Care Coordination**: Identify opportunities for integrated care
3. **Timing Optimization**: Understand optimal timing for related assessments

## Performance Considerations

### Query Optimization
- More specific patterns (e.g., `G47.31%`) are faster than broad patterns (e.g., `G%`)
- Smaller time windows reduce computation time
- Specifying diagnosis_field reduces search space

### Memory Usage
- Large time windows may return many records
- Consider filtering or pagination for very broad searches

## Comparison with Specialized Functions

| Feature | Generic Function | Pregnancy-Specific Function |
|---------|------------------|------------------------------|
| Flexibility | High - any diagnosis pattern | Low - pregnancy codes only |
| Time Windows | Configurable before/after | Pregnancy-stage specific |
| Complexity | Simple uniform logic | Complex stage-based logic |
| Performance | Good for targeted searches | Optimized for pregnancy use |

## Best Practices

### 1. Start Narrow, Expand Gradually
```python
# Start with specific pattern and narrow time window
result = extractor.get_patients_with_diagnosis_around_sleep_study("O09.1%", 14, 14)

# If needed, expand pattern or time window
result = extractor.get_patients_with_diagnosis_around_sleep_study("O09%", 30, 30)
```

### 2. Use Appropriate Time Windows
- **Acute conditions**: 7-14 days
- **Chronic conditions**: 30-90 days  
- **Screening/prevention**: 180-365 days

### 3. Consider Care Context
- Use `diagnosis_type` to focus on relevant care stages
- Use `diagnosis_field` when you know diagnostic hierarchy

### 4. Validate Results
- Check `timing_category` distribution
- Review sample cases manually
- Compare with clinical expectations

## Error Handling

The function handles common issues gracefully:
- Invalid diagnosis patterns return empty results
- Missing date fields are excluded automatically
- SQL injection is prevented through parameterized queries

## Integration with Analysis Workflows

The generic function integrates well with:
- Visualization tools (see `generic_diagnosis_analysis.py`)
- Statistical analysis pipelines
- Export workflows (CSV, Excel)
- Reporting systems
