#!/usr/bin/env python3
"""
Example Usage of Timing-Constrained Data Extraction
==================================================

This script demonstrates how to use the new timing parameters for diagnosis and medication
extraction in relation to sleep studies.
"""

import sqlite3
from osa_analysis.data.queries import DataExtractor

def main():
    # Connect to database
    conn = sqlite3.connect('patient_management.db')
    extractor = DataExtractor(conn)
    
    print("=== Data Extraction with Timing Constraints ===\n")
    
    # Example 1: Extract hypertension patients with strict timing
    print("1. Hypertension patients (strict timing - no limit before, 1 year after sleep study):")
    hypertension_strict = extractor.get_hypertension_patients(
        days_before=None,      # No limit before sleep study
        days_after=365         # 1 year after sleep study
    )
    print(f"   Found {len(hypertension_strict)} patients")
    
    # Example 2: Extract hypertension patients with moderate timing
    print("\n2. Hypertension patients (moderate timing - 6 months before, 1 year after):")
    hypertension_moderate = extractor.get_hypertension_patients(
        days_before=180,       # 6 months before sleep study
        days_after=365         # 1 year after sleep study
    )
    print(f"   Found {len(hypertension_moderate)} patients")
    
    # Example 3: Extract antihypertensive medication patients with timing
    print("\n3. Patients on antihypertensives (within 1 year after sleep study):")
    antihypertensive_timed = extractor.get_patients_on_antihypertensives(
        days_before=None,      # No limit before
        days_after=365         # 1 year after
    )
    print(f"   Found {len(antihypertensive_timed)} patients")
    
    print(f"   All hypertension patients: {len(hypertension_all)}")
    print(f"   All antihypertensive patients: {len(antihypertensive_all)}")
    
    # Example 5: Extract all data with strict timing constraints
    print("\n5. Extract all data with strict timing constraints:")
    df_strict, _, _, _ = extractor.extract_all_data_with_timing()
    print(f"   Total patients with sleep studies: {len(df_strict)}")
    print(f"   Patients with hypertension (timed): {df_strict['has_diagnosed_hypertension'].sum()}")
    print(f"   Patients on antihypertensives (timed): {df_strict['on_antihypertensives'].sum()}")
    print(f"   Patients with diabetes (timed): {df_strict['has_diabetes'].sum()}")
    print(f"   Patients with cardiovascular disease (timed): {df_strict['has_cardiovascular'].sum()}")
    
    # Example 6: Extract all data with moderate timing constraints
    print("\n6. Extract all data with moderate timing constraints:")
    df_moderate, _, _, _ = extractor.extract_all_data_with_moderate_timing()
    print(f"   Total patients with sleep studies: {len(df_moderate)}")
    print(f"   Patients with hypertension (timed): {df_moderate['has_diagnosed_hypertension'].sum()}")
    print(f"   Patients on antihypertensives (timed): {df_moderate['on_antihypertensives'].sum()}")
    
    # Example 7: Custom timing parameters
    print("\n7. Custom timing parameters (1 month before, 2 years after):")
    df_custom, _, _, _ = extractor.extract_all_data(
        use_sleep_study_timing=True,
        days_before_sleep_study=30,    # 1 month before
        days_after_sleep_study=730     # 2 years after
    )
    print(f"   Total patients with sleep studies: {len(df_custom)}")
    print(f"   Patients with hypertension (custom timing): {df_custom['has_diagnosed_hypertension'].sum()}")
    
    # Example 8: Show timing details for specific patients
    print("\n8. Detailed timing analysis for hypertension patients:")
    hypertension_details = extractor.get_patients_with_diagnosis_around_sleep_study(
        diagnosis_pattern='I1%',
        days_before=365,
        days_after=365
    )
    # Filter for actual hypertension codes
    mask = (
        (hypertension_details['primary_icd'].notna() & 
         hypertension_details['primary_icd'].str[:3].between('I10', 'I15')) |
        (hypertension_details['secondary_icd'].notna() & 
         hypertension_details['secondary_icd'].str[:3].between('I10', 'I15'))
    )
    hypertension_details = hypertension_details[mask]
    
    if not hypertension_details.empty:
        print(f"   Found {len(hypertension_details)} hypertension diagnoses around sleep studies")
        print("   Sample timing details:")
        sample = hypertension_details.head(5)[['PID', 'primary_icd', 'days_between', 'timing_category']]
        print(sample.to_string(index=False))
    
    conn.close()
    print("\n=== Analysis Complete ===")

if __name__ == "__main__":
    main()
