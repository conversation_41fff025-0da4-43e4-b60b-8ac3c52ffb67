"""
Database Connector Module
-----------------------
Handles database connections.
"""

import sqlite3

class DatabaseConnector:
    """Handles database connections."""
    
    def __init__(self, db_path):
        """
        Initialize the database connector.
        
        Args:
            db_path (str): Path to the SQLite database file
        """
        self.db_path = db_path
        self.conn = None
        self._connect()
    
    def _connect(self):
        """Establish a connection to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"Connected to database: {self.db_path}")
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            raise
    
    def get_connection(self):
        """
        Get the database connection.
        
        Returns:
            Connection: SQLite database connection
        """
        return self.conn
    
    def close_connection(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            print("Database connection closed")
