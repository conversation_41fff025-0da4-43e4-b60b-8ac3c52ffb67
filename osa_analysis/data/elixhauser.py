"""
Elixhauser Comorbidity Index Module
----------------------------------
Maps ICD-10 codes to Elixhauser comorbidity categories.
"""

import pandas as pd
import re

class ElixhauserMapper:
    """Maps ICD-10 codes to Elixhauser comorbidity categories."""
    
    def __init__(self):
        """Initialize the Elixhauser comorbidity mappings."""
        self.comorbidity_mappings = self._get_elixhauser_mappings()
    
    def _get_elixhauser_mappings(self):
        """
        Get ICD-10 code mappings for Elixhauser comorbidities.
        
        Returns:
            dict: Dictionary mapping comorbidity names to ICD-10 code patterns
        """
        return {
            'congestive_heart_failure': [
                'I09.9', 'I11.0', 'I13.0', 'I13.2', 'I25.5', 'I42.0', 'I42.5', 'I42.6', 'I42.7', 'I42.8', 'I42.9',
                'I43', 'I50', 'P29.0'
            ],
            'cardiac_arrhythmias': [
                'I44.1', 'I44.2', 'I44.3', 'I45.6', 'I45.9', 'I47', 'I48', 'I49', 'R00.0', 'R00.1', 'R00.8',
                'T82.1', 'Z45.0', 'Z95.0'
            ],
            'valvular_disease': [
                'A52.0', 'I05', 'I06', 'I07', 'I08', 'I09.1', 'I09.8', 'I34', 'I35', 'I36', 'I37', 'I38', 'I39',
                'Q23.0', 'Q23.1', 'Q23.2', 'Q23.3', 'Z95.2', 'Z95.3', 'Z95.4'
            ],
            'pulmonary_circulation_disorders': [
                'I26', 'I27', 'I28.0', 'I28.8', 'I28.9'
            ],
            'peripheral_vascular_disorders': [
                'I70', 'I71', 'I73.1', 'I73.8', 'I73.9', 'I77.1', 'I79.0', 'I79.2', 'K55.1', 'K55.8', 'K55.9',
                'Z95.8', 'Z95.9'
            ],
            'hypertension_uncomplicated': [
                'I10'
            ],
            'hypertension_complicated': [
                'I11', 'I12', 'I13', 'I15'
            ],
            'paralysis': [
                'G04.1', 'G11.4', 'G80.1', 'G80.2', 'G81', 'G82', 'G83'
            ],
            'other_neurological_disorders': [
                'G10', 'G11', 'G12', 'G13', 'G20', 'G21', 'G22', 'G25.4', 'G25.5', 'G31.2', 'G31.8', 'G31.9',
                'G32', 'G35', 'G36', 'G37', 'G40', 'G41', 'G93.1', 'G93.4', 'R47.0', 'R56'
            ],
            'chronic_pulmonary_disease': [
                'I27.8', 'I27.9', 'J40', 'J41', 'J42', 'J43', 'J44', 'J45', 'J46', 'J47', 'J60', 'J61', 'J62',
                'J63', 'J64', 'J65', 'J66', 'J67', 'J68.4', 'J70.1', 'J70.3'
            ],
            'diabetes_uncomplicated': [
                'E10.0', 'E10.1', 'E10.6', 'E10.8', 'E10.9', 'E11.0', 'E11.1', 'E11.6', 'E11.8', 'E11.9',
                'E12.0', 'E12.1', 'E12.6', 'E12.8', 'E12.9', 'E13.0', 'E13.1', 'E13.6', 'E13.8', 'E13.9',
                'E14.0', 'E14.1', 'E14.6', 'E14.8', 'E14.9'
            ],
            'diabetes_complicated': [
                'E10.2', 'E10.3', 'E10.4', 'E10.5', 'E10.7', 'E11.2', 'E11.3', 'E11.4', 'E11.5', 'E11.7',
                'E12.2', 'E12.3', 'E12.4', 'E12.5', 'E12.7', 'E13.2', 'E13.3', 'E13.4', 'E13.5', 'E13.7',
                'E14.2', 'E14.3', 'E14.4', 'E14.5', 'E14.7'
            ],
            'hypothyroidism': [
                'E00', 'E01', 'E02', 'E03', 'E89.0'
            ],
            'renal_failure': [
                'I12.0', 'I13.1', 'N18', 'N19', 'N25.0', 'Z49.0', 'Z49.1', 'Z49.2', 'Z94.0', 'Z99.2'
            ],
            'liver_disease': [
                'B18', 'I85.0', 'I85.9', 'I86.4', 'I98.2', 'K70', 'K71.1', 'K71.3', 'K71.4', 'K71.5', 'K71.7',
                'K72.1', 'K72.9', 'K73', 'K74', 'K76.0', 'K76.2', 'K76.3', 'K76.4', 'K76.5', 'K76.6', 'K76.7',
                'K76.8', 'K76.9', 'Z94.4'
            ],
            'peptic_ulcer_disease': [
                'K25', 'K26', 'K27', 'K28'
            ],
            'aids_hiv': [
                'B20', 'B21', 'B22', 'B24'
            ],
            'lymphoma': [
                'C81', 'C82', 'C83', 'C84', 'C85', 'C88', 'C96', 'C90.0', 'C90.2'
            ],
            'metastatic_cancer': [
                'C77', 'C78', 'C79', 'C80'
            ],
            'solid_tumor_without_metastasis': [
                'C00', 'C01', 'C02', 'C03', 'C04', 'C05', 'C06', 'C07', 'C08', 'C09', 'C10', 'C11', 'C12', 'C13',
                'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26',
                'C30', 'C31', 'C32', 'C33', 'C34', 'C37', 'C38', 'C39', 'C40', 'C41', 'C43', 'C45', 'C46',
                'C47', 'C48', 'C49', 'C50', 'C51', 'C52', 'C53', 'C54', 'C55', 'C56', 'C57', 'C58', 'C60',
                'C61', 'C62', 'C63', 'C64', 'C65', 'C66', 'C67', 'C68', 'C69', 'C70', 'C71', 'C72', 'C73',
                'C74', 'C75', 'C76', 'C97'
            ],
            'rheumatoid_arthritis': [
                'L94.0', 'L94.1', 'L94.3', 'M05', 'M06', 'M31.5', 'M32', 'M33', 'M34', 'M35.1', 'M35.3', 'M36.0'
            ],
            'coagulopathy': [
                'D65', 'D66', 'D67', 'D68', 'D69.1', 'D69.3', 'D69.4', 'D69.5', 'D69.6'
            ],
            'obesity': [
                'E66'
            ],
            'weight_loss': [
                'E40', 'E41', 'E42', 'E43', 'E44', 'E45', 'E46', 'R63.4', 'R64'
            ],
            'fluid_electrolyte_disorders': [
                'E22.2', 'E86', 'E87'
            ],
            'blood_loss_anemia': [
                'D50.0'
            ],
            'deficiency_anemia': [
                'D50.8', 'D50.9', 'D51', 'D52', 'D53'
            ],
            'alcohol_abuse': [
                'F10', 'E52', 'G62.1', 'I42.6', 'K29.2', 'K70.0', 'K70.3', 'K70.9', 'T51', 'Z50.2', 'Z71.4', 'Z72.1'
            ],
            'drug_abuse': [
                'F11', 'F12', 'F13', 'F14', 'F15', 'F16', 'F18', 'F19', 'Z71.5', 'Z72.2'
            ],
            'psychoses': [
                'F20', 'F22', 'F23', 'F24', 'F25', 'F28', 'F29', 'F30.2', 'F31.2', 'F31.5'
            ],
            'depression': [
                'F20.4', 'F31.3', 'F31.4', 'F31.5', 'F32', 'F33', 'F34.1', 'F41.2', 'F43.2'
            ]
        }
    
    def _match_icd_code(self, icd_code, pattern_list):
        """
        Check if an ICD code matches any pattern in the list.
        
        Args:
            icd_code (str): ICD-10 code to check
            pattern_list (list): List of ICD-10 code patterns
            
        Returns:
            bool: True if code matches any pattern
        """
        if pd.isna(icd_code) or not icd_code:
            return False
            
        # Clean the ICD code (remove dots, spaces, convert to uppercase)
        clean_code = str(icd_code).replace('.', '').replace(' ', '').upper()
        
        for pattern in pattern_list:
            clean_pattern = pattern.replace('.', '').replace(' ', '').upper()
            
            # Exact match
            if clean_code == clean_pattern:
                return True
            
            # Pattern matching for ranges (e.g., I10 matches I10.x)
            if clean_code.startswith(clean_pattern):
                return True
                
        return False
    
    def map_patient_comorbidities(self, patient_diagnoses_df):
        """
        Map patient diagnoses to Elixhauser comorbidities.
        
        Args:
            patient_diagnoses_df (DataFrame): DataFrame with columns ['PID', 'primary_icd', 'secondary_icd']
            
        Returns:
            DataFrame: DataFrame with PID and binary columns for each Elixhauser comorbidity
        """
        # Get unique patients
        patients = patient_diagnoses_df['PID'].unique()
        
        # Initialize result dataframe
        result_df = pd.DataFrame({'PID': patients})
        
        # Initialize all comorbidity columns to 0
        for comorbidity in self.comorbidity_mappings.keys():
            result_df[f'elixhauser_{comorbidity}'] = 0
        
        # Process each patient
        for pid in patients:
            patient_data = patient_diagnoses_df[patient_diagnoses_df['PID'] == pid]
            
            # Get all ICD codes for this patient
            all_codes = []
            if 'primary_icd' in patient_data.columns:
                all_codes.extend(patient_data['primary_icd'].dropna().tolist())
            if 'secondary_icd' in patient_data.columns:
                all_codes.extend(patient_data['secondary_icd'].dropna().tolist())
            
            # Check each comorbidity
            for comorbidity, icd_patterns in self.comorbidity_mappings.items():
                has_comorbidity = any(self._match_icd_code(code, icd_patterns) for code in all_codes)
                result_df.loc[result_df['PID'] == pid, f'elixhauser_{comorbidity}'] = int(has_comorbidity)
        
        return result_df
    
    def get_comorbidity_summary(self, comorbidity_df):
        """
        Get summary statistics for Elixhauser comorbidities.

        Args:
            comorbidity_df (DataFrame): DataFrame with Elixhauser comorbidity columns

        Returns:
            DataFrame: Summary statistics
        """
        elixhauser_cols = [col for col in comorbidity_df.columns if col.startswith('elixhauser_')]

        summary = []
        for col in elixhauser_cols:
            comorbidity_name = col.replace('elixhauser_', '').replace('_', ' ').title()
            count = comorbidity_df[col].sum()
            percentage = (count / len(comorbidity_df)) * 100
            summary.append({
                'Comorbidity': comorbidity_name,
                'Count': count,
                'Percentage': f"{percentage:.1f}%"
            })

        return pd.DataFrame(summary).sort_values('Count', ascending=False)

    def analyze_icd_code_coverage(self, diagnoses_df):
        """
        Analyze ICD code coverage for each Elixhauser category.

        Args:
            diagnoses_df (DataFrame): DataFrame with diagnosis data containing ICD codes

        Returns:
            DataFrame: Analysis of ICD code coverage by category
        """
        # Get all unique ICD codes from the dataset
        all_codes = set()
        if 'primary_icd' in diagnoses_df.columns:
            all_codes.update(diagnoses_df['primary_icd'].dropna().tolist())
        if 'secondary_icd' in diagnoses_df.columns:
            all_codes.update(diagnoses_df['secondary_icd'].dropna().tolist())

        coverage_analysis = []

        for comorbidity, icd_patterns in self.comorbidity_mappings.items():
            # Count total ICD patterns defined for this category
            total_patterns = len(icd_patterns)

            # Find which patterns are actually matched in the dataset
            matched_patterns = set()
            matched_codes = set()

            for code in all_codes:
                if self._match_icd_code(code, icd_patterns):
                    matched_codes.add(code)
                    # Find which pattern(s) this code matches
                    for pattern in icd_patterns:
                        if self._match_icd_code(code, [pattern]):
                            matched_patterns.add(pattern)

            # Count patients with this comorbidity
            patient_count = 0
            if not diagnoses_df.empty:
                temp_df = self.map_patient_comorbidities(diagnoses_df)
                if f'elixhauser_{comorbidity}' in temp_df.columns:
                    patient_count = temp_df[f'elixhauser_{comorbidity}'].sum()

            coverage_analysis.append({
                'Comorbidity': comorbidity.replace('_', ' ').title(),
                'Total_ICD_Patterns': total_patterns,
                'Matched_Patterns': len(matched_patterns),
                'Unique_Codes_Found': len(matched_codes),
                'Patients_Affected': patient_count,
                'Pattern_Coverage_Pct': (len(matched_patterns) / total_patterns * 100) if total_patterns > 0 else 0,
                'Matched_ICD_Codes': ', '.join(sorted(matched_codes)[:10]) + ('...' if len(matched_codes) > 10 else ''),
                'Sample_Patterns': ', '.join(icd_patterns[:5]) + ('...' if len(icd_patterns) > 5 else '')
            })

        return pd.DataFrame(coverage_analysis).sort_values('Patients_Affected', ascending=False)

    def get_detailed_icd_mapping(self):
        """
        Get detailed mapping of all ICD codes for each category.

        Returns:
            dict: Dictionary with category names as keys and ICD code lists as values
        """
        detailed_mapping = {}
        for comorbidity, icd_patterns in self.comorbidity_mappings.items():
            readable_name = comorbidity.replace('_', ' ').title()
            detailed_mapping[readable_name] = {
                'icd_codes': icd_patterns,
                'total_codes': len(icd_patterns),
                'category_key': comorbidity
            }
        return detailed_mapping
