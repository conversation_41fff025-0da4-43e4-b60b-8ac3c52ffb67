"""
Data Extraction Module
--------------------
Extracts data from the database.
"""

import pandas as pd
from .elixhauser import ElixhauserMapper

class DataExtractor:
    """Extracts data from the database."""
    
    def __init__(self, conn):
        """
        Initialize the data extractor.

        Args:
            conn: SQLite database connection
        """
        self.conn = conn
        self.elixhauser_mapper = ElixhauserMapper()
    
    def get_suspected_osa_patients(self):
        """
        Get patients with suspected Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d1.PID 
        FROM Diagnosen d1
        WHERE 
        d1.DiagnoseTyp IN ('Aufnahmediagnose') 
        and (d1.PrimaerdiagnoseICD LIKE 'G47.31%' OR d1.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_extern_suspected_osa_patients(self):
        """
        Get patients with suspected Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        WHERE d.Diagnosesicherheit = 'Verdacht auf' and
        (d.PrimaerdiagnoseICD LIKE 'G47.31%' OR d.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_unsuspected_osa_patients(self):
        """
        Get patients with Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d1.PID 
        FROM Diagnosen d1
        LEFT JOIN Diagnosen d2 ON d1.PID = d2.PID
        WHERE 
        d1.DiagnoseTyp IN ('Aufnahmediagnose') 
        and        (d1.PrimaerdiagnoseICD not LIKE 'G47.31%' AND d1.SekundaerdiagnoseICD not LIKE 'G47.31%')
        and        (d2.PrimaerdiagnoseICD LIKE 'G47.31%' OR d2.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_osa_patients(self):
        """
        Get patients with Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        
        WHERE
        d.DiagnoseTyp IN ('Entlassdiagnose') AND
        (d.PrimaerdiagnoseICD LIKE 'G47.31%' OR d.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_sleep_study_patients(self):
        """
        Get patients who underwent sleep study (OPS 1-790) with date.
        
        Returns:
            DataFrame: Patient IDs with sleep study dates
        """
        query = """
        SELECT p.PID, MAX(p.Durchfuehrungsdatum) as sleep_study_date
        FROM Prozeduren p 
        WHERE p.OPSCode LIKE '1-790%'
        GROUP BY p.PID
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_pregnant_during_sleep_study_patients(self, days_window=35):
        """
        Get patients who were pregnant during their sleep study based on pregnancy duration codes (O09) 
        and abortion codes (O02). Uses pregnancy-specific time windows where sleep study should be 
        within reasonable timeframe considering pregnancy stage.
        
        For O09 codes (pregnancy duration):
        - 5-13 weeks: sleep study within 5 weeks (35 days) before diagnosis
        - 14-19 weeks: sleep study within days_window before diagnosis  
        - 20-25 weeks: sleep study within days_window before diagnosis
        - 26-33 weeks: sleep study within days_window before diagnosis
        - 34-36 weeks: sleep study within days_window before diagnosis
        - 37-41 weeks: sleep study within days_window before diagnosis
        
        For O02 codes (abortion): sleep study within days_window of diagnosis
        
        Args:
            days_window (int): Number of days before pregnancy diagnosis to look for sleep study (default: 35)
        
        Returns:
            DataFrame: Patient IDs with pregnancy diagnosis, sleep study date, and pregnancy details
        """
        query = """
        SELECT DISTINCT 
            d.PID,
            d.Feststellungsdatum as pregnancy_diagnosis_date,
            d.PrimaerdiagnoseICD as primary_icd,
            d.PrimaerdiagnoseText as primary_text,
            d.SekundaerdiagnoseICD as secondary_icd,
            d.SekundaerdiagnoseText as secondary_text,
            p.Durchfuehrungsdatum as sleep_study_date,
            JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between,
            CASE 
                WHEN d.PrimaerdiagnoseICD LIKE 'O09%' THEN 'pregnancy_duration'
                WHEN d.PrimaerdiagnoseICD LIKE 'O02%' THEN 'abortion'
                WHEN d.SekundaerdiagnoseICD LIKE 'O09%' THEN 'pregnancy_duration'
                WHEN d.SekundaerdiagnoseICD LIKE 'O02%' THEN 'abortion'
                ELSE 'other'
            END as pregnancy_category,
            CASE 
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.1%' OR d.SekundaerdiagnoseICD LIKE 'O09.1%' THEN '5-13 weeks'
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.2%' OR d.SekundaerdiagnoseICD LIKE 'O09.2%' THEN '14-19 weeks'
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.3%' OR d.SekundaerdiagnoseICD LIKE 'O09.3%' THEN '20-25 weeks'
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.4%' OR d.SekundaerdiagnoseICD LIKE 'O09.4%' THEN '26-33 weeks'
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.5%' OR d.SekundaerdiagnoseICD LIKE 'O09.5%' THEN '34-36 weeks'
                WHEN d.PrimaerdiagnoseICD LIKE 'O09.6%' OR d.SekundaerdiagnoseICD LIKE 'O09.6%' THEN '37-41 weeks'
                ELSE 'unknown'
            END as pregnancy_week_range
        FROM Diagnosen d
        JOIN Prozeduren p ON d.PID = p.PID
        WHERE 
            (d.PrimaerdiagnoseICD LIKE 'O09%' OR d.SekundaerdiagnoseICD LIKE 'O09%' OR 
             d.PrimaerdiagnoseICD LIKE 'O02%' OR d.SekundaerdiagnoseICD LIKE 'O02%')
            AND p.OPSCode LIKE '1-790%'
            AND d.Feststellungsdatum IS NOT NULL
            AND p.Durchfuehrungsdatum IS NOT NULL
            AND (
                -- For O09.1 (5-13 weeks): sleep study within 35 days before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.1%' OR d.SekundaerdiagnoseICD LIKE 'O09.1%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND 35)
                OR
                -- For O09.2 (14-19 weeks): sleep study within days_window before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.2%' OR d.SekundaerdiagnoseICD LIKE 'O09.2%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND ?)
                OR
                -- For O09.3 (20-25 weeks): sleep study within days_window before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.3%' OR d.SekundaerdiagnoseICD LIKE 'O09.3%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND ?)
                OR
                -- For O09.4 (26-33 weeks): sleep study within days_window before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.4%' OR d.SekundaerdiagnoseICD LIKE 'O09.4%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND ?)
                OR
                -- For O09.5 (34-36 weeks): sleep study within days_window before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.5%' OR d.SekundaerdiagnoseICD LIKE 'O09.5%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND ?)
                OR
                -- For O09.6 (37-41 weeks): sleep study within days_window before diagnosis
                ((d.PrimaerdiagnoseICD LIKE 'O09.6%' OR d.SekundaerdiagnoseICD LIKE 'O09.6%') 
                 AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN 0 AND ?)
                OR
                -- For O02 (abortion): sleep study within days_window of diagnosis (before or after)
                ((d.PrimaerdiagnoseICD LIKE 'O02%' OR d.SekundaerdiagnoseICD LIKE 'O02%') 
                 AND ABS(JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum)) <= ?)
            )
        ORDER BY d.PID, p.Durchfuehrungsdatum, d.Feststellungsdatum
        """
        return pd.read_sql_query(query, self.conn, params=(days_window, days_window, days_window, days_window, days_window, days_window))
    
    def get_patient_demographics(self):
        """
        Get patient demographics including age and sex.
        
        Returns:
            DataFrame: Patient demographics
        """
        query = """
        SELECT 
            p.PID,
            p.AdministrativesGeschlecht as Sex,
            p.Geburtsdatum
        FROM Patient p
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_hypertension_patients(self, days_before=None, days_after=365):
        """
        Get patients with diagnosed hypertension (ICD I10-I15).
        
        Args:
            days_before (int): Number of days before sleep study to look for diagnosis 
                             (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis 
                            (default: 365 - 1 year)
        
        Returns:
            DataFrame: Patient IDs with hypertension
        """
        # Use the generic function to get hypertension around sleep study
        df = self.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern='I1%',
            days_before=days_before if days_before is not None else 999999,  # Large number if no limit
            days_after=days_after
        )
        # Filter for hypertension codes I10-I15
        mask = (
            (df['primary_icd'].notna() & df['primary_icd'].str[:3].between('I10', 'I15')) |
            (df['secondary_icd'].notna() & df['secondary_icd'].str[:3].between('I10', 'I15'))
        )
        df = df[mask]
        return df[['PID']].drop_duplicates()
    
    def get_patients_on_antihypertensives(self, days_before=None, days_after=365):
        """
        Get patients on antihypertensive medications based on ATC codes.
        
        Args:
            days_before (int): Number of days before sleep study to look for medication 
                             (default: None - no limit)
            days_after (int): Number of days after sleep study to look for medication 
                            (default: 365 - 1 year)
        
        Returns:
            DataFrame: Patient IDs on antihypertensives
        """
        # ATC codes for antihypertensive medications
        atc_codes = [
            'C02%',  # Antihypertensiva
            'C03%',  # Diuretika
            'C07%',  # Beta-Adrenorezeptorantagonisten
            'C08%',  # Calciumkanalblocker
            'C09%'   # Mittel mit Wirkung auf das Renin-Angiotensin-System
        ]
        
        # Build the query with timing constraints around sleep study
        atc_conditions = " OR ".join([f"m.ATCCode LIKE '{code}'" for code in atc_codes])
        
        # Set days_before to large number if not specified
        before_days = days_before if days_before is not None else 999999
        
        query = f"""
        SELECT DISTINCT 
            m.PID,
            m.Medikationsbezeichnung,
            m.ATCCode,
            m.ATCBezeichnung,
            p.Durchfuehrungsdatum as sleep_study_date,
            f.Entlassdatum as medication_date,
            JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between
        FROM Medikation m
        JOIN Prozeduren p ON m.PID = p.PID
        JOIN Fall f ON f.Fallscheinnummer  = m.Fallscheinnummer
        WHERE 
            ({atc_conditions})
            AND p.OPSCode LIKE '1-790%'
            AND f.Entlassdatum IS NOT NULL
            AND p.Durchfuehrungsdatum IS NOT NULL
            AND JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -? AND ?
        GROUP BY m.PID
        ORDER BY m.PID, p.Durchfuehrungsdatum, f.Entlassdatum
        """
        
        df = pd.read_sql_query(query, self.conn, params=(before_days, days_after))
        return df[['PID']].drop_duplicates()
    
    def get_diabetes_patients(self, days_before=None, days_after=365):
        """
        Get patients with diabetes (ICD E10-E14).
        
        Args:
            days_before (int): Number of days before sleep study to look for diagnosis 
                             (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis 
                            (default: 365 - 1 year)
        
        Returns:
            DataFrame: Patient IDs with diabetes
        """
        # Use the generic function to get diabetes around sleep study
        df = self.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern='E1%',
            days_before=days_before if days_before is not None else 999999,
            days_after=days_after
        )
        # Filter for diabetes codes E10-E14
        mask = (
            (df['primary_icd'].notna() & df['primary_icd'].str[:3].between('E10', 'E14')) |
            (df['secondary_icd'].notna() & df['secondary_icd'].str[:3].between('E10', 'E14'))
        )
        df = df[mask]
        return df[['PID']].drop_duplicates()
        
    def get_cardiovascular_patients(self, days_before=None, days_after=365):
        """
        Get patients with cardiovascular diseases (ICD I20-I25).
        
        Args:
            days_before (int): Number of days before sleep study to look for diagnosis 
                             (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis 
                            (default: 365 - 1 year)
        
        Returns:
            DataFrame: Patient IDs with cardiovascular disease
        """
        # Use the generic function to get cardiovascular disease around sleep study
        df = self.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern='I2%',
            days_before=days_before if days_before is not None else 999999,
            days_after=days_after
        )
        # Filter for cardiovascular codes I20-I25
        mask = (
            (df['primary_icd'].notna() & df['primary_icd'].str[:3].between('I20', 'I25')) |
            (df['secondary_icd'].notna() & df['secondary_icd'].str[:3].between('I20', 'I25'))
        )
        df = df[mask]
        return df[['PID']].drop_duplicates()
        
    
    def get_medication_data(self):
        """
        Get medication data for patients.
        
        Returns:
            DataFrame: Medication data
        """
        query = """
        SELECT 
            m.PID,
            m.Medikationsbezeichnung,
            m.ATCCode,
            m.ATCBezeichnung
        FROM Medikation m
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_medication_by_atc_class(self, days_before=None, days_after=365):
        """
        Get patients on specific medication classes by ATC code.
        
        Args:
            days_before (int): Number of days before sleep study to look for medication 
                             (default: None - no limit)
            days_after (int): Number of days after sleep study to look for medication 
                            (default: 365 - 1 year)
        
        Returns:
            tuple: (dict of DataFrames by medication class, dict of ATC classes)
        """
        atc_classes = {
            'C02': 'Antihypertensiva',
            'C03': 'Diuretika',
            'C07': 'Beta-Blocker',
            'C08': 'Calciumkanalblocker',
            'C09': 'ACE-Hemmer/ARBs'
        }
        
        result = {}
        
        for atc_prefix, name in atc_classes.items():
            # Set days_before to large number if not specified
            before_days = days_before if days_before is not None else 999999
            
            query = f"""
            SELECT DISTINCT 
                m.PID,
                m.Medikationsbezeichnung,
                m.ATCCode,
                m.ATCBezeichnung,
                p.Durchfuehrungsdatum as sleep_study_date,
                f.Entlassdatum as medication_date,
                JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between
            FROM Medikation m
            JOIN Prozeduren p ON m.PID = p.PID
            JOIN Fall f ON f.Fallscheinnummer  = m.Fallscheinnummer
            WHERE 
                m.ATCCode LIKE '{atc_prefix}%'
                AND p.OPSCode LIKE '1-790%'
                AND m.Status <> 'nicht verabreicht'
                AND f.Entlassdatum IS NOT NULL
                AND p.Durchfuehrungsdatum IS NOT NULL
                AND JULIANDAY(f.Entlassdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -? AND ?
            ORDER BY m.PID, p.Durchfuehrungsdatum, f.Entlassdatum
            """
            df = pd.read_sql_query(query, self.conn, params=(before_days, days_after))

            
            df[f'on_{name.lower().replace("-", "_")}'] = 1
            result[name] = df
        
        return result, atc_classes
    
    def sql(self, query):
        return pd.read_sql_query(query, self.conn)
    
    def get_bmi_data(self):
        """
        Get BMI data from vital signs or calculate it from height and weight.
        
        Returns:
            DataFrame: BMI data
        """
        # First try to get direct BMI measurements
        bmi_query = """
        SELECT 
            v.PID,
            v.Vitalwert as BMI
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%BMI%' OR v.Text LIKE '%BMI%'
        """
        bmi_df = pd.read_sql_query(bmi_query, self.conn)
        
        # Convert BMI to numeric, handling potential errors
        if not bmi_df.empty:
            bmi_df['BMI'] = pd.to_numeric(bmi_df['BMI'], errors='coerce')
        
        # Get height data
        height_query = """
        SELECT 
            v.PID,
            v.Vitalwert as Height,
            v.Masseinheit as Height_Unit
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%Größe%' OR v.Text LIKE '%Größe%' OR v.Vitalparameter LIKE '%Height%'
        """
        height_df = pd.read_sql_query(height_query, self.conn)
        
        # Get weight data
        weight_query = """
        SELECT 
            v.PID,
            v.Vitalwert as Weight,
            v.Masseinheit as Weight_Unit
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%Gewicht%' OR v.Text LIKE '%Gewicht%' OR v.Vitalparameter LIKE '%Weight%'
        """
        weight_df = pd.read_sql_query(weight_query, self.conn)
        
        # Convert height and weight to numeric
        if not height_df.empty:
            height_df['Height'] = pd.to_numeric(height_df['Height'], errors='coerce')
        
        if not weight_df.empty:
            weight_df['Weight'] = pd.to_numeric(weight_df['Weight'], errors='coerce')
        
        # Merge height and weight data
        hw_df = pd.merge(height_df, weight_df, on='PID', how='inner')
        
        # Calculate BMI for patients with height and weight
        if not hw_df.empty:
            # Convert height to meters if in cm
            hw_df['Height_m'] = hw_df.apply(
                lambda row: row['Height'] / 100 if (pd.notna(row['Height_Unit']) and row['Height_Unit'] == 'cm') or 
                                                   (pd.isna(row['Height_Unit']) and row['Height'] > 3) 
                            else row['Height'], 
                axis=1
            )
            
            # Convert weight to kg if in g
            hw_df['Weight_kg'] = hw_df.apply(
                lambda row: row['Weight'] / 1000 if (pd.notna(row['Weight_Unit']) and row['Weight_Unit'] == 'g')
                            else row['Weight'], 
                axis=1
            )
            
            # Calculate BMI: weight(kg) / height(m)²
            hw_df['Calculated_BMI'] = hw_df['Weight_kg'] / (hw_df['Height_m'] ** 2)
            
            # Create a dataframe with calculated BMIs
            calc_bmi_df = hw_df[['PID', 'Calculated_BMI']].rename(columns={'Calculated_BMI': 'BMI'})
            
            # Combine direct BMI measurements with calculated BMIs
            if not bmi_df.empty:
                # For patients with both measured and calculated BMI, prefer the measured one
                combined_df = pd.concat([bmi_df, calc_bmi_df])
                combined_df = combined_df.drop_duplicates(subset=['PID'], keep='first')
            else:
                combined_df = calc_bmi_df
        else:
            combined_df = bmi_df
        
        # Group by patient and take the average BMI (in case of multiple measurements)
        if not combined_df.empty:
            result_df = combined_df.groupby('PID')['BMI'].mean().reset_index()
            
            # Filter out implausible BMI values
            result_df = result_df[(result_df['BMI'] > 10) & (result_df['BMI'] < 100)]
            
            return result_df
        else:
            return pd.DataFrame(columns=['PID', 'BMI'])

    def get_all_diagnoses_for_patients(self, patient_ids=None, days_before=None, days_after=365):
        """
        Get all diagnoses for specified patients or all patients with sleep studies.

        Args:
            patient_ids (list): List of patient IDs to get diagnoses for (default: None - all sleep study patients)
            days_before (int): Number of days before sleep study to look for diagnosis (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis (default: 365)

        Returns:
            DataFrame: All diagnoses with patient IDs and ICD codes
        """
        # Build patient filter
        patient_filter = ""
        params = []

        if patient_ids is not None:
            placeholders = ','.join(['?' for _ in patient_ids])
            patient_filter = f"AND d.PID IN ({placeholders})"
            params.extend(patient_ids)

        # Build timing filter
        timing_filter = ""
        if days_before is not None or days_after is not None:
            timing_filter = """
            AND EXISTS (
                SELECT 1 FROM Prozeduren p
                WHERE p.PID = d.PID
                AND p.OPSCode LIKE '1-790%'
                AND p.Durchfuehrungsdatum IS NOT NULL
                AND d.Feststellungsdatum IS NOT NULL
            """

            if days_before is not None and days_after is not None:
                timing_filter += " AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -? AND ?"
                params.extend([days_before, days_after])
            elif days_after is not None:
                timing_filter += " AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) <= ?"
                params.append(days_after)

            timing_filter += ")"

        query = f"""
        SELECT DISTINCT
            d.PID,
            d.PrimaerdiagnoseICD as primary_icd,
            d.PrimaerdiagnoseText as primary_text,
            d.SekundaerdiagnoseICD as secondary_icd,
            d.SekundaerdiagnoseText as secondary_text,
            d.DiagnoseTyp as diagnosis_type,
            d.Diagnosesicherheit as diagnosis_certainty,
            d.Feststellungsdatum as diagnosis_date
        FROM Diagnosen d
        WHERE
            (d.PrimaerdiagnoseICD IS NOT NULL OR d.SekundaerdiagnoseICD IS NOT NULL)
            AND d.Diagnosetyp NOT IN ('Aufnahmediagnose','Einweisungsdiagnose')
            {patient_filter}
            {timing_filter}
        ORDER BY d.PID, d.Feststellungsdatum
        """

        return pd.read_sql_query(query, self.conn, params=params)

    def get_elixhauser_comorbidities(self, patient_ids=None, days_before=None, days_after=365):
        """
        Get Elixhauser comorbidities for specified patients.

        Args:
            patient_ids (list): List of patient IDs to get comorbidities for (default: None - all sleep study patients)
            days_before (int): Number of days before sleep study to look for diagnosis (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis (default: 365)

        Returns:
            DataFrame: DataFrame with PID and binary columns for each Elixhauser comorbidity
        """
        # Get all diagnoses for the specified patients
        diagnoses_df = self.get_all_diagnoses_for_patients(patient_ids, days_before, days_after)

        if diagnoses_df.empty:
            # Return empty dataframe with proper structure
            empty_df = pd.DataFrame({'PID': []})
            for comorbidity in self.elixhauser_mapper.comorbidity_mappings.keys():
                empty_df[f'elixhauser_{comorbidity}'] = []
            return empty_df

        # Map diagnoses to Elixhauser comorbidities
        comorbidities_df = self.elixhauser_mapper.map_patient_comorbidities(diagnoses_df)

        return comorbidities_df

    def analyze_elixhauser_icd_coverage(self, patient_ids=None, days_before=None, days_after=365):
        """
        Analyze ICD code coverage for Elixhauser comorbidities.

        Args:
            patient_ids (list): List of patient IDs to analyze (default: None - all sleep study patients)
            days_before (int): Number of days before sleep study to look for diagnosis (default: None - no limit)
            days_after (int): Number of days after sleep study to look for diagnosis (default: 365)

        Returns:
            tuple: (coverage_analysis_df, detailed_mapping_dict)
        """
        # Get all diagnoses for analysis
        diagnoses_df = self.get_all_diagnoses_for_patients(patient_ids, days_before, days_after)

        # Perform coverage analysis
        coverage_df = self.elixhauser_mapper.analyze_icd_code_coverage(diagnoses_df)
        detailed_mapping = self.elixhauser_mapper.get_detailed_icd_mapping()

        return coverage_df, detailed_mapping

    def extract_all_data(self, use_sleep_study_timing=False, days_before_sleep_study=None, days_after_sleep_study=365):
        """Extract and merge all required data for analysis.
        
        Args:
            use_sleep_study_timing (bool): If True, only include diagnoses/medications around sleep study (default: False)
            days_before_sleep_study (int): Number of days before sleep study to look for diagnoses/medications 
                                         (default: None - no limit if use_sleep_study_timing=False)
            days_after_sleep_study (int): Number of days after sleep study to look for diagnoses/medications 
                                        (default: 365 - 1 year)
        
        Returns:
            tuple: (merged_dataframe, top_osa_meds, top_osa_atc, atc_classes)
        """
        # Get OSA patients
        osa_patients = self.get_osa_patients()
        osa_patients['has_osa'] = 1

        suspected_osa_patients = self.get_suspected_osa_patients()
        suspected_osa_patients['has_suspected_osa'] = 1

        unsuspected_osa_patients = self.get_unsuspected_osa_patients()
        unsuspected_osa_patients['has_unsuspected_osa'] = 1
        
        # Get sleep study patients
        sleep_study_patients = self.get_sleep_study_patients()
        sleep_study_patients['had_sleep_study'] = 1
        
        # Get demographics
        demographics = self.get_patient_demographics()
        
        # Get comorbidities with timing constraints if specified
        hypertension_patients = self.get_hypertension_patients(
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )
        hypertension_patients['has_diagnosed_hypertension'] = 1
        
        # Get patients on antihypertensive medications with timing constraints if specified
        antihypertensive_patients = self.get_patients_on_antihypertensives(
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )
        antihypertensive_patients['on_antihypertensives'] = 1
        
        # Get specific antihypertensive medication classes with timing constraints if specified
        med_classes, atc_classes = self.get_medication_by_atc_class(
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )
        
        diabetes_patients = self.get_diabetes_patients(
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )
        diabetes_patients['has_diabetes'] = 1
                
        cardiovascular_patients = self.get_cardiovascular_patients(
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )
        cardiovascular_patients['has_cardiovascular'] = 1
        
        # Get BMI data
        bmi_data = self.get_bmi_data()

        # Get Elixhauser comorbidities
        elixhauser_data = self.get_elixhauser_comorbidities(
            patient_ids=None,  # Get for all sleep study patients
            days_before=days_before_sleep_study,
            days_after=days_after_sleep_study
        )

        # Merge all data
        df = sleep_study_patients
        df = df.merge(osa_patients[['PID', 'has_osa']], on='PID', how='left')
        df['has_osa'] = df['has_osa'].fillna(0)

        # Convert dates to datetime
        df['sleep_study_date'] = pd.to_datetime(df['sleep_study_date'], errors='coerce')
        demographics['Geburtsdatum'] = pd.to_datetime(demographics['Geburtsdatum'], errors='coerce')

        df = df.merge(demographics[['PID', 'Sex', 'Geburtsdatum']], on='PID', how='left')

        df['Age'] = (df['sleep_study_date'] - df['Geburtsdatum']).dt.days / 365.25
        df = df.merge(hypertension_patients[['PID', 'has_diagnosed_hypertension']], on='PID', how='left')
        df = df.merge(antihypertensive_patients[['PID', 'on_antihypertensives']], on='PID', how='left')
        
        # Merge specific medication classes
        for name, med_df in med_classes.items():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in med_df.columns:
                med_df_unique = med_df[['PID', col_name]].drop_duplicates(subset=['PID'])
                df = df.merge(med_df_unique[['PID', col_name]], on='PID', how='left')
        
        df = df.merge(diabetes_patients[['PID', 'has_diabetes']], on='PID', how='left')
        df = df.merge(cardiovascular_patients[['PID', 'has_cardiovascular']], on='PID', how='left')
        df = df.merge(bmi_data, on='PID', how='left')

        df = df.merge(suspected_osa_patients[['PID', 'has_suspected_osa']], on='PID', how='left')
        df = df.merge(unsuspected_osa_patients[['PID', 'has_unsuspected_osa']], on='PID', how='left')

        # Merge Elixhauser comorbidities
        df = df.merge(elixhauser_data, on='PID', how='left')

        # Fill NAs for binary variables
        binary_cols = [col for col in df.columns if col.startswith(('has_', 'had_', 'on_', 'elixhauser_'))]
        df[binary_cols] = df[binary_cols].fillna(0)
        
        # Create combined hypertension variable (diagnosed OR on medications)
        df['has_hypertension'] = ((df['has_diagnosed_hypertension'] == 1) | 
                                 (df['on_antihypertensives'] == 1)).astype(int)
        
        # Analyze medication patterns
        med_data = self.get_medication_data()
        
        # Get the most common medications for OSA patients
        osa_meds = med_data[med_data['PID'].isin(osa_patients['PID'])]
        top_osa_meds = osa_meds['ATCBezeichnung'].value_counts().head(10)
        
        # Get the most common ATC codes for OSA patients
        top_osa_atc = osa_meds['ATCCode'].apply(lambda x: x[:3] if pd.notna(x) else x).value_counts().head(10)
        
        return df, top_osa_meds, top_osa_atc, atc_classes
    
    def extract_all_data_with_timing(self):
        """
        Extract all data with strict timing constraints around sleep study.
        Hypertension and medications must be diagnosed/prescribed before or within 1 year after sleep study.
        
        Returns:
            tuple: (merged_dataframe, top_osa_meds, top_osa_atc, atc_classes)
        """
        return self.extract_all_data(
            use_sleep_study_timing=True,
            days_before_sleep_study=None,  # No limit before
            days_after_sleep_study=365     # 1 year after
        )
    
    def get_patients_with_diagnosis_around_sleep_study(self, diagnosis_pattern, days_before=30, days_after=30, 
                                                       diagnosis_field='both', diagnosis_type=None):
        """
        Generic function to get patients with any diagnosis around their sleep study.
        
        Args:
            diagnosis_pattern (str): SQL LIKE pattern for diagnosis codes (e.g., 'O09%', 'G47%', 'I10%')
            days_before (int): Number of days before sleep study to look for diagnosis (default: 30)
            days_after (int): Number of days after sleep study to look for diagnosis (default: 30)
            diagnosis_field (str): Which diagnosis field to search ('primary', 'secondary', 'both') (default: 'both')
            diagnosis_type (str): Filter by diagnosis type (e.g., 'Aufnahmediagnose', 'Entlassdiagnose') (default: None)
        
        Returns:
            DataFrame: Patient IDs with diagnosis, sleep study date, and timing details
        """
        # Build diagnosis field conditions
        if diagnosis_field == 'primary':
            diagnosis_condition = f"d.PrimaerdiagnoseICD LIKE '{diagnosis_pattern}'"
        elif diagnosis_field == 'secondary':
            diagnosis_condition = f"d.SekundaerdiagnoseICD LIKE '{diagnosis_pattern}'"
        else:  # both
            diagnosis_condition = f"(d.PrimaerdiagnoseICD LIKE '{diagnosis_pattern}' OR d.SekundaerdiagnoseICD LIKE '{diagnosis_pattern}')"
        
        # Add diagnosis type filter if specified
        diagnosis_type_condition = ""
        if diagnosis_type:
            diagnosis_type_condition = f"AND d.DiagnoseTyp = '{diagnosis_type}'"
        
        query = f"""
        SELECT DISTINCT 
            d.PID,
            d.Feststellungsdatum as diagnosis_date,
            d.PrimaerdiagnoseICD as primary_icd,
            d.PrimaerdiagnoseText as primary_text,
            d.SekundaerdiagnoseICD as secondary_icd,
            d.SekundaerdiagnoseText as secondary_text,
            d.DiagnoseTyp as diagnosis_type,
            d.Diagnosesicherheit as diagnosis_certainty,
            p.Durchfuehrungsdatum as sleep_study_date,
            JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) as days_between,
            CASE 
                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) < 0 THEN 'before_sleep_study'
                WHEN JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) = 0 THEN 'same_day'
                ELSE 'after_sleep_study'
            END as timing_category,
            CASE 
                WHEN d.PrimaerdiagnoseICD LIKE '{diagnosis_pattern}' THEN 'primary'
                WHEN d.SekundaerdiagnoseICD LIKE '{diagnosis_pattern}' THEN 'secondary'
                ELSE 'unknown'
            END as diagnosis_position
        FROM Diagnosen d
        JOIN Prozeduren p ON d.PID = p.PID
        WHERE 
            {diagnosis_condition}
            AND p.OPSCode LIKE '1-790%'
            AND d.Feststellungsdatum IS NOT NULL
            AND d.Diagnosetyp NOT IN ('Aufnahmediagnose','Einweisungsdiagnose')
            AND p.Durchfuehrungsdatum IS NOT NULL
            {diagnosis_type_condition}
            AND JULIANDAY(d.Feststellungsdatum) - JULIANDAY(p.Durchfuehrungsdatum) BETWEEN -? AND ?
        GROUP BY d.PID
        ORDER BY d.PID, p.Durchfuehrungsdatum, d.Feststellungsdatum
        """
        
        return pd.read_sql_query(query, self.conn, params=(days_before, days_after))
