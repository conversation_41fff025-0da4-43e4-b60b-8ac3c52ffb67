#!/usr/bin/env python3
"""
OSA Analysis Main Module
-----------------------
Main entry point for the OSA analysis application.
"""

import argparse
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor
from osa_analysis.analysis.processor import DataProcessor
from osa_analysis.analysis.statistics import StatisticsGenerator
from osa_analysis.analysis.stop_bang import StopBangAnalyzer
from osa_analysis.analysis.modeling import PredictiveModeler
from osa_analysis.analysis.model_evaluation import ModelEvaluator
from osa_analysis.visualization.plots import Visualizer

def main():
    """Main entry point for the OSA analysis application."""
    # Parse command line arguments
    
    print("Starting OSA Analysis...")
    
    # Determine timing settings
    
    
    # Connect to the database
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()
    
    # Extract data
    print("Extracting data from database...")
    extractor = DataExtractor(conn)
    
    df, top_osa_meds, top_osa_atc, atc_classes = extractor.extract_all_data_with_timing()
    
    print(f"Data extraction complete. Dataset contains {len(df)} patients.")
    
    # Generate statistics
    print("Generating statistics...")
    stats_generator = StatisticsGenerator()
    stats_generator.generate_statistics(df, top_osa_meds, top_osa_atc, atc_classes)
    
    # Perform STOP-BANG analysis
    print("Performing STOP-BANG analysis...")
    stop_bang_analyzer = StopBangAnalyzer()
    stop_bang_df = stop_bang_analyzer.analyze(df)
    
    # Build predictive models
    print("Building predictive models...")
    modeler = PredictiveModeler()
    modeler.build_models(df, atc_classes)
    
    # Evaluate model performance across demographic groups
    print("Evaluating model performance across demographic groups...")
    evaluator = ModelEvaluator()
    evaluator.evaluate_across_groups(df, atc_classes)
    
    # Create visualizations
    print("Creating visualizations...")
    visualizer = Visualizer()
    visualizer.create_visualizations(df, atc_classes)
    visualizer.create_stop_bang_visualizations(stop_bang_df)
    
    # Close database connection
    db_connector.close_connection()
    
    print("Analysis complete!")
    print("  - Only diagnoses/medications within specified timeframe were included")

if __name__ == "__main__":
    main()
