"""
Predictive Modeling Module
-------------------------
Builds and evaluates predictive models for OSA.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import statsmodels.api as sm

class PredictiveModeler:
    """Builds and evaluates predictive models for OSA."""

    def build_models(self, df, atc_classes):
        """
        Build and evaluate predictive models for OSA.

        Args:
            df (DataFrame): The merged dataset
            atc_classes (dict): Dictionary of ATC classes
        """
        print("\n" + "-"*50)
        print("PREDICTIVE MODELING FOR OSA")
        print("-"*50)

        # Prepare data for modeling
        model_cols = ['Age', 'has_hypertension', 'has_diabetes', 
                     'has_cardiovascular', 'on_antihypertensives']

        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                model_cols.append(col_name)

        # Add sex as dummy variable if available
        if 'Sex' in df.columns and df['Sex'].notna().any():
            # Convert sex to dummy variables
            df['is_male'] = (df['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')

        # Add BMI if available
        if 'BMI' in df.columns and df['BMI'].notna().any():
            model_cols.append('BMI')

        # Filter rows with complete data
        model_df = df[['has_osa'] + model_cols].dropna()

        if len(model_df) > 10:  # Only proceed if we have enough data
            X = model_df[model_cols]
            y = model_df['has_osa']

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

            # Train Random Forest model
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X_train, y_train)

            # Evaluate model
            y_pred = rf.predict(X_test)
            y_prob = rf.predict_proba(X_test)[:, 1]

            print("\nRandom Forest Model Performance:")
            print(classification_report(y_test, y_pred))

            # Calculate and display AUC
            auc = roc_auc_score(y_test, y_prob)
            print(f"\nAUC (Area Under ROC Curve): {auc:.4f}")

            # Calculate ROC curve data (not used directly but available for future plotting)
            _ = roc_curve(y_test, y_prob)

            # Feature importance
            feature_importance = pd.DataFrame({
                'Feature': model_cols,
                'Importance': rf.feature_importances_
            }).sort_values('Importance', ascending=False)

            print("\nFeature Importance for Predicting OSA:")
            for i, row in feature_importance.iterrows():
                print(f"  {row['Feature']}: {row['Importance']:.4f}")

            # Logistic Regression for odds ratios
            X_with_const = sm.add_constant(X)
            logit_model = sm.Logit(y, X_with_const)

            try:
                result = logit_model.fit(disp=0)
                odds_ratios = np.exp(result.params)
                conf_intervals = np.exp(result.conf_int())

                print("\nLogistic Regression Results (Odds Ratios):")
                for i, var in enumerate(['const'] + model_cols):
                    print(f"  {var}: OR={odds_ratios[i]:.2f}, 95% CI=({conf_intervals[0][i]:.2f}-{conf_intervals[1][i]:.2f})")
            except Exception as e:
                print("\nLogistic Regression could not converge. This might be due to perfect separation or multicollinearity.")
                print(f"Error details: {str(e)}")

            # Sex-specific models if we have enough data
            if 'Sex' in df.columns and df['Sex'].notna().any():
                self._build_sex_specific_models(model_df, model_cols)
        else:
            print("\nInsufficient data for predictive modeling.")

    def _build_sex_specific_models(self, model_df, model_cols):
        """Build sex-specific predictive models."""
        print("\nSex-specific predictive models:")
        # Use is_male to determine sex instead of looking for 'Sex' column
        sexes = ['männlich', 'weiblich']
        for sex in sexes:
            if pd.notna(sex):
                # Filter data for this sex
                sex_model_df = model_df[model_df['is_male'] == (1 if sex == 'männlich' else 0)]

                if len(sex_model_df) > 20:  # Only proceed if we have enough data
                    sex_X = sex_model_df[model_cols]
                    sex_y = sex_model_df['has_osa']

                    # Remove 'is_male' from features for sex-specific model
                    if 'is_male' in sex_X.columns:
                        sex_X = sex_X.drop('is_male', axis=1)

                    # Split data
                    sex_X_train, sex_X_test, sex_y_train, sex_y_test = train_test_split(
                        sex_X, sex_y, test_size=0.3, random_state=42
                    )

                    # Train Random Forest model
                    sex_rf = RandomForestClassifier(n_estimators=100, random_state=42)
                    sex_rf.fit(sex_X_train, sex_y_train)

                    # Evaluate model
                    sex_y_pred = sex_rf.predict(sex_X_test)
                    sex_y_prob = sex_rf.predict_proba(sex_X_test)[:, 1]

                    print(f"\n{sex} patients model performance:")
                    print(classification_report(sex_y_test, sex_y_pred))

                    # Calculate and display AUC for sex-specific model
                    sex_auc = roc_auc_score(sex_y_test, sex_y_prob)
                    print(f"AUC (Area Under ROC Curve): {sex_auc:.4f}")

                    # Feature importance
                    sex_feature_importance = pd.DataFrame({
                        'Feature': sex_X.columns,
                        'Importance': sex_rf.feature_importances_
                    }).sort_values('Importance', ascending=False)

                    print(f"\nFeature Importance for Predicting OSA in {sex} patients:")
                    for i, row in sex_feature_importance.iterrows():
                        print(f"  {row['Feature']}: {row['Importance']:.4f}")
                else:
                    print(f"\nInsufficient data for {sex}-specific model (n={len(sex_model_df)}).")
