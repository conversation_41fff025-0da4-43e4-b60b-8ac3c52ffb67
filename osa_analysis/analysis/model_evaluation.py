"""
Model Evaluation Module
----------------------
Evaluates model performance across different demographic groups.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
import os

class ModelEvaluator:
    """Evaluates model performance across different demographic groups."""
    
    def __init__(self):
        """Initialize the model evaluator."""
        # Create plots directory if it doesn't exist
        if not os.path.exists('plots'):
            os.makedirs('plots')
    
    def evaluate_across_groups(self, df, atc_classes):
        """
        Evaluate model performance across different demographic groups.

        Args:
            df (DataFrame): The merged dataset
            atc_classes (dict): Dictionary of ATC classes
        """
        print("\n" + "="*50)
        print("MODEL PERFORMANCE ACROSS DEMOGRAPHIC GROUPS")
        print("="*50)

        # Prepare data for modeling
        model_cols = ['Age', 'has_hypertension', 'has_diabetes',
                     'has_cardiovascular', 'on_antihypertensives']

        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                model_cols.append(col_name)

        # Add sex as dummy variable if available
        if 'Sex' in df.columns and df['Sex'].notna().any():
            # Convert sex to dummy variables
            df['is_male'] = (df['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')

        # Add BMI if available
        if 'BMI' in df.columns and df['BMI'].notna().any():
            model_cols.append('BMI')

        # Calculate STOP-BANG scores if not already present
        if 'partial_stop_bang_score' not in df.columns:
            df = self._calculate_stop_bang_scores(df)

        # Filter rows with complete data
        model_df = df[list(set(['has_osa', 'has_suspected_osa', 'has_unsuspected_osa', 'Sex', 'Age', 'partial_stop_bang_score'] + model_cols))].dropna()

        if len(model_df) > 10:  # Only proceed if we have enough data
            # Evaluate Random Forest model by demographic groups
            print("\n" + "="*50)
            print("RANDOM FOREST MODEL EVALUATION")
            print("="*50)

            # Evaluate by gender
            self._evaluate_by_gender(model_df, model_cols)

            # Evaluate by age ranges
            self._evaluate_by_age_decades(model_df, model_cols)
            self._evaluate_by_age_fifty(model_df, model_cols)

            # Evaluate by OSA suspicion status
            self._evaluate_by_suspected_osa(model_df, model_cols)
            self._evaluate_by_unsuspected_osa(model_df, model_cols)

            # Create summary visualization for Random Forest
            self._create_summary_visualization(model_df, model_cols)

            # Evaluate STOP-BANG model by demographic groups
            print("\n" + "="*50)
            print("STOP-BANG MODEL EVALUATION")
            print("="*50)

            # Evaluate STOP-BANG by gender
            self._evaluate_stop_bang_by_gender(model_df)

            # Evaluate STOP-BANG by age ranges
            self._evaluate_stop_bang_by_age_decades(model_df)
            self._evaluate_stop_bang_by_age_fifty(model_df)

            # Evaluate STOP-BANG by OSA suspicion status
            self._evaluate_stop_bang_by_suspected_osa(model_df)
            self._evaluate_stop_bang_by_unsuspected_osa(model_df)

            # Create summary visualization for STOP-BANG
            self._create_stop_bang_summary_visualization(model_df)

            # Compare Random Forest vs STOP-BANG performance
            self._compare_models(model_df, model_cols)
        else:
            print("\nInsufficient data for model evaluation.")

    def _calculate_stop_bang_scores(self, df):
        """
        Calculate STOP-BANG scores for the dataset.

        Args:
            df (DataFrame): The dataset

        Returns:
            DataFrame: Dataset with STOP-BANG scores added
        """
        # Create a copy to avoid modifying the original
        stop_bang_df = df.copy()

        # Initialize STOP-BANG components
        stop_bang_df['sb_bmi'] = 0
        stop_bang_df['sb_age'] = 0
        stop_bang_df['sb_gender'] = 0
        stop_bang_df['sb_pressure'] = 0

        # B: BMI > 35 kg/m²
        if 'BMI' in stop_bang_df.columns:
            stop_bang_df.loc[stop_bang_df['BMI'] > 35, 'sb_bmi'] = 1

        # A: Age > 50 years
        if 'Age' in stop_bang_df.columns:
            stop_bang_df.loc[stop_bang_df['Age'] > 50, 'sb_age'] = 1

        # G: Gender (male)
        if 'Sex' in stop_bang_df.columns:
            stop_bang_df.loc[stop_bang_df['Sex'] == 'männlich', 'sb_gender'] = 1
        elif 'is_male' in stop_bang_df.columns:
            stop_bang_df.loc[stop_bang_df['is_male'] == 1, 'sb_gender'] = 1

        # P: High blood pressure
        if 'has_hypertension' in stop_bang_df.columns:
            stop_bang_df.loc[stop_bang_df['has_hypertension'] == 1, 'sb_pressure'] = 1

        # Calculate partial STOP-BANG score (out of 4)
        stop_bang_df['partial_stop_bang_score'] = (
            stop_bang_df['sb_bmi'] +
            stop_bang_df['sb_age'] +
            stop_bang_df['sb_gender'] +
            stop_bang_df['sb_pressure']
        )

        return stop_bang_df

    def _evaluate_by_gender(self, df, model_cols):
        """Evaluate model performance by gender."""
        print("\n" + "-"*50)
        print("MODEL PERFORMANCE BY GENDER")
        print("-"*50)
        
        if 'Sex' in df.columns and df['Sex'].notna().any():
            # Get unique genders (excluding any non-binary for this analysis)
            genders = df['Sex'].unique()
            genders = [g for g in genders if g in ['männlich', 'weiblich']]
            
            results = []
            
            # Evaluate for each gender
            for gender in genders:
                gender_df = df[df['Sex'] == gender]
                
                if len(gender_df) > 10:  # Only proceed if we have enough data
                    # Remove 'is_male' from features for gender-specific model
                    gender_features = [col for col in model_cols if col != 'is_male']
                    
                    X = gender_df[gender_features]
                    y = gender_df['has_osa']
                    
                    # Perform cross-validation
                    metrics = self._cross_validate(X, y)
                    
                    print(f"\nPerformance for {gender} patients (n={len(gender_df)}):")
                    print(f"  AUC: {metrics['auc_mean']:.3f} ± {metrics['auc_std']:.3f}")
                    print(f"  Accuracy: {metrics['accuracy_mean']:.3f} ± {metrics['accuracy_std']:.3f}")
                    print(f"  Precision: {metrics['precision_mean']:.3f} ± {metrics['precision_std']:.3f}")
                    print(f"  Recall: {metrics['recall_mean']:.3f} ± {metrics['recall_std']:.3f}")
                    print(f"  F1 Score: {metrics['f1_mean']:.3f} ± {metrics['f1_std']:.3f}")
                    
                    # Store results for visualization
                    results.append({
                        'Group': gender,
                        'Category': 'Gender',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Accuracy': metrics['accuracy_mean'],
                        'Precision': metrics['precision_mean'],
                        'Recall': metrics['recall_mean'],
                        'F1': metrics['f1_mean'],
                        'Count': len(gender_df)
                    })
                else:
                    print(f"\nInsufficient data for {gender} patients (n={len(gender_df)}).")
            
            # Compare performance between genders
            if len(results) > 1:
                print("\nGender performance comparison:")
                male_metrics = next((r for r in results if r['Group'] == 'männlich'), None)
                female_metrics = next((r for r in results if r['Group'] == 'weiblich'), None)
                
                if male_metrics and female_metrics:
                    auc_diff = male_metrics['AUC'] - female_metrics['AUC']
                    print(f"  AUC difference (male - female): {auc_diff:.3f}")
                    
                    if abs(auc_diff) > 0.05:
                        better_gender = "male" if auc_diff > 0 else "female"
                        print(f"  Model performs better for {better_gender} patients")
                    else:
                        print("  No substantial difference in model performance between genders")
            
            # Create visualization
            if results:
                self._create_group_comparison_plot(results, 'Gender', 'gender_performance')
        else:
            print("Sex information not available for gender-based evaluation.")
    
    def _evaluate_by_age_decades(self, df, model_cols):
        """Evaluate model performance by age decades."""
        print("\n" + "-"*50)
        print("MODEL PERFORMANCE BY AGE DECADES")
        print("-"*50)
        
        if 'Age' in df.columns and df['Age'].notna().any():
            # Create age decade groups
            df['age_decade'] = (df['Age'] // 10 * 10).astype(int)
            df['age_decade'][df['age_decade'] < 20] = 20
            df['age_decade'][df['age_decade'] > 80] = 80
            
            # Get unique decades
            decades = sorted(df['age_decade'].unique())
            
            results = []
            
            # Evaluate for each decade
            for decade in decades:
                decade_df = df[df['age_decade'] == decade]
                
                if len(decade_df) > 10:  # Only proceed if we have enough data
                    X = decade_df[model_cols]
                    y = decade_df['has_osa']
                    
                    # Perform cross-validation
                    metrics = self._cross_validate(X, y)
                    
                    decade_label = f"{decade}s"
                    if decade == 20:
                      decade_label += " or younger"
                    if decade == 80:
                      decade_label += " or older"
                    
                    print(f"\nPerformance for patients in their {decade_label} (n={len(decade_df)}):")
                    print(f"  AUC: {metrics['auc_mean']:.3f} ± {metrics['auc_std']:.3f}")
                    print(f"  Accuracy: {metrics['accuracy_mean']:.3f} ± {metrics['accuracy_std']:.3f}")
                    print(f"  Precision: {metrics['precision_mean']:.3f} ± {metrics['precision_std']:.3f}")
                    print(f"  Recall: {metrics['recall_mean']:.3f} ± {metrics['recall_std']:.3f}")
                    print(f"  F1 Score: {metrics['f1_mean']:.3f} ± {metrics['f1_std']:.3f}")
                    
                    # Store results for visualization
                    results.append({
                        'Group': decade_label,
                        'Category': 'Age Decade',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Accuracy': metrics['accuracy_mean'],
                        'Precision': metrics['precision_mean'],
                        'Recall': metrics['recall_mean'],
                        'F1': metrics['f1_mean'],
                        'Count': len(decade_df)
                    })
                else:
                    print(f"\nInsufficient data for patients in their {decade}s (n={len(decade_df)}).")
            
            # Create visualization
            if results:
                self._create_group_comparison_plot(results, 'Age Decade', 'age_decade_performance')
        else:
            print("Age information not available for decade-based evaluation.")
    
    def _evaluate_by_age_fifty(self, df, model_cols):
        """Evaluate model performance by age split at 50."""
        print("\n" + "-"*50)
        print("MODEL PERFORMANCE BY AGE (SPLIT AT 50)")
        print("-"*50)
        
        if 'Age' in df.columns and df['Age'].notna().any():
            # Create age groups
            df['age_group'] = np.where(df['Age'] >= 50, '≥ 50', '< 50')
            
            results = []
            
            # Evaluate for each age group
            for age_group in ['< 50', '≥ 50']:
                group_df = df[df['age_group'] == age_group]
                
                if len(group_df) > 10:  # Only proceed if we have enough data
                    X = group_df[model_cols]
                    y = group_df['has_osa']
                    
                    # Perform cross-validation
                    metrics = self._cross_validate(X, y)
                    
                    print(f"\nPerformance for patients {age_group} years (n={len(group_df)}):")
                    print(f"  AUC: {metrics['auc_mean']:.3f} ± {metrics['auc_std']:.3f}")
                    print(f"  Accuracy: {metrics['accuracy_mean']:.3f} ± {metrics['accuracy_std']:.3f}")
                    print(f"  Precision: {metrics['precision_mean']:.3f} ± {metrics['precision_std']:.3f}")
                    print(f"  Recall: {metrics['recall_mean']:.3f} ± {metrics['recall_std']:.3f}")
                    print(f"  F1 Score: {metrics['f1_mean']:.3f} ± {metrics['f1_std']:.3f}")
                    
                    # Store results for visualization
                    results.append({
                        'Group': age_group,
                        'Category': 'Age Group',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Accuracy': metrics['accuracy_mean'],
                        'Precision': metrics['precision_mean'],
                        'Recall': metrics['recall_mean'],
                        'F1': metrics['f1_mean'],
                        'Count': len(group_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {age_group} years (n={len(group_df)}).")
            
            # Compare performance between age groups
            if len(results) > 1:
                print("\nAge group performance comparison:")
                younger_metrics = next((r for r in results if r['Group'] == '< 50'), None)
                older_metrics = next((r for r in results if r['Group'] == '≥ 50'), None)
                
                if younger_metrics and older_metrics:
                    auc_diff = older_metrics['AUC'] - younger_metrics['AUC']
                    print(f"  AUC difference (≥50 - <50): {auc_diff:.3f}")
                    
                    if abs(auc_diff) > 0.05:
                        better_group = "older (≥50)" if auc_diff > 0 else "younger (<50)"
                        print(f"  Model performs better for {better_group} patients")
                    else:
                        print("  No substantial difference in model performance between age groups")
            
            # Create visualization
            if results:
                self._create_group_comparison_plot(results, 'Age Group', 'age_group_performance')
        else:
            print("Age information not available for age group evaluation.")
    
    def _evaluate_by_suspected_osa(self, df, model_cols):
        """Evaluate model performance by suspected OSA status."""
        print("\n" + "-"*50)
        print("MODEL PERFORMANCE BY SUSPECTED OSA STATUS")
        print("-"*50)
        
        if 'has_suspected_osa' in df.columns:
            results = []
            
            # Evaluate for patients with and without suspected OSA
            for status in [1, 0]:
                status_label = "With" if status == 1 else "Without"
                status_df = df[df['has_suspected_osa'] == status]
                
                if len(status_df) > 10:  # Only proceed if we have enough data
                    X = status_df[model_cols]
                    y = status_df['has_osa']
                    
                    # Perform cross-validation
                    metrics = self._cross_validate(X, y)
                    
                    print(f"\nPerformance for patients {status_label.lower()} suspected OSA (n={len(status_df)}):")
                    print(f"  AUC: {metrics['auc_mean']:.3f} ± {metrics['auc_std']:.3f}")
                    print(f"  Accuracy: {metrics['accuracy_mean']:.3f} ± {metrics['accuracy_std']:.3f}")
                    print(f"  Precision: {metrics['precision_mean']:.3f} ± {metrics['precision_std']:.3f}")
                    print(f"  Recall: {metrics['recall_mean']:.3f} ± {metrics['recall_std']:.3f}")
                    print(f"  F1 Score: {metrics['f1_mean']:.3f} ± {metrics['f1_std']:.3f}")
                    
                    # Store results for visualization
                    results.append({
                        'Group': f"{status_label} Suspected OSA",
                        'Category': 'Suspected OSA',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Accuracy': metrics['accuracy_mean'],
                        'Precision': metrics['precision_mean'],
                        'Recall': metrics['recall_mean'],
                        'F1': metrics['f1_mean'],
                        'Count': len(status_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {status_label.lower()} suspected OSA (n={len(status_df)}).")
            
            # Compare performance between groups
            if len(results) > 1:
                print("\nSuspected OSA status performance comparison:")
                with_metrics = next((r for r in results if r['Group'] == 'With Suspected OSA'), None)
                without_metrics = next((r for r in results if r['Group'] == 'Without Suspected OSA'), None)
                
                if with_metrics and without_metrics:
                    auc_diff = with_metrics['AUC'] - without_metrics['AUC']
                    print(f"  AUC difference (with - without): {auc_diff:.3f}")
                    
                    if abs(auc_diff) > 0.05:
                        better_group = "with" if auc_diff > 0 else "without"
                        print(f"  Model performs better for patients {better_group} suspected OSA")
                    else:
                        print("  No substantial difference in model performance between suspected OSA groups")
            
            # Create visualization
            if results:
                self._create_group_comparison_plot(results, 'Suspected OSA', 'suspected_osa_performance')
        else:
            print("Suspected OSA information not available for evaluation.")
    
    def _evaluate_by_unsuspected_osa(self, df, model_cols):
        """Evaluate model performance by unsuspected OSA status."""
        print("\n" + "-"*50)
        print("MODEL PERFORMANCE BY UNSUSPECTED OSA STATUS")
        print("-"*50)
        
        if 'has_unsuspected_osa' in df.columns:
            results = []
            
            # Evaluate for patients with and without unsuspected OSA
            for status in [1, 0]:
                status_label = "With" if status == 1 else "Without"
                status_df = df[df['has_unsuspected_osa'] == status]
                
                if len(status_df) > 10:  # Only proceed if we have enough data
                    X = status_df[model_cols]
                    y = status_df['has_osa']
                    
                    # Perform cross-validation
                    metrics = self._cross_validate(X, y)
                    
                    print(f"\nPerformance for patients {status_label.lower()} unsuspected OSA (n={len(status_df)}):")
                    print(f"  AUC: {metrics['auc_mean']:.3f} ± {metrics['auc_std']:.3f}")
                    print(f"  Accuracy: {metrics['accuracy_mean']:.3f} ± {metrics['accuracy_std']:.3f}")
                    print(f"  Precision: {metrics['precision_mean']:.3f} ± {metrics['precision_std']:.3f}")
                    print(f"  Recall: {metrics['recall_mean']:.3f} ± {metrics['recall_std']:.3f}")
                    print(f"  F1 Score: {metrics['f1_mean']:.3f} ± {metrics['f1_std']:.3f}")
                    
                    # Store results for visualization
                    results.append({
                        'Group': f"{status_label} Unsuspected OSA",
                        'Category': 'Unsuspected OSA',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Accuracy': metrics['accuracy_mean'],
                        'Precision': metrics['precision_mean'],
                        'Recall': metrics['recall_mean'],
                        'F1': metrics['f1_mean'],
                        'Count': len(status_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {status_label.lower()} unsuspected OSA (n={len(status_df)}).")
            
            # Compare performance between groups
            if len(results) > 1:
                print("\nUnsuspected OSA status performance comparison:")
                with_metrics = next((r for r in results if r['Group'] == 'With Unsuspected OSA'), None)
                without_metrics = next((r for r in results if r['Group'] == 'Without Unsuspected OSA'), None)
                
                if with_metrics and without_metrics:
                    auc_diff = with_metrics['AUC'] - without_metrics['AUC']
                    print(f"  AUC difference (with - without): {auc_diff:.3f}")
                    
                    if abs(auc_diff) > 0.05:
                        better_group = "with" if auc_diff > 0 else "without"
                        print(f"  Model performs better for patients {better_group} unsuspected OSA")
                    else:
                        print("  No substantial difference in model performance between unsuspected OSA groups")
            
            # Create visualization
            if results:
                self._create_group_comparison_plot(results, 'Unsuspected OSA', 'unsuspected_osa_performance')
        else:
            print("Unsuspected OSA information not available for evaluation.")
    
    def _cross_validate(self, X, y, n_splits=5):
        """
        Perform cross-validation and return performance metrics.
        """
        # Initialize metrics storage
        aucs = []
        accuracies = []
        precisions = []
        recalls = []
        f1_scores = []
        
        # Initialize cross-validation
        skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
        
        # Perform cross-validation
        for train_index, test_index in skf.split(X, y):
            X_train, X_test = X.iloc[train_index], X.iloc[test_index]
            y_train, y_test = y.iloc[train_index], y.iloc[test_index]
            
            # Train model
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Check if model predicts more than one class
            proba = model.predict_proba(X_test)
            if proba.shape[1] > 1:  # If we have probabilities for multiple classes
                y_prob = proba[:, 1]  # Get probability of positive class
                
                # Calculate AUC if we have both classes in the test set
                if len(np.unique(y_test)) > 1:
                    aucs.append(roc_auc_score(y_test, y_prob))
                else:
                    aucs.append(np.nan)
            else:
                # Only one class was predicted
                aucs.append(np.nan)
            
            # Calculate other metrics
            accuracies.append(accuracy_score(y_test, y_pred))
            
            # Handle division by zero warnings
            if len(np.unique(y_test)) > 1:
                precisions.append(precision_score(y_test, y_pred))
                recalls.append(recall_score(y_test, y_pred))
                f1_scores.append(f1_score(y_test, y_pred))
            else:
                precisions.append(np.nan)
                recalls.append(np.nan)
                f1_scores.append(np.nan)
        
        # Calculate mean and standard deviation of metrics
        metrics = {
            'auc_mean': np.nanmean(aucs),
            'auc_std': np.nanstd(aucs),
            'accuracy_mean': np.mean(accuracies),
            'accuracy_std': np.std(accuracies),
            'precision_mean': np.nanmean(precisions),
            'precision_std': np.nanstd(precisions),
            'recall_mean': np.nanmean(recalls),
            'recall_std': np.nanstd(recalls),
            'f1_mean': np.nanmean(f1_scores),
            'f1_std': np.nanstd(f1_scores)
        }
        
        return metrics
    
    def _create_group_comparison_plot(self, results, category_name, filename_suffix):
        """
        Create a bar plot comparing model performance across groups.
        
        Args:
            results (list): List of dictionaries with performance metrics
            category_name (str): Name of the category being compared
            filename_suffix (str): Suffix for the output filename
        """
        # Convert results to DataFrame
        results_df = pd.DataFrame(results)
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Create bar plot for AUC
        ax = sns.barplot(x='Group', y='AUC', data=results_df, palette='viridis')
        
        # Add error bars
        for i, row in results_df.iterrows():
            ax.errorbar(i, row['AUC'], yerr=row['AUC_std'], fmt='none', color='black', capsize=5)
        
        # Add count labels on top of bars
        for i, row in results_df.iterrows():
            ax.text(i, row['AUC'] + row['AUC_std'] + 0.02, f"n={row['Count']}", ha='center')
        
        # Set labels and title
        plt.title(f'Model Performance (AUC) by {category_name}', fontsize=16)
        plt.xlabel(category_name, fontsize=14)
        plt.ylabel('AUC', fontsize=14)
        plt.ylim(0, 1.1)  # Set y-axis limits
        
        # Add grid
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Rotate x-axis labels if needed
        plt.xticks(rotation=45 if len(results) > 3 else 0, ha='right' if len(results) > 3 else 'center')
        
        # Tight layout
        plt.tight_layout()
        
        # Save figure
        plt.savefig(f'plots/model_performance_{filename_suffix}.png', dpi=300)
        plt.close()
        
        # Create a more comprehensive metrics comparison plot
        plt.figure(figsize=(14, 10))
        
        # Reshape data for plotting multiple metrics
        plot_data = []
        for _, row in results_df.iterrows():
            for metric in ['AUC', 'Accuracy', 'Precision', 'Recall', 'F1']:
                plot_data.append({
                    'Group': row['Group'],
                    'Metric': metric,
                    'Value': row[metric]
                })
        
        plot_df = pd.DataFrame(plot_data)
        
        # Create grouped bar plot
        sns.barplot(x='Group', y='Value', hue='Metric', data=plot_df, palette='tab10')
        
        # Set labels and title
        plt.title(f'Model Performance Metrics by {category_name}', fontsize=16)
        plt.xlabel(category_name, fontsize=14)
        plt.ylabel('Score', fontsize=14)
        plt.ylim(0, 1.1)  # Set y-axis limits
        
        # Add grid
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Rotate x-axis labels if needed
        plt.xticks(rotation=45 if len(results) > 3 else 0, ha='right' if len(results) > 3 else 'center')
        
        # Add legend
        plt.legend(title='Metric', bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Tight layout
        plt.tight_layout()
        
        # Save figure
        plt.savefig(f'plots/model_metrics_{filename_suffix}.png', dpi=300)
        plt.close()
    
    def _create_summary_visualization(self, df, model_cols):
        """
        Create a summary visualization of model performance across all groups.
        
        Args:
            df (DataFrame): The dataset
            model_cols (list): List of model feature columns
        """
        # Collect all results
        all_results = []
        
        # Gender results
        if 'Sex' in df.columns and df['Sex'].notna().any():
            for gender in ['männlich', 'weiblich']:
                gender_df = df[df['Sex'] == gender]
                if len(gender_df) > 10:
                    gender_features = [col for col in model_cols if col != 'is_male']
                    X = gender_df[gender_features]
                    y = gender_df['has_osa']
                    metrics = self._cross_validate(X, y)
                    all_results.append({
                        'Group': gender,
                        'Category': 'Gender',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Count': len(gender_df)
                    })
        
        # Age group results
        if 'Age' in df.columns and df['Age'].notna().any():
            # Age decades
            df['age_decade'] = (df['Age'] // 10 * 10).astype(int)
            for decade in sorted(df['age_decade'].unique()):
                decade_df = df[df['age_decade'] == decade]
                if len(decade_df) > 10:
                    X = decade_df[model_cols]
                    y = decade_df['has_osa']
                    metrics = self._cross_validate(X, y)
                    all_results.append({
                        'Group': f"{decade}s",
                        'Category': 'Age Decade',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Count': len(decade_df)
                    })
            
            # Age split at 50
            df['age_group'] = np.where(df['Age'] >= 50, '≥ 50', '< 50')
            for age_group in ['< 50', '≥ 50']:
                group_df = df[df['age_group'] == age_group]
                if len(group_df) > 10:
                    X = group_df[model_cols]
                    y = group_df['has_osa']
                    metrics = self._cross_validate(X, y)
                    all_results.append({
                        'Group': age_group,
                        'Category': 'Age Group',
                        'AUC': metrics['auc_mean'],
                        'AUC_std': metrics['auc_std'],
                        'Count': len(group_df)
                    })
        
        # OSA suspicion status results
        for col, category in [('has_suspected_osa', 'Suspected OSA'), 
                             ('has_unsuspected_osa', 'Unsuspected OSA')]:
            if col in df.columns:
                for status in [1, 0]:
                    status_label = "With" if status == 1 else "Without"
                    status_df = df[df[col] == status]
                    if len(status_df) > 10:
                        X = status_df[model_cols]
                        y = status_df['has_osa']
                        metrics = self._cross_validate(X, y)
                        all_results.append({
                            'Group': f"{status_label} {category}",
                            'Category': category,
                            'AUC': metrics['auc_mean'],
                            'AUC_std': metrics['auc_std'],
                            'Count': len(status_df)
                        })
        
        # Create overall summary visualization if we have results
        if all_results:
            # Convert to DataFrame
            summary_df = pd.DataFrame(all_results)
            
            # Create figure
            plt.figure(figsize=(16, 10))
            
            # Create grouped bar plot
            ax = sns.barplot(x='Group', y='AUC', hue='Category', data=summary_df, palette='tab10')
            
            # Add error bars
            for i, row in summary_df.iterrows():
                ax.errorbar(i, row['AUC'], yerr=row['AUC_std'], fmt='none', color='black', capsize=5)
            
            # Set labels and title
            plt.title('Model Performance (AUC) Across All Groups', fontsize=16)
            plt.xlabel('Group', fontsize=14)
            plt.ylabel('AUC', fontsize=14)
            plt.ylim(0, 1.1)  # Set y-axis limits
            
            # Add grid
            plt.grid(axis='y', linestyle='--', alpha=0.7)
            
            # Rotate x-axis labels
            plt.xticks(rotation=45, ha='right')
            
            # Add legend
            plt.legend(title='Category', bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # Tight layout
            plt.tight_layout()
            
            # Save figure
            plt.savefig('plots/model_performance_summary.png', dpi=300)
            plt.close()
            
            # Create a heatmap for easier comparison
            # Pivot the data
            pivot_df = summary_df.pivot(index='Group', columns='Category', values='AUC')
            
            # Create heatmap
            plt.figure(figsize=(12, 10))
            sns.heatmap(pivot_df, annot=True, cmap='viridis', vmin=0.5, vmax=1.0, 
                       linewidths=.5, cbar_kws={'label': 'AUC'})
            
            plt.title('Model Performance (AUC) Heatmap', fontsize=16)
            plt.tight_layout()
            
            # Save figure
            plt.savefig('plots/model_performance_heatmap.png', dpi=300)
            plt.close()

    def _evaluate_stop_bang_by_gender(self, df):
        """Evaluate STOP-BANG model performance by gender."""
        print("\n" + "-"*50)
        print("STOP-BANG MODEL PERFORMANCE BY GENDER")
        print("-"*50)

        if 'Sex' in df.columns and df['Sex'].notna().any():
            # Get unique genders (excluding any non-binary for this analysis)
            genders = df['Sex'].unique()
            genders = [g for g in genders if g in ['männlich', 'weiblich']]

            results = []

            # Evaluate for each gender
            for gender in genders:
                gender_df = df[df['Sex'] == gender]

                if len(gender_df) > 10:  # Only proceed if we have enough data
                    # Evaluate STOP-BANG performance
                    metrics = self._evaluate_stop_bang_performance(gender_df)

                    print(f"\nSTOP-BANG performance for {gender} patients (n={len(gender_df)}):")
                    print(f"  AUC: {metrics['auc']:.3f}")
                    print(f"  Best cutoff: ≥{metrics['best_cutoff']}")
                    print(f"  Sensitivity: {metrics['sensitivity']:.3f}")
                    print(f"  Specificity: {metrics['specificity']:.3f}")
                    print(f"  F1 Score: {metrics['f1']:.3f}")

                    # Store results for visualization
                    results.append({
                        'Group': gender,
                        'Category': 'Gender',
                        'AUC': metrics['auc'],
                        'Sensitivity': metrics['sensitivity'],
                        'Specificity': metrics['specificity'],
                        'F1': metrics['f1'],
                        'Best_Cutoff': metrics['best_cutoff'],
                        'Count': len(gender_df)
                    })
                else:
                    print(f"\nInsufficient data for {gender} patients (n={len(gender_df)}).")

            # Compare performance between genders
            if len(results) > 1:
                print("\nGender performance comparison:")
                male_metrics = next((r for r in results if r['Group'] == 'männlich'), None)
                female_metrics = next((r for r in results if r['Group'] == 'weiblich'), None)

                if male_metrics and female_metrics:
                    auc_diff = male_metrics['AUC'] - female_metrics['AUC']
                    print(f"  AUC difference (male - female): {auc_diff:.3f}")

                    if abs(auc_diff) > 0.05:
                        better_gender = "male" if auc_diff > 0 else "female"
                        print(f"  STOP-BANG performs better for {better_gender} patients")
                    else:
                        print("  No substantial difference in STOP-BANG performance between genders")

            # Create visualization
            if results:
                self._create_stop_bang_comparison_plot(results, 'Gender', 'stop_bang_gender_performance')
        else:
            print("Sex information not available for gender-based evaluation.")

    def _evaluate_stop_bang_performance(self, df):
        """
        Evaluate STOP-BANG performance for a given dataset.

        Args:
            df (DataFrame): Dataset with STOP-BANG scores

        Returns:
            dict: Performance metrics
        """
        from sklearn.metrics import roc_auc_score

        # Calculate AUC
        try:
            auc = roc_auc_score(df['has_osa'], df['partial_stop_bang_score'])
        except ValueError:
            # Handle case where only one class is present
            auc = 0.0

        # Find best cutoff by evaluating all possible cutoffs
        best_f1 = 0
        best_cutoff = 1
        best_metrics = {'sensitivity': 0, 'specificity': 0, 'f1': 0}

        for cutoff in range(1, 5):
            # Predicted positive if score >= cutoff
            y_pred = (df['partial_stop_bang_score'] >= cutoff).astype(int)

            # Calculate metrics
            tp = ((y_pred == 1) & (df['has_osa'] == 1)).sum()
            fp = ((y_pred == 1) & (df['has_osa'] == 0)).sum()
            tn = ((y_pred == 0) & (df['has_osa'] == 0)).sum()
            fn = ((y_pred == 0) & (df['has_osa'] == 1)).sum()

            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            ppv = tp / (tp + fp) if (tp + fp) > 0 else 0

            f1 = 2 * (ppv * sensitivity) / (ppv + sensitivity) if (ppv + sensitivity) > 0 else 0

            if f1 > best_f1:
                best_f1 = f1
                best_cutoff = cutoff
                best_metrics = {
                    'sensitivity': sensitivity,
                    'specificity': specificity,
                    'f1': f1
                }

        return {
            'auc': auc,
            'best_cutoff': best_cutoff,
            'sensitivity': best_metrics['sensitivity'],
            'specificity': best_metrics['specificity'],
            'f1': best_metrics['f1']
        }

    def _create_stop_bang_comparison_plot(self, results, category_name, filename_suffix):
        """
        Create a bar plot comparing STOP-BANG performance across groups.

        Args:
            results (list): List of dictionaries with performance metrics
            category_name (str): Name of the category being compared
            filename_suffix (str): Suffix for the output filename
        """
        # Convert results to DataFrame
        results_df = pd.DataFrame(results)

        # Create figure
        plt.figure(figsize=(12, 8))

        # Create bar plot for AUC
        ax = sns.barplot(x='Group', y='AUC', data=results_df, palette='viridis')

        # Add count labels on top of bars
        for i, row in results_df.iterrows():
            ax.text(i, row['AUC'] + 0.02, f"n={row['Count']}", ha='center')

        # Set labels and title
        plt.title(f'STOP-BANG Performance (AUC) by {category_name}', fontsize=16)
        plt.xlabel(category_name, fontsize=14)
        plt.ylabel('AUC', fontsize=14)
        plt.ylim(0, 1.1)  # Set y-axis limits

        # Add grid
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # Rotate x-axis labels if needed
        plt.xticks(rotation=45 if len(results) > 3 else 0, ha='right' if len(results) > 3 else 'center')

        # Tight layout
        plt.tight_layout()

        # Save figure
        plt.savefig(f'plots/{filename_suffix}.png', dpi=300)
        plt.close()

        # Create a more comprehensive metrics comparison plot
        plt.figure(figsize=(14, 10))

        # Reshape data for plotting multiple metrics
        plot_data = []
        for _, row in results_df.iterrows():
            for metric in ['AUC', 'Sensitivity', 'Specificity', 'F1']:
                plot_data.append({
                    'Group': row['Group'],
                    'Metric': metric,
                    'Value': row[metric]
                })

        plot_df = pd.DataFrame(plot_data)

        # Create grouped bar plot
        sns.barplot(x='Group', y='Value', hue='Metric', data=plot_df, palette='tab10')

        # Set labels and title
        plt.title(f'STOP-BANG Performance Metrics by {category_name}', fontsize=16)
        plt.xlabel(category_name, fontsize=14)
        plt.ylabel('Score', fontsize=14)
        plt.ylim(0, 1.1)  # Set y-axis limits

        # Add grid
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # Rotate x-axis labels if needed
        plt.xticks(rotation=45 if len(results) > 3 else 0, ha='right' if len(results) > 3 else 'center')

        # Add legend
        plt.legend(title='Metric', bbox_to_anchor=(1.05, 1), loc='upper left')

        # Tight layout
        plt.tight_layout()

        # Save figure
        plt.savefig(f'plots/{filename_suffix}_metrics.png', dpi=300)
        plt.close()

    def _evaluate_stop_bang_by_age_decades(self, df):
        """Evaluate STOP-BANG model performance by age decades."""
        print("\n" + "-"*50)
        print("STOP-BANG MODEL PERFORMANCE BY AGE DECADES")
        print("-"*50)

        if 'Age' in df.columns and df['Age'].notna().any():
            # Create age decade groups
            df['age_decade'] = (df['Age'] // 10 * 10).astype(int)
            df['age_decade'][df['age_decade'] < 20] = 20
            df['age_decade'][df['age_decade'] > 80] = 80

            # Get unique decades
            decades = sorted(df['age_decade'].unique())

            results = []

            # Evaluate for each decade
            for decade in decades:
                decade_df = df[df['age_decade'] == decade]

                if len(decade_df) > 10:  # Only proceed if we have enough data
                    # Evaluate STOP-BANG performance
                    metrics = self._evaluate_stop_bang_performance(decade_df)

                    decade_label = f"{decade}s"
                    if decade == 20:
                        decade_label += " or younger"
                    if decade == 80:
                        decade_label += " or older"

                    print(f"\nSTOP-BANG performance for patients in their {decade_label} (n={len(decade_df)}):")
                    print(f"  AUC: {metrics['auc']:.3f}")
                    print(f"  Best cutoff: ≥{metrics['best_cutoff']}")
                    print(f"  Sensitivity: {metrics['sensitivity']:.3f}")
                    print(f"  Specificity: {metrics['specificity']:.3f}")
                    print(f"  F1 Score: {metrics['f1']:.3f}")

                    # Store results for visualization
                    results.append({
                        'Group': decade_label,
                        'Category': 'Age Decade',
                        'AUC': metrics['auc'],
                        'Sensitivity': metrics['sensitivity'],
                        'Specificity': metrics['specificity'],
                        'F1': metrics['f1'],
                        'Best_Cutoff': metrics['best_cutoff'],
                        'Count': len(decade_df)
                    })
                else:
                    print(f"\nInsufficient data for patients in their {decade}s (n={len(decade_df)}).")

            # Create visualization
            if results:
                self._create_stop_bang_comparison_plot(results, 'Age Decade', 'stop_bang_age_decade_performance')
        else:
            print("Age information not available for decade-based evaluation.")

    def _evaluate_stop_bang_by_age_fifty(self, df):
        """Evaluate STOP-BANG model performance by age split at 50."""
        print("\n" + "-"*50)
        print("STOP-BANG MODEL PERFORMANCE BY AGE (SPLIT AT 50)")
        print("-"*50)

        if 'Age' in df.columns and df['Age'].notna().any():
            # Create age groups
            df['age_group'] = np.where(df['Age'] >= 50, '≥ 50', '< 50')

            results = []

            # Evaluate for each age group
            for age_group in ['< 50', '≥ 50']:
                group_df = df[df['age_group'] == age_group]

                if len(group_df) > 10:  # Only proceed if we have enough data
                    # Evaluate STOP-BANG performance
                    metrics = self._evaluate_stop_bang_performance(group_df)

                    print(f"\nSTOP-BANG performance for patients {age_group} years (n={len(group_df)}):")
                    print(f"  AUC: {metrics['auc']:.3f}")
                    print(f"  Best cutoff: ≥{metrics['best_cutoff']}")
                    print(f"  Sensitivity: {metrics['sensitivity']:.3f}")
                    print(f"  Specificity: {metrics['specificity']:.3f}")
                    print(f"  F1 Score: {metrics['f1']:.3f}")

                    # Store results for visualization
                    results.append({
                        'Group': age_group,
                        'Category': 'Age Group',
                        'AUC': metrics['auc'],
                        'Sensitivity': metrics['sensitivity'],
                        'Specificity': metrics['specificity'],
                        'F1': metrics['f1'],
                        'Best_Cutoff': metrics['best_cutoff'],
                        'Count': len(group_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {age_group} years (n={len(group_df)}).")

            # Compare performance between age groups
            if len(results) > 1:
                print("\nAge group performance comparison:")
                younger_metrics = next((r for r in results if r['Group'] == '< 50'), None)
                older_metrics = next((r for r in results if r['Group'] == '≥ 50'), None)

                if younger_metrics and older_metrics:
                    auc_diff = older_metrics['AUC'] - younger_metrics['AUC']
                    print(f"  AUC difference (≥50 - <50): {auc_diff:.3f}")

                    if abs(auc_diff) > 0.05:
                        better_group = "older (≥50)" if auc_diff > 0 else "younger (<50)"
                        print(f"  STOP-BANG performs better for {better_group} patients")
                    else:
                        print("  No substantial difference in STOP-BANG performance between age groups")

            # Create visualization
            if results:
                self._create_stop_bang_comparison_plot(results, 'Age Group', 'stop_bang_age_group_performance')
        else:
            print("Age information not available for age group evaluation.")

    def _evaluate_stop_bang_by_suspected_osa(self, df):
        """Evaluate STOP-BANG model performance by suspected OSA status."""
        print("\n" + "-"*50)
        print("STOP-BANG MODEL PERFORMANCE BY SUSPECTED OSA STATUS")
        print("-"*50)

        if 'has_suspected_osa' in df.columns:
            results = []

            # Evaluate for patients with and without suspected OSA
            for status in [1, 0]:
                status_label = "With" if status == 1 else "Without"
                status_df = df[df['has_suspected_osa'] == status]

                if len(status_df) > 10:  # Only proceed if we have enough data
                    # Evaluate STOP-BANG performance
                    metrics = self._evaluate_stop_bang_performance(status_df)

                    print(f"\nSTOP-BANG performance for patients {status_label.lower()} suspected OSA (n={len(status_df)}):")
                    print(f"  AUC: {metrics['auc']:.3f}")
                    print(f"  Best cutoff: ≥{metrics['best_cutoff']}")
                    print(f"  Sensitivity: {metrics['sensitivity']:.3f}")
                    print(f"  Specificity: {metrics['specificity']:.3f}")
                    print(f"  F1 Score: {metrics['f1']:.3f}")

                    # Store results for visualization
                    results.append({
                        'Group': f"{status_label} Suspected OSA",
                        'Category': 'Suspected OSA',
                        'AUC': metrics['auc'],
                        'Sensitivity': metrics['sensitivity'],
                        'Specificity': metrics['specificity'],
                        'F1': metrics['f1'],
                        'Best_Cutoff': metrics['best_cutoff'],
                        'Count': len(status_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {status_label.lower()} suspected OSA (n={len(status_df)}).")

            # Compare performance between groups
            if len(results) > 1:
                print("\nSuspected OSA status performance comparison:")
                with_metrics = next((r for r in results if r['Group'] == 'With Suspected OSA'), None)
                without_metrics = next((r for r in results if r['Group'] == 'Without Suspected OSA'), None)

                if with_metrics and without_metrics:
                    auc_diff = with_metrics['AUC'] - without_metrics['AUC']
                    print(f"  AUC difference (with - without): {auc_diff:.3f}")

                    if abs(auc_diff) > 0.05:
                        better_group = "with" if auc_diff > 0 else "without"
                        print(f"  STOP-BANG performs better for patients {better_group} suspected OSA")
                    else:
                        print("  No substantial difference in STOP-BANG performance between suspected OSA groups")

            # Create visualization
            if results:
                self._create_stop_bang_comparison_plot(results, 'Suspected OSA', 'stop_bang_suspected_osa_performance')
        else:
            print("Suspected OSA information not available for evaluation.")

    def _evaluate_stop_bang_by_unsuspected_osa(self, df):
        """Evaluate STOP-BANG model performance by unsuspected OSA status."""
        print("\n" + "-"*50)
        print("STOP-BANG MODEL PERFORMANCE BY UNSUSPECTED OSA STATUS")
        print("-"*50)

        if 'has_unsuspected_osa' in df.columns:
            results = []

            # Evaluate for patients with and without unsuspected OSA
            for status in [1, 0]:
                status_label = "With" if status == 1 else "Without"
                status_df = df[df['has_unsuspected_osa'] == status]

                if len(status_df) > 10:  # Only proceed if we have enough data
                    # Evaluate STOP-BANG performance
                    metrics = self._evaluate_stop_bang_performance(status_df)

                    print(f"\nSTOP-BANG performance for patients {status_label.lower()} unsuspected OSA (n={len(status_df)}):")
                    print(f"  AUC: {metrics['auc']:.3f}")
                    print(f"  Best cutoff: ≥{metrics['best_cutoff']}")
                    print(f"  Sensitivity: {metrics['sensitivity']:.3f}")
                    print(f"  Specificity: {metrics['specificity']:.3f}")
                    print(f"  F1 Score: {metrics['f1']:.3f}")

                    # Store results for visualization
                    results.append({
                        'Group': f"{status_label} Unsuspected OSA",
                        'Category': 'Unsuspected OSA',
                        'AUC': metrics['auc'],
                        'Sensitivity': metrics['sensitivity'],
                        'Specificity': metrics['specificity'],
                        'F1': metrics['f1'],
                        'Best_Cutoff': metrics['best_cutoff'],
                        'Count': len(status_df)
                    })
                else:
                    print(f"\nInsufficient data for patients {status_label.lower()} unsuspected OSA (n={len(status_df)}).")

            # Compare performance between groups
            if len(results) > 1:
                print("\nUnsuspected OSA status performance comparison:")
                with_metrics = next((r for r in results if r['Group'] == 'With Unsuspected OSA'), None)
                without_metrics = next((r for r in results if r['Group'] == 'Without Unsuspected OSA'), None)

                if with_metrics and without_metrics:
                    auc_diff = with_metrics['AUC'] - without_metrics['AUC']
                    print(f"  AUC difference (with - without): {auc_diff:.3f}")

                    if abs(auc_diff) > 0.05:
                        better_group = "with" if auc_diff > 0 else "without"
                        print(f"  STOP-BANG performs better for patients {better_group} unsuspected OSA")
                    else:
                        print("  No substantial difference in STOP-BANG performance between unsuspected OSA groups")

            # Create visualization
            if results:
                self._create_stop_bang_comparison_plot(results, 'Unsuspected OSA', 'stop_bang_unsuspected_osa_performance')
        else:
            print("Unsuspected OSA information not available for evaluation.")

    def _create_stop_bang_summary_visualization(self, df):
        """
        Create a summary visualization of STOP-BANG performance across all groups.

        Args:
            df (DataFrame): The dataset
        """
        # Collect all STOP-BANG results
        all_results = []

        # Gender results
        if 'Sex' in df.columns and df['Sex'].notna().any():
            for gender in ['männlich', 'weiblich']:
                gender_df = df[df['Sex'] == gender]
                if len(gender_df) > 10:
                    metrics = self._evaluate_stop_bang_performance(gender_df)
                    all_results.append({
                        'Group': gender,
                        'Category': 'Gender',
                        'AUC': metrics['auc'],
                        'Count': len(gender_df)
                    })

        # Age group results
        if 'Age' in df.columns and df['Age'].notna().any():
            # Age decades
            df['age_decade'] = (df['Age'] // 10 * 10).astype(int)
            for decade in sorted(df['age_decade'].unique()):
                decade_df = df[df['age_decade'] == decade]
                if len(decade_df) > 10:
                    metrics = self._evaluate_stop_bang_performance(decade_df)
                    all_results.append({
                        'Group': f"{decade}s",
                        'Category': 'Age Decade',
                        'AUC': metrics['auc'],
                        'Count': len(decade_df)
                    })

            # Age split at 50
            df['age_group'] = np.where(df['Age'] >= 50, '≥ 50', '< 50')
            for age_group in ['< 50', '≥ 50']:
                group_df = df[df['age_group'] == age_group]
                if len(group_df) > 10:
                    metrics = self._evaluate_stop_bang_performance(group_df)
                    all_results.append({
                        'Group': age_group,
                        'Category': 'Age Group',
                        'AUC': metrics['auc'],
                        'Count': len(group_df)
                    })

        # OSA suspicion status results
        for col, category in [('has_suspected_osa', 'Suspected OSA'),
                             ('has_unsuspected_osa', 'Unsuspected OSA')]:
            if col in df.columns:
                for status in [1, 0]:
                    status_label = "With" if status == 1 else "Without"
                    status_df = df[df[col] == status]
                    if len(status_df) > 10:
                        metrics = self._evaluate_stop_bang_performance(status_df)
                        all_results.append({
                            'Group': f"{status_label} {category}",
                            'Category': category,
                            'AUC': metrics['auc'],
                            'Count': len(status_df)
                        })

        # Create overall summary visualization if we have results
        if all_results:
            # Convert to DataFrame
            summary_df = pd.DataFrame(all_results)

            # Create figure
            plt.figure(figsize=(16, 10))

            # Create grouped bar plot
            ax = sns.barplot(x='Group', y='AUC', hue='Category', data=summary_df, palette='tab10')

            # Set labels and title
            plt.title('STOP-BANG Performance (AUC) Across All Groups', fontsize=16)
            plt.xlabel('Group', fontsize=14)
            plt.ylabel('AUC', fontsize=14)
            plt.ylim(0, 1.1)  # Set y-axis limits

            # Add grid
            plt.grid(axis='y', linestyle='--', alpha=0.7)

            # Rotate x-axis labels
            plt.xticks(rotation=45, ha='right')

            # Add legend
            plt.legend(title='Category', bbox_to_anchor=(1.05, 1), loc='upper left')

            # Tight layout
            plt.tight_layout()

            # Save figure
            plt.savefig('plots/stop_bang_performance_summary.png', dpi=300)
            plt.close()

            # Create a heatmap for easier comparison
            # Pivot the data
            pivot_df = summary_df.pivot(index='Group', columns='Category', values='AUC')

            # Create heatmap
            plt.figure(figsize=(12, 10))
            sns.heatmap(pivot_df, annot=True, cmap='viridis', vmin=0.5, vmax=1.0,
                       linewidths=.5, cbar_kws={'label': 'AUC'})

            plt.title('STOP-BANG Performance (AUC) Heatmap', fontsize=16)
            plt.tight_layout()

            # Save figure
            plt.savefig('plots/stop_bang_performance_heatmap.png', dpi=300)
            plt.close()

    def _compare_models(self, df, model_cols):
        """
        Compare Random Forest and STOP-BANG model performance.

        Args:
            df (DataFrame): The dataset
            model_cols (list): List of model feature columns
        """
        print("\n" + "="*50)
        print("RANDOM FOREST vs STOP-BANG MODEL COMPARISON")
        print("="*50)

        # Overall comparison
        print("\nOverall Performance Comparison:")

        # Random Forest performance
        X = df[model_cols]
        y = df['has_osa']
        rf_metrics = self._cross_validate(X, y)

        # STOP-BANG performance
        sb_metrics = self._evaluate_stop_bang_performance(df)

        print(f"\nRandom Forest Model:")
        print(f"  AUC: {rf_metrics['auc_mean']:.3f} ± {rf_metrics['auc_std']:.3f}")
        print(f"  Accuracy: {rf_metrics['accuracy_mean']:.3f} ± {rf_metrics['accuracy_std']:.3f}")
        print(f"  F1 Score: {rf_metrics['f1_mean']:.3f} ± {rf_metrics['f1_std']:.3f}")

        print(f"\nSTOP-BANG Model:")
        print(f"  AUC: {sb_metrics['auc']:.3f}")
        print(f"  Best cutoff: ≥{sb_metrics['best_cutoff']}")
        print(f"  Sensitivity: {sb_metrics['sensitivity']:.3f}")
        print(f"  Specificity: {sb_metrics['specificity']:.3f}")
        print(f"  F1 Score: {sb_metrics['f1']:.3f}")

        # Compare AUC
        auc_diff = rf_metrics['auc_mean'] - sb_metrics['auc']
        print(f"\nAUC Difference (Random Forest - STOP-BANG): {auc_diff:.3f}")

        if abs(auc_diff) > 0.05:
            better_model = "Random Forest" if auc_diff > 0 else "STOP-BANG"
            print(f"  {better_model} shows better overall performance")
        else:
            print("  No substantial difference in overall performance")

        # Create comparison visualization
        comparison_data = [
            {'Model': 'Random Forest', 'AUC': rf_metrics['auc_mean'], 'F1': rf_metrics['f1_mean']},
            {'Model': 'STOP-BANG', 'AUC': sb_metrics['auc'], 'F1': sb_metrics['f1']}
        ]

        comparison_df = pd.DataFrame(comparison_data)

        # Create side-by-side comparison plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # AUC comparison
        sns.barplot(x='Model', y='AUC', data=comparison_df, ax=ax1, palette=['skyblue', 'lightcoral'])
        ax1.set_title('AUC Comparison', fontsize=14)
        ax1.set_ylim(0, 1)
        ax1.grid(axis='y', linestyle='--', alpha=0.7)

        # F1 Score comparison
        sns.barplot(x='Model', y='F1', data=comparison_df, ax=ax2, palette=['skyblue', 'lightcoral'])
        ax2.set_title('F1 Score Comparison', fontsize=14)
        ax2.set_ylim(0, 1)
        ax2.grid(axis='y', linestyle='--', alpha=0.7)

        plt.suptitle('Random Forest vs STOP-BANG Model Performance', fontsize=16)
        plt.tight_layout()

        # Save figure
        plt.savefig('plots/model_comparison.png', dpi=300)
        plt.close()

        # Demographic-specific comparisons
        print("\n" + "-"*50)
        print("DEMOGRAPHIC-SPECIFIC MODEL COMPARISONS")
        print("-"*50)

        # Gender comparison
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print("\nGender-specific performance:")
            for gender in ['männlich', 'weiblich']:
                gender_df = df[df['Sex'] == gender]
                if len(gender_df) > 10:
                    # Random Forest
                    gender_features = [col for col in model_cols if col != 'is_male']
                    X_gender = gender_df[gender_features]
                    y_gender = gender_df['has_osa']
                    rf_gender_metrics = self._cross_validate(X_gender, y_gender)

                    # STOP-BANG
                    sb_gender_metrics = self._evaluate_stop_bang_performance(gender_df)

                    print(f"\n  {gender} patients:")
                    print(f"    Random Forest AUC: {rf_gender_metrics['auc_mean']:.3f}")
                    print(f"    STOP-BANG AUC: {sb_gender_metrics['auc']:.3f}")

                    gender_auc_diff = rf_gender_metrics['auc_mean'] - sb_gender_metrics['auc']
                    print(f"    Difference (RF - SB): {gender_auc_diff:.3f}")

        # Age group comparison
        if 'Age' in df.columns and df['Age'].notna().any():
            print("\nAge group-specific performance:")
            df['age_group'] = np.where(df['Age'] >= 50, '≥ 50', '< 50')
            for age_group in ['< 50', '≥ 50']:
                group_df = df[df['age_group'] == age_group]
                if len(group_df) > 10:
                    # Random Forest
                    X_age = group_df[model_cols]
                    y_age = group_df['has_osa']
                    rf_age_metrics = self._cross_validate(X_age, y_age)

                    # STOP-BANG
                    sb_age_metrics = self._evaluate_stop_bang_performance(group_df)

                    print(f"\n  Patients {age_group} years:")
                    print(f"    Random Forest AUC: {rf_age_metrics['auc_mean']:.3f}")
                    print(f"    STOP-BANG AUC: {sb_age_metrics['auc']:.3f}")

                    age_auc_diff = rf_age_metrics['auc_mean'] - sb_age_metrics['auc']
                    print(f"    Difference (RF - SB): {age_auc_diff:.3f}")

        print("\n" + "="*50)
        print("MODEL EVALUATION SUMMARY")
        print("="*50)
        print("\nBoth Random Forest and STOP-BANG models have been evaluated across")
        print("different demographic groups. Key findings:")
        print("- Performance varies across demographic subgroups")
        print("- Consider demographic-specific model selection for clinical use")
        print("- STOP-BANG offers simplicity and interpretability")
        print("- Random Forest may capture more complex patterns")
        print("\nRefer to the generated plots for detailed visualizations.")
