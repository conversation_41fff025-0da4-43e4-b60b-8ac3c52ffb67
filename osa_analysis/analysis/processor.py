"""
Data Processing Module
--------------------
Processes and merges data for OSA analysis.
"""

import pandas as pd
import numpy as np

class DataProcessor:
    """Processes and merges data for analysis."""
    
    def merge_data(self, sleep_study_patients, osa_patients, suspected_osa_patients, unsuspected_osa_patients, demographics, 
                  hypertension_patients, antihypertensive_patients, med_classes,
                  diabetes_patients, cardiovascular_patients, bmi_data):
        """
        Merge all data sources into a single DataFrame.
        
        Args:
            sleep_study_patients (DataFrame): Sleep study patient data
            osa_patients (DataFrame): OSA patient data
            demographics (DataFrame): Patient demographics
            hypertension_patients (DataFrame): Hypertension patient data
            antihypertensive_patients (DataFrame): Antihypertensive medication data
            med_classes (DataFrame): Medication class data
            diabetes_patients (DataFrame): Diabetes patient data
            cardiovascular_patients (DataFrame): Cardiovascular patient data
            bmi_data (DataFrame): BMI data
            
        Returns:
            DataFrame: Merged dataset
        """
        # Start with sleep study patients as base to ensure we have study dates
        df = sleep_study_patients.copy()
        
        # Convert dates to datetime
        df['sleep_study_date'] = pd.to_datetime(df['sleep_study_date'], errors='coerce')
        
        # Merge demographics
        demographics['Geburtsdatum'] = pd.to_datetime(demographics['Geburtsdatum'], errors='coerce')
        df = df.merge(demographics[['PID', 'Sex', 'Geburtsdatum']], on='PID', how='left')
        
        # Calculate age at time of sleep study
        df['Age'] = (df['sleep_study_date'] - df['Geburtsdatum']).dt.days / 365.25
        
        # Add OSA status
        df['has_osa'] = df['PID'].isin(osa_patients['PID']).astype(int)

        # Add suspected OSA status
        df['has_suspected_osa'] = df['PID'].isin(suspected_osa_patients['PID']).astype(int)
        
        # Add unsuspected OSA status
        df['has_unsuspected_osa'] = df['PID'].isin(unsuspected_osa_patients['PID']).astype(int)
        
        # Add hypertension status
        df['has_diagnosed_hypertension'] = df['PID'].isin(hypertension_patients['PID']).astype(int)
        
        # Add antihypertensive medication status
        df['on_antihypertensives'] = df['PID'].isin(antihypertensive_patients['PID']).astype(int)
        
        # Add other conditions
        df['has_diabetes'] = df['PID'].isin(diabetes_patients['PID']).astype(int)
        df['has_cardiovascular'] = df['PID'].isin(cardiovascular_patients['PID']).astype(int)
        
        # Merge BMI data
        df = df.merge(bmi_data[['PID', 'BMI']], on='PID', how='left')
        
        # Create combined hypertension variable
        df['has_hypertension'] = ((df['has_diagnosed_hypertension'] == 1) | 
                                 (df['on_antihypertensives'] == 1)).astype(int)
        
        return df
    
    def get_top_medications(self, med_data, osa_patients, top_n=10):
        """
        Get the top medications prescribed to OSA patients.
        
        Args:
            med_data (DataFrame): Medication data
            osa_patients (DataFrame): OSA patient data
            top_n (int): Number of top medications to return
            
        Returns:
            Series: Top medications and their counts
        """
        osa_meds = med_data[med_data['PID'].isin(osa_patients['PID'])]
        return osa_meds['ATCCode'].value_counts().head(top_n)
    
    def get_top_atc_codes(self, med_data, osa_patients, top_n=10):
        """
        Get the top ATC codes prescribed to OSA patients.
        
        Args:
            med_data (DataFrame): Medication data
            osa_patients (DataFrame): OSA patient data
            top_n (int): Number of top ATC codes to return
            
        Returns:
            Series: Top ATC codes and their counts
        """
        osa_meds = med_data[med_data['PID'].isin(osa_patients['PID'])]
        return osa_meds['ATCCode'].value_counts().head(top_n)
