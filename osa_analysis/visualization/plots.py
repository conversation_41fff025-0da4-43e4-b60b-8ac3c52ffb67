"""
Visualization Module
------------------
Creates visualizations for OSA analysis.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_curve, auc

class Visualizer:
    """Creates visualizations for OSA analysis."""
    
    def __init__(self):
        """Initialize the visualizer."""
        # Set up the plotting style
        plt.style.use('seaborn-v0_8-whitegrid')
        
        # Create a directory for plots if it doesn't exist
        if not os.path.exists('plots'):
            os.makedirs('plots')
    
    def create_visualizations(self, df, atc_classes):
        """
        Create visualizations for the analysis with sex-specific breakdowns.
        
        Args:
            df (DataFrame): The merged dataset
            atc_classes (dict): Dictionary of ATC classes
        """
        # Clean data for visualization
        df = df.copy()
        if 'Sex' in df.columns:
            df = df[df['Sex'] != 'divers']  # Remove diverse sex for binary analysis
            df = df.sort_values('has_osa')
        
        # Create demographic visualizations
        self._create_age_plots(df)
        self._create_comorbidity_plots(df)
        self._create_sex_plots(df)
        self._create_bmi_plots(df)
        self._create_medication_plots(df, atc_classes)
        self._create_feature_importance_plots(df, atc_classes)
        self._create_hypertension_venn_diagrams(df)
        self._create_osa_prevalence_plots(df)
        self._create_age_bmi_plots(df)
        
        print("\nVisualizations saved to 'plots' directory, including sex-specific analyses.")
    
    def create_stop_bang_visualizations(self, df):
        """
        Create visualizations for the STOP-BANG analysis.
        
        Args:
            df (DataFrame): The dataset with STOP-BANG scores
        """
        # 1. Distribution of partial STOP-BANG scores by OSA status
        plt.figure(figsize=(10, 6))
        sns.countplot(data=df, x='partial_stop_bang_score', hue='has_osa')
        plt.title('Distribution of Partial STOP-BANG Scores by OSA Status')
        plt.xlabel('Partial STOP-BANG Score (out of 4)')
        plt.ylabel('Number of Patients')
        plt.tight_layout()
        plt.savefig('plots/stop_bang_score_distribution.png')
        plt.close()
        
        # 2. OSA prevalence by STOP-BANG score
        prevalence_data = []
        for score in sorted(df['partial_stop_bang_score'].unique()):
            score_group = df[df['partial_stop_bang_score'] == score]
            osa_count = score_group['has_osa'].sum()
            total_count = len(score_group)
            prevalence = osa_count / total_count * 100
            prevalence_data.append({'Score': score, 'Prevalence': prevalence, 'Count': total_count})
        
        prevalence_df = pd.DataFrame(prevalence_data)
        
        plt.figure(figsize=(10, 6))
        ax = sns.barplot(data=prevalence_df, x='Score', y='Prevalence')
        
        # Add count labels on top of bars
        for i, row in prevalence_df.iterrows():
            ax.text(i, row['Prevalence'] + 2, f"n={row['Count']}", ha='center')
        
        plt.title('OSA Prevalence by Partial STOP-BANG Score')
        plt.xlabel('Partial STOP-BANG Score (out of 4)')
        plt.ylabel('OSA Prevalence (%)')
        plt.ylim(0, 100)
        plt.tight_layout()
        plt.savefig('plots/stop_bang_osa_prevalence.png')
        plt.close()
        
        # 3. ROC curve for partial STOP-BANG score
        fpr, tpr, thresholds = roc_curve(df['has_osa'], df['partial_stop_bang_score'])
        roc_auc = auc(fpr, tpr)
        
        plt.figure(figsize=(8, 8))
        plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.2f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curve for Partial STOP-BANG Score')
        plt.legend(loc="lower right")
        plt.tight_layout()
        plt.savefig('plots/stop_bang_roc_curve.png')
        plt.close()
        
        # 4. Component analysis
        self._create_stop_bang_component_plots(df)
        
        # 5. Sex-specific analysis
        self._create_stop_bang_sex_plots(df)
        
        # 6. Gender bias visualization
        self._create_stop_bang_gender_bias_plots(df)
        
        print("\nSTOP-BANG visualizations saved to 'plots' directory.")
    
    def _create_age_plots(self, df):
        """Create age distribution plots."""
        # Age distribution by OSA status and sex
        if 'Sex' in df.columns and df['Sex'].notna().any():
            plt.figure(figsize=(12, 8))
            for i, sex in enumerate(sorted(df['Sex'].unique())):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0:
                        sns.histplot(data=sex_df, x='Age', hue='has_osa', bins=20, 
                                    element='step', common_norm=False, stat='density',
                                    palette=['blue', 'red'], alpha=0.6, label=f'{sex}')
            
            plt.title('Age Distribution by OSA Status and Sex')
            plt.xlabel('Age (years)')
            plt.ylabel('Density')
            plt.savefig('plots/age_distribution_by_sex.png')
            plt.close()
        
        # Overall age distribution
        plt.figure(figsize=(10, 6))
        sns.histplot(data=df, x='Age', hue='has_osa', bins=20, element='step', common_norm=False, stat='density')
        plt.title('Age Distribution by OSA Status')
        plt.xlabel('Age (years)')
        plt.ylabel('Density')
        plt.savefig('plots/age_distribution.png')
        plt.close()
    
    def _create_comorbidity_plots(self, df):
        """Create comorbidity prevalence plots."""
        comorbidities = [
            ('Diagnosed Hypertension', 'has_diagnosed_hypertension'),
            ('Medication-implied Hypertension', 'on_antihypertensives'),
            ('Combined Hypertension', 'has_hypertension'),
            ('Diabetes', 'has_diabetes'),
            ('Cardiovascular', 'has_cardiovascular')
        ]
        
        if 'Sex' in df.columns and df['Sex'].notna().any():
            for sex in sorted(df['Sex'].unique()):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0:
                        prevalence_data = []
                        for name, col in comorbidities:
                            if col in sex_df.columns:
                                sex_osa_prev = sex_df[sex_df['has_osa'] == 1][col].mean() * 100
                                sex_non_osa_prev = sex_df[sex_df['has_osa'] == 0][col].mean() * 100
                                prevalence_data.append({'Condition': name, 'Group': 'OSA', 'Prevalence': sex_osa_prev})
                                prevalence_data.append({'Condition': name, 'Group': 'No OSA', 'Prevalence': sex_non_osa_prev})
                        
                        if prevalence_data:
                            prevalence_df = pd.DataFrame(prevalence_data)
                            
                            plt.figure(figsize=(14, 8))
                            sns.barplot(data=prevalence_df, x='Condition', y='Prevalence', hue='Group')
                            plt.title(f'Comorbidity Prevalence by OSA Status - {sex} Patients')
                            plt.xlabel('Condition')
                            plt.ylabel('Prevalence (%)')
                            plt.xticks(rotation=45, ha='right')
                            plt.tight_layout()
                            plt.savefig(f'plots/comorbidity_prevalence_{sex}.png')
                            plt.close()
        
        # Overall comorbidity prevalence plot
        prevalence_data = []
        for name, col in comorbidities:
            if col in df.columns:
                osa_prev = df[df['has_osa'] == 1][col].mean() * 100
                non_osa_prev = df[df['has_osa'] == 0][col].mean() * 100
                prevalence_data.append({'Condition': name, 'Group': 'OSA', 'Prevalence': osa_prev})
                prevalence_data.append({'Condition': name, 'Group': 'No OSA', 'Prevalence': non_osa_prev})
        
        if prevalence_data:
            prevalence_df = pd.DataFrame(prevalence_data)
            
            plt.figure(figsize=(14, 8))
            sns.barplot(data=prevalence_df, x='Condition', y='Prevalence', hue='Group')
            plt.title('Comorbidity Prevalence by OSA Status - All Patients')
            plt.xlabel('Condition')
            plt.ylabel('Prevalence (%)')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig('plots/comorbidity_prevalence.png')
            plt.close()
    
    def _create_sex_plots(self, df):
        """Create sex distribution plots."""
        if 'Sex' in df.columns and df['Sex'].notna().any():
            plt.figure(figsize=(8, 6))
            sex_counts = pd.crosstab(df['Sex'], df['has_osa'], normalize='columns') * 100
            sex_counts.plot(kind='bar', stacked=False)
            plt.title('Sex Distribution by OSA Status')
            plt.xlabel('Sex')
            plt.ylabel('Percentage (%)')
            plt.xticks(rotation=0)
            plt.tight_layout()
            plt.savefig('plots/sex_distribution.png')
            plt.close()
    
    def _create_bmi_plots(self, df):
        """Create BMI distribution plots."""
        if 'BMI' in df.columns and df['BMI'].notna().any() and 'Sex' in df.columns and df['Sex'].notna().any():
            plt.figure(figsize=(12, 8))
            for i, sex in enumerate(sorted(df['Sex'].unique())):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0 and sex_df['BMI'].notna().any():
                        plt.subplot(1, len(df['Sex'].unique()), i+1)
                        sns.boxplot(data=sex_df, x='has_osa', y='BMI')
                        plt.title(f'BMI Distribution - {sex} Patients')
                        plt.xlabel('OSA Status')
                        plt.ylabel('BMI')
                        plt.xticks([0, 1], ['No OSA', 'OSA'])
            
            plt.tight_layout()
            plt.savefig('plots/bmi_distribution_by_sex.png')
            plt.close()
        
        # Overall BMI distribution
        if 'BMI' in df.columns and df['BMI'].notna().any():
            plt.figure(figsize=(10, 6))
            sns.boxplot(data=df, x='has_osa', y='BMI')
            plt.title('BMI Distribution by OSA Status - All Patients')
            plt.xlabel('OSA Status')
            plt.ylabel('BMI')
            plt.xticks([0, 1], ['No OSA', 'OSA'])
            plt.tight_layout()
            plt.savefig('plots/bmi_distribution.png')
            plt.close()
    
    def _create_medication_plots(self, df, atc_classes):
        """Create medication use plots."""
        med_classes = []
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                med_classes.append((name, col_name))
        
        if med_classes and 'Sex' in df.columns and df['Sex'].notna().any():
            for sex in sorted(df['Sex'].unique()):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0:
                        med_data = []
                        for name, col in med_classes:
                            sex_osa_prev = sex_df[sex_df['has_osa'] == 1][col].mean() * 100
                            sex_non_osa_prev = sex_df[sex_df['has_osa'] == 0][col].mean() * 100
                            med_data.append({'Medication': name, 'Group': 'OSA', 'Prevalence': sex_osa_prev})
                            med_data.append({'Medication': name, 'Group': 'No OSA', 'Prevalence': sex_non_osa_prev})
                        
                        if med_data:
                            med_df = pd.DataFrame(med_data)
                            
                            plt.figure(figsize=(12, 7))
                            sns.barplot(data=med_df, x='Medication', y='Prevalence', hue='Group')
                            plt.title(f'Antihypertensive Medication Use by OSA Status - {sex} Patients')
                            plt.xlabel('Medication Class')
                            plt.ylabel('Prevalence (%)')
                            plt.xticks(rotation=45, ha='right')
                            plt.tight_layout()
                            plt.savefig(f'plots/antihypertensive_use_{sex}.png')
                            plt.close()
        
        # Overall medication use plot
        if med_classes:
            med_data = []
            for name, col in med_classes:
                osa_prev = df[df['has_osa'] == 1][col].mean() * 100
                non_osa_prev = df[df['has_osa'] == 0][col].mean() * 100
                med_data.append({'Medication': name, 'Group': 'OSA', 'Prevalence': osa_prev})
                med_data.append({'Medication': name, 'Group': 'No OSA', 'Prevalence': non_osa_prev})
            
            if med_data:
                med_df = pd.DataFrame(med_data)
                
                plt.figure(figsize=(12, 7))
                sns.barplot(data=med_df, x='Medication', y='Prevalence', hue='Group')
                plt.title('Antihypertensive Medication Use by OSA Status - All Patients')
                plt.xlabel('Medication Class')
                plt.ylabel('Prevalence (%)')
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                plt.savefig('plots/antihypertensive_use.png')
                plt.close()
    
    def _create_feature_importance_plots(self, df, atc_classes):
        """Create feature importance plots."""
        from sklearn.ensemble import RandomForestClassifier
        
        if 'has_osa' in df.columns and 'Sex' in df.columns and df['Sex'].notna().any():
            model_cols = ['Age', 'has_hypertension', 'has_diabetes', 
                         'has_cardiovascular', 'on_antihypertensives']
            
            # Add specific antihypertensive medication classes
            for name in atc_classes.values():
                col_name = f'on_{name.lower().replace("-", "_")}'
                if col_name in df.columns:
                    model_cols.append(col_name)
            
            # Add BMI if available
            if 'BMI' in df.columns and df['BMI'].notna().any():
                model_cols.append('BMI')
            
            for sex in sorted(df['Sex'].unique()):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0:
                        # Create is_male flag for consistency with overall model
                        sex_df['is_male'] = (sex_df['Sex'] == 'männlich').astype(int)
                        
                        # Filter rows with complete data
                        sex_model_df = sex_df[['has_osa'] + model_cols].dropna()
                        
                        if len(sex_model_df) > 10:  # Only proceed if we have enough data
                            # Remove is_male from features for sex-specific model
                            sex_features = [col for col in model_cols if col != 'is_male']
                            
                            X = sex_model_df[sex_features]
                            y = sex_model_df['has_osa']
                            
                            rf = RandomForestClassifier(n_estimators=100, random_state=42)
                            rf.fit(X, y)
                            
                            feature_importance = pd.DataFrame({
                                'Feature': sex_features,
                                'Importance': rf.feature_importances_
                            }).sort_values('Importance', ascending=False)
                            
                            plt.figure(figsize=(10, 8))
                            sns.barplot(data=feature_importance, x='Importance', y='Feature')
                            plt.title(f'Feature Importance for Predicting OSA - {sex} Patients')
                            plt.xlabel('Importance')
                            plt.ylabel('Feature')
                            plt.tight_layout()
                            plt.savefig(f'plots/feature_importance_{sex}.png')
                            plt.close()
        
        # Overall feature importance plot
        model_cols = ['Age', 'has_hypertension', 'has_diabetes',
                     'has_cardiovascular', 'on_antihypertensives']
        
        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                model_cols.append(col_name)
        
        # Add sex as dummy variable if available
        if 'Sex' in df.columns and df['Sex'].notna().any():
            df['is_male'] = (df['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')
        
        # Add BMI if available
        if 'BMI' in df.columns and df['BMI'].notna().any():
            model_cols.append('BMI')
        
        model_df = df[['has_osa'] + model_cols].dropna()
        
        if len(model_df) > 10:
            X = model_df[model_cols]
            y = model_df['has_osa']
            
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X, y)
            
            feature_importance = pd.DataFrame({
                'Feature': model_cols,
                'Importance': rf.feature_importances_
            }).sort_values('Importance', ascending=False)
            
            plt.figure(figsize=(10, 8))
            sns.barplot(data=feature_importance, x='Importance', y='Feature')
            plt.title('Feature Importance for Predicting OSA - All Patients')
            plt.xlabel('Importance')
            plt.ylabel('Feature')
            plt.tight_layout()
            plt.savefig('plots/feature_importance.png')
            plt.close()
    
    def _create_hypertension_venn_diagrams(self, df):
        """Create Venn diagrams of hypertension definitions."""
        try:
            from matplotlib_venn import venn2
            
            if 'has_diagnosed_hypertension' in df.columns and 'on_antihypertensives' in df.columns and 'Sex' in df.columns and df['Sex'].notna().any():
                for sex in sorted(df['Sex'].unique()):
                    if pd.notna(sex):
                        sex_df = df[df['Sex'] == sex]
                        if len(sex_df) > 0:
                            # Get sets of patients
                            diagnosed_set = set(sex_df[sex_df['has_diagnosed_hypertension'] == 1]['PID'])
                            medication_set = set(sex_df[sex_df['on_antihypertensives'] == 1]['PID'])
                            
                            if diagnosed_set or medication_set:  # Only create if we have data
                                plt.figure(figsize=(8, 6))
                                venn2([diagnosed_set, medication_set], 
                                    set_labels=('Diagnosed Hypertension', 'On Antihypertensive Medications'))
                                plt.title(f'Overlap Between Different Hypertension Definitions - {sex} Patients')
                                plt.tight_layout()
                                plt.savefig(f'plots/hypertension_venn_{sex}.png')
                                plt.close()
            
            # Overall Venn diagram
            if 'has_diagnosed_hypertension' in df.columns and 'on_antihypertensives' in df.columns:
                # Get sets of patients
                diagnosed_set = set(df[df['has_diagnosed_hypertension'] == 1]['PID'])
                medication_set = set(df[df['on_antihypertensives'] == 1]['PID'])
                
                plt.figure(figsize=(8, 6))
                venn2([diagnosed_set, medication_set], 
                    set_labels=('Diagnosed Hypertension', 'On Antihypertensive Medications'))
                plt.title('Overlap Between Different Hypertension Definitions - All Patients')
                plt.tight_layout()
                plt.savefig('plots/hypertension_venn.png')
                plt.close()
        except ImportError:
            print("matplotlib_venn not installed. Skipping Venn diagram.")
    
    def _create_osa_prevalence_plots(self, df):
        """Create OSA prevalence plots."""
        if 'Sex' in df.columns and df['Sex'].notna().any():
            sex_osa_counts = df.groupby('Sex')['has_osa'].sum()
            sex_total_counts = df.groupby('Sex').size()
            sex_osa_prevalence = (sex_osa_counts / sex_total_counts * 100).reset_index()
            sex_osa_prevalence.columns = ['Sex', 'OSA_Prevalence']
            
            plt.figure(figsize=(8, 6))
            sns.barplot(data=sex_osa_prevalence, x='Sex', y='OSA_Prevalence')
            plt.title('OSA Prevalence by Sex')
            plt.xlabel('Sex')
            plt.ylabel('Prevalence (%)')
            plt.tight_layout()
            plt.savefig('plots/osa_prevalence_by_sex.png')
            plt.close()
    
    def _create_age_bmi_plots(self, df):
        """Create age and BMI scatter plots."""
        if 'Age' in df.columns and 'BMI' in df.columns and df['BMI'].notna().any() and 'Sex' in df.columns and df['Sex'].notna().any():
            for sex in sorted(df['Sex'].unique()):
                if pd.notna(sex):
                    sex_df = df[df['Sex'] == sex]
                    if len(sex_df) > 0 and sex_df['BMI'].notna().any():
                        plt.figure(figsize=(10, 8))
                        sns.scatterplot(data=sex_df, x='Age', y='BMI', hue='has_osa', style='has_osa', s=100)
                        plt.title(f'Age and BMI Relationship with OSA - {sex} Patients')
                        plt.xlabel('Age (years)')
                        plt.ylabel('BMI')
                        plt.tight_layout()
                        plt.savefig(f'plots/age_bmi_osa_{sex}.png')
                        plt.close()
            
            # Overall scatter plot
            plt.figure(figsize=(10, 8))
            sns.scatterplot(data=df.dropna(subset=['Age', 'BMI']), x='Age', y='BMI', hue='has_osa', style='has_osa', s=100)
            plt.title('Age and BMI Relationship with OSA - All Patients')
            plt.xlabel('Age (years)')
            plt.ylabel('BMI')
            plt.tight_layout()
            plt.savefig('plots/age_bmi_osa.png')
            plt.close()
    
    def _create_stop_bang_component_plots(self, df):
        """Create STOP-BANG component analysis plots."""
        components = [
            ('BMI > 35', 'sb_bmi'),
            ('Age > 50', 'sb_age'),
            ('Male gender', 'sb_gender'),
            ('Hypertension', 'sb_pressure')
        ]
        
        component_data = []
        for name, col in components:
            # Calculate OSA prevalence for positive and negative cases
            positive_group = df[df[col] == 1]
            negative_group = df[df[col] == 0]
            
            pos_osa_count = positive_group['has_osa'].sum()
            pos_total = len(positive_group)
            pos_prevalence = pos_osa_count / pos_total * 100 if pos_total > 0 else 0
            
            neg_osa_count = negative_group['has_osa'].sum()
            neg_total = len(negative_group)
            neg_prevalence = neg_osa_count / neg_total * 100 if neg_total > 0 else 0
            
            component_data.append({'Component': name, 'Status': 'Positive', 'Prevalence': pos_prevalence, 'Count': pos_total})
            component_data.append({'Component': name, 'Status': 'Negative', 'Prevalence': neg_prevalence, 'Count': neg_total})
        
        component_df = pd.DataFrame(component_data)
        
        plt.figure(figsize=(12, 8))
        ax = sns.barplot(data=component_df, x='Component', y='Prevalence', hue='Status')
        
        # Add count labels on top of bars
        for i, row in component_df.iterrows():
            ax.text(i % 4 + (0.2 if row['Status'] == 'Positive' else -0.2), 
                    row['Prevalence'] + 2, 
                    f"n={row['Count']}", 
                    ha='center')
        
        plt.title('OSA Prevalence by STOP-BANG Components')
        plt.xlabel('STOP-BANG Component')
        plt.ylabel('OSA Prevalence (%)')
        plt.ylim(0, 100)
        plt.tight_layout()
        plt.savefig('plots/stop_bang_component_analysis.png')
        plt.close()
    
    def _create_stop_bang_sex_plots(self, df):
        """Create STOP-BANG sex-specific plots."""
        if 'Sex' in df.columns and df['Sex'].notna().any():
            # Filter out any non-binary sex categories for this analysis
            binary_sex_df = df[df['Sex'].isin(['männlich', 'weiblich'])]
            
            if len(binary_sex_df) > 0:
                plt.figure(figsize=(10, 6))
                sns.boxplot(data=binary_sex_df, x='Sex', y='partial_stop_bang_score', hue='has_osa')
                plt.title('Partial STOP-BANG Scores by Sex and OSA Status')
                plt.xlabel('Sex')
                plt.ylabel('Partial STOP-BANG Score')
                plt.tight_layout()
                plt.savefig('plots/stop_bang_by_sex.png')
                plt.close()
                
                # Score distribution by sex
                for sex in ['männlich', 'weiblich']:
                    sex_df = binary_sex_df[binary_sex_df['Sex'] == sex]
                    if len(sex_df) > 0:
                        plt.figure(figsize=(10, 6))
                        sns.countplot(data=sex_df, x='partial_stop_bang_score', hue='has_osa')
                        plt.title(f'Distribution of Partial STOP-BANG Scores - {sex} Patients')
                        plt.xlabel('Partial STOP-BANG Score (out of 4)')
                        plt.ylabel('Number of Patients')
                        plt.tight_layout()
                        plt.savefig(f'plots/stop_bang_score_distribution_{sex}.png')
                        plt.close()
                        
                        # ROC curve for this sex
                        fpr, tpr, thresholds = roc_curve(sex_df['has_osa'], sex_df['partial_stop_bang_score'])
                        roc_auc = auc(fpr, tpr)
                        
                        plt.figure(figsize=(8, 8))
                        plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.2f})')
                        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                        plt.xlim([0.0, 1.0])
                        plt.ylim([0.0, 1.05])
                        plt.xlabel('False Positive Rate')
                        plt.ylabel('True Positive Rate')
                        plt.title(f'ROC Curve for Partial STOP-BANG Score - {sex} Patients')
                        plt.legend(loc="lower right")
                        plt.tight_layout()
                        plt.savefig(f'plots/stop_bang_roc_curve_{sex}.png')
                        plt.close()
    
    def _create_stop_bang_gender_bias_plots(self, df):
        """Create STOP-BANG gender bias plots."""
        if 'Sex' in df.columns and df['Sex'].notna().any():
            binary_sex_df = df[df['Sex'].isin(['männlich', 'weiblich'])]
            if len(binary_sex_df) > 0:
                # ROC curves by gender
                plt.figure(figsize=(10, 8))
                
                for sex, color in zip(['männlich', 'weiblich'], ['blue', 'red']):
                    sex_df = binary_sex_df[binary_sex_df['Sex'] == sex]
                    if len(sex_df) > 0:
                        fpr, tpr, _ = roc_curve(sex_df['has_osa'], sex_df['partial_stop_bang_score'])
                        roc_auc = auc(fpr, tpr)
                        plt.plot(fpr, tpr, color=color, lw=2, 
                                label=f'{sex} (AUC = {roc_auc:.2f})')
                
                # Also plot without gender component
                binary_sex_df['score_without_gender'] = binary_sex_df['partial_stop_bang_score'] - binary_sex_df['sb_gender']
                fpr, tpr, _ = roc_curve(binary_sex_df['has_osa'], binary_sex_df['score_without_gender'])
                roc_auc = auc(fpr, tpr)
                plt.plot(fpr, tpr, color='green', lw=2, linestyle='--',
                        label=f'Without gender component (AUC = {roc_auc:.2f})')
                
                plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle=':')
                plt.xlim([0.0, 1.0])
                plt.ylim([0.0, 1.05])
                plt.xlabel('False Positive Rate')
                plt.ylabel('True Positive Rate')
                plt.title('ROC Curves by Gender - STOP-BANG Gender Bias Analysis')
                plt.legend(loc="lower right")
                plt.tight_layout()
                plt.savefig('plots/stop_bang_gender_bias_roc.png')
                plt.close()
