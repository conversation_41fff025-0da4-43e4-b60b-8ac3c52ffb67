"""
Distribution Analysis Script
---------------------------
Creates distribution plots for age, BMI, top medications, and top diagnoses
from the patient management database.
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import Counter
import os

class DistributionPlotter:
    """Creates distribution plots from patient database."""
    
    def __init__(self, db_path='../patient_management.db'):
        """Initialize with database path."""
        self.db_path = db_path
        self.conn = None
        
    def connect_db(self):
        """Connect to the SQLite database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"Connected to database: {self.db_path}")
            return True
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            return False
    
    def close_db(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            print("Database connection closed.")
    
    def load_patient_data(self):
        """Load patient demographic data (age, BMI)."""
        query = """
        SELECT 
            PID,
            AdministrativesGeschlecht as Sex,
            Geburtsdatum,
            CASE 
                WHEN Geburtsdatum IS NOT NULL 
                THEN CAST((julianday('now') - julianday(Geburtsdatum)) / 365.25 AS INTEGER)
                ELSE NULL 
            END as Age
        FROM Patient
        WHERE Geburtsdatum IS NOT NULL
        """
        
        try:
            df = pd.read_sql_query(query, self.conn)
            print(f"Loaded {len(df)} patient records")
            return df
        except Exception as e:
            print(f"Error loading patient data: {e}")
            return pd.DataFrame()
    
    def load_vital_data(self):
        """Load BMI data from vital signs."""
        query = """
        SELECT 
            v.PID,
            v.Vitalparameter,
            v.Vitalwert,
            v.Masseinheit
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%BMI%' 
           OR v.Vitalparameter LIKE '%Body Mass Index%'
           OR v.Vitalparameter LIKE '%Körpergewicht%'
           OR v.Vitalparameter LIKE '%Größe%'
           OR v.Vitalparameter LIKE '%Height%'
           OR v.Vitalparameter LIKE '%Weight%'
        """
        
        try:
            df = pd.read_sql_query(query, self.conn)
            print(f"Loaded {len(df)} vital sign records")
            return df
        except Exception as e:
            print(f"Error loading vital data: {e}")
            return pd.DataFrame()
    
    def calculate_bmi_from_vitals(self, vitals_df):
        """Calculate BMI from height and weight measurements."""
        bmi_data = []
        
        # Group by patient
        for pid in vitals_df['PID'].unique():
            patient_vitals = vitals_df[vitals_df['PID'] == pid]
            
            height = None
            weight = None
            bmi = None
            
            for _, row in patient_vitals.iterrows():
                param = str(row['Vitalparameter']).lower()
                value = row['Vitalwert']
                
                # Try to extract numeric value
                try:
                    if isinstance(value, str):
                        # Extract first number from string
                        import re
                        numbers = re.findall(r'\d+\.?\d*', value)
                        if numbers:
                            value = float(numbers[0])
                        else:
                            continue
                    else:
                        value = float(value)
                except (ValueError, TypeError):
                    continue
                
                # Identify parameter type
                if 'bmi' in param or 'body mass index' in param:
                    bmi = value
                elif any(word in param for word in ['größe', 'height', 'körpergröße']):
                    # Convert cm to m if necessary
                    if value > 3:  # Assume cm if > 3
                        height = value / 100
                    else:
                        height = value
                elif any(word in param for word in ['gewicht', 'weight', 'körpergewicht']):
                    weight = value
            
            # Calculate BMI if we have height and weight but no direct BMI
            if bmi is None and height is not None and weight is not None and height > 0:
                bmi = weight / (height ** 2)
            
            if bmi is not None and 10 <= bmi <= 80:  # Reasonable BMI range
                bmi_data.append({'PID': pid, 'BMI': bmi})
        
        return pd.DataFrame(bmi_data)
    
    def load_medication_data(self):
        """Load medication data."""
        query = """
        SELECT 
            PID,
            Medikationsbezeichnung,
            ATCCode,
            ATCBezeichnung,
            COUNT(*) as frequency
        FROM Medikation
        WHERE Medikationsbezeichnung IS NOT NULL
        GROUP BY PID, Medikationsbezeichnung, ATCCode
        """
        
        try:
            df = pd.read_sql_query(query, self.conn)
            print(f"Loaded {len(df)} medication records")
            return df
        except Exception as e:
            print(f"Error loading medication data: {e}")
            return pd.DataFrame()
    
    def load_diagnosis_data(self):
        """Load diagnosis data."""
        query = """
        SELECT 
            PID,
            PrimaerdiagnoseICD,
            PrimaerdiagnoseText,
            SekundaerdiagnoseICD,
            SekundaerdiagnoseText,
            COUNT(*) as frequency
        FROM Diagnosen
        WHERE PrimaerdiagnoseText IS NOT NULL OR SekundaerdiagnoseText IS NOT NULL
        GROUP BY PID, PrimaerdiagnoseICD, PrimaerdiagnoseText
        """
        
        try:
            df = pd.read_sql_query(query, self.conn)
            print(f"Loaded {len(df)} diagnosis records")
            return df
        except Exception as e:
            print(f"Error loading diagnosis data: {e}")
            return pd.DataFrame()
    
    def plot_age_distribution(self, patient_df, save_path='plots/age_distribution.png'):
        """Plot age distribution."""
        if patient_df.empty or 'Age' not in patient_df.columns:
            print("No age data available for plotting")
            return
        
        # Filter out invalid ages
        valid_ages = patient_df['Age'].dropna()
        valid_ages = valid_ages[(valid_ages >= 0) & (valid_ages <= 120)]
        
        if len(valid_ages) == 0:
            print("No valid age data available")
            return
        
        plt.figure(figsize=(12, 6))
        
        # Create subplot with histogram and box plot
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), height_ratios=[3, 1])
        
        # Histogram
        ax1.hist(valid_ages, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('Age (years)')
        ax1.set_ylabel('Number of Patients')
        ax1.set_title(f'Age Distribution (n={len(valid_ages)})')
        ax1.grid(True, alpha=0.3)
        
        # Add statistics
        mean_age = valid_ages.mean()
        median_age = valid_ages.median()
        std_age = valid_ages.std()
        
        ax1.axvline(mean_age, color='red', linestyle='--', label=f'Mean: {mean_age:.1f}')
        ax1.axvline(median_age, color='green', linestyle='--', label=f'Median: {median_age:.1f}')
        ax1.legend()
        
        # Box plot
        ax2.boxplot(valid_ages, vert=False, patch_artist=True, 
                   boxprops=dict(facecolor='lightblue', alpha=0.7))
        ax2.set_xlabel('Age (years)')
        ax2.set_title('Age Distribution Box Plot')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Age distribution plot saved to: {save_path}")
        plt.close()
        
        # Print statistics
        print(f"\nAge Statistics:")
        print(f"  Count: {len(valid_ages)}")
        print(f"  Mean: {mean_age:.2f} years")
        print(f"  Median: {median_age:.2f} years")
        print(f"  Standard deviation: {std_age:.2f} years")
        print(f"  Range: {valid_ages.min():.0f} - {valid_ages.max():.0f} years")
    
    def plot_bmi_distribution(self, bmi_df, save_path='plots/bmi_distribution.png'):
        """Plot BMI distribution."""
        if bmi_df.empty or 'BMI' not in bmi_df.columns:
            print("No BMI data available for plotting")
            return
        
        # Filter out invalid BMI values
        valid_bmi = bmi_df['BMI'].dropna()
        valid_bmi = valid_bmi[(valid_bmi >= 10) & (valid_bmi <= 80)]
        
        if len(valid_bmi) == 0:
            print("No valid BMI data available")
            return
        
        plt.figure(figsize=(12, 10))
        
        # Create subplot with histogram and box plot
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), height_ratios=[3, 1])
        
        # Histogram
        ax1.hist(valid_bmi, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
        ax1.set_xlabel('BMI (kg/m²)')
        ax1.set_ylabel('Number of Patients')
        ax1.set_title(f'BMI Distribution (n={len(valid_bmi)})')
        ax1.grid(True, alpha=0.3)
        
        # Add BMI category lines
        ax1.axvline(18.5, color='blue', linestyle=':', alpha=0.7, label='Underweight')
        ax1.axvline(25, color='green', linestyle=':', alpha=0.7, label='Normal')
        ax1.axvline(30, color='orange', linestyle=':', alpha=0.7, label='Overweight')
        ax1.axvline(35, color='red', linestyle=':', alpha=0.7, label='Obese')
        
        # Add statistics
        mean_bmi = valid_bmi.mean()
        median_bmi = valid_bmi.median()
        
        ax1.axvline(mean_bmi, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_bmi:.1f}')
        ax1.axvline(median_bmi, color='darkgreen', linestyle='--', linewidth=2, label=f'Median: {median_bmi:.1f}')
        ax1.legend()
        
        # Box plot
        ax2.boxplot(valid_bmi, vert=False, patch_artist=True, 
                   boxprops=dict(facecolor='lightcoral', alpha=0.7))
        ax2.set_xlabel('BMI (kg/m²)')
        ax2.set_title('BMI Distribution Box Plot')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"BMI distribution plot saved to: {save_path}")
        plt.close()
        
        # Print statistics and categories
        print(f"\nBMI Statistics:")
        print(f"  Count: {len(valid_bmi)}")
        print(f"  Mean: {mean_bmi:.2f} kg/m²")
        print(f"  Median: {median_bmi:.2f} kg/m²")
        print(f"  Standard deviation: {valid_bmi.std():.2f} kg/m²")
        print(f"  Range: {valid_bmi.min():.1f} - {valid_bmi.max():.1f} kg/m²")
        
        # BMI categories
        underweight = len(valid_bmi[valid_bmi < 18.5])
        normal = len(valid_bmi[(valid_bmi >= 18.5) & (valid_bmi < 25)])
        overweight = len(valid_bmi[(valid_bmi >= 25) & (valid_bmi < 30)])
        obese = len(valid_bmi[valid_bmi >= 30])
        
        print(f"\nBMI Categories:")
        print(f"  Underweight (<18.5): {underweight} ({underweight/len(valid_bmi)*100:.1f}%)")
        print(f"  Normal (18.5-24.9): {normal} ({normal/len(valid_bmi)*100:.1f}%)")
        print(f"  Overweight (25-29.9): {overweight} ({overweight/len(valid_bmi)*100:.1f}%)")
        print(f"  Obese (≥30): {obese} ({obese/len(valid_bmi)*100:.1f}%)")
    
    def plot_top_medications(self, med_df, save_path='plots/top_medications.png'):
        """Plot top 10 medications."""
        if med_df.empty:
            print("No medication data available for plotting")
            return
        
        # Count medication frequencies
        med_counts = Counter()
        
        for _, row in med_df.iterrows():
            med_name = row['Medikationsbezeichnung']
            if pd.notna(med_name) and med_name.strip():
                # Clean medication name
                med_name = med_name.strip().title()
                med_counts[med_name] += row['frequency']
        
        if not med_counts:
            print("No valid medication data found")
            return
        
        # Get top 10
        top_meds = med_counts.most_common(10)
        
        medications = [med[0] for med in top_meds]
        counts = [med[1] for med in top_meds]
        
        plt.figure(figsize=(12, 8))
        bars = plt.barh(range(len(medications)), counts, color='lightgreen', alpha=0.8)
        
        # Customize plot
        plt.yticks(range(len(medications)), medications)
        plt.xlabel('Number of Prescriptions')
        plt.title('Top 10 Medications')
        plt.grid(True, alpha=0.3, axis='x')
        
        # Add value labels on bars
        for i, (bar, count) in enumerate(zip(bars, counts)):
            plt.text(bar.get_width() + max(counts) * 0.01, bar.get_y() + bar.get_height()/2, 
                    str(count), ha='left', va='center')
        
        # Invert y-axis to show highest at top
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Top medications plot saved to: {save_path}")
        plt.close()
        
        # Print top medications
        print(f"\nTop 10 Medications:")
        for i, (med, count) in enumerate(top_meds, 1):
            print(f"  {i:2d}. {med}: {count} prescriptions")
    
    def plot_top_diagnoses(self, diag_df, save_path='plots/top_diagnoses.png'):
        """Plot top 10 diagnoses."""
        if diag_df.empty:
            print("No diagnosis data available for plotting")
            return
        
        # Count diagnosis frequencies
        diag_counts = Counter()
        
        for _, row in diag_df.iterrows():
            # Primary diagnosis
            if pd.notna(row['PrimaerdiagnoseText']) and row['PrimaerdiagnoseText'].strip():
                diag_text = row['PrimaerdiagnoseText'].strip().title()
                icd_code = row['PrimaerdiagnoseICD'] if pd.notna(row['PrimaerdiagnoseICD']) else ''
                
                # Combine ICD code and text if available
                if icd_code:
                    diag_label = f"{icd_code}: {diag_text}"
                else:
                    diag_label = diag_text
                
                diag_counts[diag_label] += row['frequency']
            
            # Secondary diagnosis
            if pd.notna(row['SekundaerdiagnoseText']) and row['SekundaerdiagnoseText'].strip():
                diag_text = row['SekundaerdiagnoseText'].strip().title()
                icd_code = row['SekundaerdiagnoseICD'] if pd.notna(row['SekundaerdiagnoseICD']) else ''
                
                # Combine ICD code and text if available
                if icd_code:
                    diag_label = f"{icd_code}: {diag_text}"
                else:
                    diag_label = diag_text
                
                diag_counts[diag_label] += row['frequency']
        
        if not diag_counts:
            print("No valid diagnosis data found")
            return
        
        # Get top 10
        top_diagnoses = diag_counts.most_common(10)
        
        diagnoses = [diag[0] for diag in top_diagnoses]
        counts = [diag[1] for diag in top_diagnoses]
        
        # Truncate long diagnosis names for display
        display_diagnoses = []
        for diag in diagnoses:
            if len(diag) > 50:
                display_diagnoses.append(diag[:47] + "...")
            else:
                display_diagnoses.append(diag)
        
        plt.figure(figsize=(14, 8))
        bars = plt.barh(range(len(display_diagnoses)), counts, color='lightblue', alpha=0.8)
        
        # Customize plot
        plt.yticks(range(len(display_diagnoses)), display_diagnoses)
        plt.xlabel('Number of Diagnoses')
        plt.title('Top 10 Diagnoses')
        plt.grid(True, alpha=0.3, axis='x')
        
        # Add value labels on bars
        for i, (bar, count) in enumerate(zip(bars, counts)):
            plt.text(bar.get_width() + max(counts) * 0.01, bar.get_y() + bar.get_height()/2, 
                    str(count), ha='left', va='center')
        
        # Invert y-axis to show highest at top
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Top diagnoses plot saved to: {save_path}")
        plt.close()
        
        # Print top diagnoses
        print(f"\nTop 10 Diagnoses:")
        for i, (diag, count) in enumerate(top_diagnoses, 1):
            print(f"  {i:2d}. {diag}: {count} cases")
    
    def create_summary_dashboard(self, patient_df, bmi_df, med_df, diag_df, save_path='plots/summary_dashboard.png'):
        """Create a summary dashboard with all distributions."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Age distribution
        if not patient_df.empty and 'Age' in patient_df.columns:
            valid_ages = patient_df['Age'].dropna()
            valid_ages = valid_ages[(valid_ages >= 0) & (valid_ages <= 120)]
            if len(valid_ages) > 0:
                ax1.hist(valid_ages, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                ax1.set_xlabel('Age (years)')
                ax1.set_ylabel('Number of Patients')
                ax1.set_title(f'Age Distribution (n={len(valid_ages)})')
                ax1.grid(True, alpha=0.3)
        
        # BMI distribution
        if not bmi_df.empty and 'BMI' in bmi_df.columns:
            valid_bmi = bmi_df['BMI'].dropna()
            valid_bmi = valid_bmi[(valid_bmi >= 10) & (valid_bmi <= 80)]
            if len(valid_bmi) > 0:
                ax2.hist(valid_bmi, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
                ax2.set_xlabel('BMI (kg/m²)')
                ax2.set_ylabel('Number of Patients')
                ax2.set_title(f'BMI Distribution (n={len(valid_bmi)})')
                ax2.grid(True, alpha=0.3)
        
        # Top 5 medications
        if not med_df.empty:
            med_counts = Counter()
            for _, row in med_df.iterrows():
                med_name = row['Medikationsbezeichnung']
                if pd.notna(med_name) and med_name.strip():
                    med_name = med_name.strip().title()
                    med_counts[med_name] += row['frequency']
            
            if med_counts:
                top_meds = med_counts.most_common(5)
                medications = [med[0][:20] + "..." if len(med[0]) > 20 else med[0] for med in top_meds]
                counts = [med[1] for med in top_meds]
                
                ax3.barh(range(len(medications)), counts, color='lightgreen', alpha=0.8)
                ax3.set_yticks(range(len(medications)))
                ax3.set_yticklabels(medications)
                ax3.set_xlabel('Number of Prescriptions')
                ax3.set_title('Top 5 Medications')
                ax3.grid(True, alpha=0.3, axis='x')
                ax3.invert_yaxis()
        
        # Top 5 diagnoses
        if not diag_df.empty:
            diag_counts = Counter()
            for _, row in diag_df.iterrows():
                if pd.notna(row['PrimaerdiagnoseText']) and row['PrimaerdiagnoseText'].strip():
                    diag_text = row['PrimaerdiagnoseText'].strip().title()
                    diag_counts[diag_text] += row['frequency']
            
            if diag_counts:
                top_diagnoses = diag_counts.most_common(5)
                diagnoses = [diag[0][:25] + "..." if len(diag[0]) > 25 else diag[0] for diag in top_diagnoses]
                counts = [diag[1] for diag in top_diagnoses]
                
                ax4.barh(range(len(diagnoses)), counts, color='lightblue', alpha=0.8)
                ax4.set_yticks(range(len(diagnoses)))
                ax4.set_yticklabels(diagnoses)
                ax4.set_xlabel('Number of Diagnoses')
                ax4.set_title('Top 5 Diagnoses')
                ax4.grid(True, alpha=0.3, axis='x')
                ax4.invert_yaxis()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Summary dashboard saved to: {save_path}")
        plt.close()
    
    def run_analysis(self):
        """Run complete distribution analysis."""
        print("="*60)
        print("PATIENT DATABASE DISTRIBUTION ANALYSIS")
        print("="*60)
        
        # Create plots directory
        os.makedirs('plots', exist_ok=True)
        
        # Connect to database
        if not self.connect_db():
            return
        
        try:
            # Load data
            print("\nLoading data from database...")
            patient_df = self.load_patient_data()
            vitals_df = self.load_vital_data()
            med_df = self.load_medication_data()
            diag_df = self.load_diagnosis_data()
            
            # Process BMI data
            print("\nProcessing BMI data...")
            bmi_df = self.calculate_bmi_from_vitals(vitals_df)
            
            # Create plots
            print("\nGenerating distribution plots...")
            self.plot_age_distribution(patient_df)
            self.plot_bmi_distribution(bmi_df)
            self.plot_top_medications(med_df)
            self.plot_top_diagnoses(diag_df)
            self.create_summary_dashboard(patient_df, bmi_df, med_df, diag_df)
            
            print("\n" + "="*60)
            print("ANALYSIS COMPLETE")
            print("="*60)
            print("Generated plots:")
            print("  - plots/age_distribution.png")
            print("  - plots/bmi_distribution.png") 
            print("  - plots/top_medications.png")
            print("  - plots/top_diagnoses.png")
            print("  - plots/summary_dashboard.png")
            
        except Exception as e:
            print(f"Error during analysis: {e}")
        finally:
            self.close_db()


def main():
    """Main function to run the distribution analysis."""
    plotter = DistributionPlotter('../patient_management.db')
    plotter.run_analysis()


if __name__ == "__main__":
    main()
