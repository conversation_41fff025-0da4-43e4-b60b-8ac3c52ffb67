import pandas as pd
import sqlite3
import os

def clean_column_names(df):
    """Clean column names to match database column names"""
    # Replace spaces and special characters with standard format
    df.columns = df.columns.str.replace(' ', '')
    df.columns = df.columns.str.replace('/', '')
    df.columns = df.columns.str.replace('-', '')
    df.columns = df.columns.str.replace('(', '')
    df.columns = df.columns.str.replace(')', '')
    df.columns = df.columns.str.replace('.', '')
    df.columns = df.columns.str.replace('ä', 'ae')
    df.columns = df.columns.str.replace('ö', 'oe')
    df.columns = df.columns.str.replace('ü', 'ue')
    df.columns = df.columns.str.replace('ß', 'ss')
    return df

def load_excel_to_sqlite(excel_path, db_path):
    """Load data from Excel file into SQLite database"""
    print(f"Loading data from {excel_path} into {db_path}...")
    
    # Check if Excel file exists
    if not os.path.exists(excel_path):
        print(f"Error: Excel file {excel_path} not found.")
        return
    
    # Connect to SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Map Excel sheet names to database table names
    sheet_to_table = {
        'Patient': 'Patient',
        'Fall': 'Fall',
        'Diagnosen': 'Diagnosen',
        'Prozeduren': 'Prozeduren',
        'Vitaldaten': 'Vitaldaten',
        'Medikation': 'Medikation',
        'LSTM': 'LSTM'
    }
    
    # Load each sheet and insert into corresponding table
    try:
        # Get all sheet names
        excel = pd.ExcelFile(excel_path)
        sheet_names = excel.sheet_names
        
        for sheet_name in sheet_names:
            if sheet_name in sheet_to_table:
                table_name = sheet_to_table[sheet_name]
                print(f"Processing sheet: {sheet_name} -> table: {table_name}")
                
                # Read the sheet
                df = pd.read_excel(excel, sheet_name=sheet_name)
                
                # Clean column names
                df = clean_column_names(df)
                
                # Handle date columns
                date_columns = [col for col in df.columns if any(date_term in col.lower() for date_term in ['datum', 'tag', 'zeitpunkt', 'termin'])]
                for col in date_columns:
                    if col in df.columns:
                        df[col] = pd.to_datetime(df[col], errors='coerce')
                
                # Convert boolean columns
                if 'PatientVerstorben' in df.columns:
                    df['PatientVerstorben'] = df['PatientVerstorben'].astype(bool)
                if 'Durchgefuehrt' in df.columns:
                    df['Durchgefuehrt'] = df['Durchgefuehrt'].astype(bool)
                
                # Get column names from the database table
                cursor.execute(f"PRAGMA table_info({table_name})")
                db_columns = [row[1] for row in cursor.fetchall()]
                
                # Filter dataframe to only include columns that exist in the database
                common_columns = [col for col in df.columns if col in db_columns]
                df_filtered = df[common_columns]
                
                # Insert data into the table
                df_filtered.to_sql(table_name, conn, if_exists='append', index=False)
                
                print(f"Successfully inserted {len(df_filtered)} rows into {table_name}")
            else:
                print(f"Warning: No mapping found for sheet {sheet_name}")
        
        print("Data loading completed successfully!")
    
    except Exception as e:
        print(f"Error loading data: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    # Prompt user for Excel file path or use default
    excel_path = input("Enter the path to your Excel file (or press Enter for default '200325_SOM_WP3.xlsx'): ")
    if not excel_path:
        excel_path = "database/200325_SOM_WP3.xlsx"
    
    # Use the database created earlier
    db_path = "../patient_management_pseudo.db"
    
    # Load data from Excel to SQLite
    load_excel_to_sqlite(excel_path, db_path)
