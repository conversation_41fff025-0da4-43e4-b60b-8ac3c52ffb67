Ich hab jetzt alles mit biosignalen ausgeschlossen. Es sind jetzt 61 Einträge.

Nach jedem Eintrag gibt es 2 Frei zeilen (links steht wer verantwortlich ist).

    Zeile für KI Einordnung (ohne das in den klammern): correct, to much (haluzination), to few (missing facts), wrong
    Zeile für manuelle Eintragungen wenn KI nicht correct ist


Hier ist die Beschreibung/Filterung und wie die Zeilen auszufüllen sind und wie die Anweisungen für die KI waren (die Anweisung sollte man berücksichtigen wenn man die KI einstuft, da es manchmal nicht ganz spezifisch ist z.B. population gibts keine standard gruppe wesegen vermutlich " adults" bei vielen unbestimmten genommen wurde und bei "demographic_specifics" waren die angaben für die KI sehr ungenau).


AI Model: Gemini Flash 2.5 (5-20)

Query (Algorithms in sleep research) -> 12590 
Abstract ai review (Algorithms in sleep research): 4850 / 
Manual (Franz) Paper gathering rejection: 324 (proceedings, not available, no access)
Full Paper ai review: rejected (1050 total), accepted (3489)


output type: output_type: "diagnose", "stage detection", "event detection" (can be multiple)
-> diagnose + sleep disorder: 2012 total entries
data_base: "bio signals", "video", "sonar", "audio", "pressure", "movement", "heat", "tabular data", "other"
-> excluded audio, biosignals, heat, images, movement, pressure, respiration, sonar, video: 70 total entries
output_tasks: what tasks are solved: Sleep Staging, Respiratory Event Detection, Leg Movement Detection, Arousal Detection, Insomnia Diagnosis, Parasomnia Diagnosis, Hypersomnia Diagnosis, Sleep-Related-Movement Disorder Diagnosis, Sleep-Related-Breathing Disorder Diagnosis, Other Diagnosis, Other Event Detection
-> Sleep-Related-Breathing Disorder Diagnosis: 61


Zu prüfen:

data_base (ai): "bio signals", "video", "sonar", "audio", "pressure", "movement", "heat", "tabular data", "other"
data_base (human): just check if its tabluar data or other that are not in the list

dataset (ai): which datasets are used (SHHS, MESA, MrOS, ...)
dataset (human): public available (specificy name), private (specify clinic)

population (ai): what is the population: adults, children, elderly, animals
population (human): population of the database (adults with suspected sleep disorder, children, ...)

demographic_specifics (ai): age ranges, sex, BMI categories, ...
demographic_specifics (human): what are inclusion or exclusion criterias for the study: general population, suspected sleep disorder, age ranges, only male, ...

clinical_conditions (ai): OSA, insomnia, hypersomnia, healthy controls, ...
clinical_conditions (human): OSA, insomnia, hypersomnia, healthy controls, ...

source_device (ai): "clinical PSG", "wearable", "smartphone", "home monitoring device", "research prototype", "other"
source_device (human): just check if correct

diagnose_source (ai): if sleep disorders are present, how where they diagnosed ? (sleep expert, derived from parameters, ...)
diagnose_source (human): who did the diagnosis (sleep expert, derived from parameter, device, ...)

algorithm_approach (ai): "rule-based", "classical ML", "deep learning", "ensemble", "hybrid", "signal processing"
algorithm_approach (human): classical ml, deep learning, rule based, other (maybe specify)

output_tasks (ai): Sleep Staging, Respiratory Event Detection, Leg Movement Detection, Arousal Detection, Insomnia Diagnosis, Parasomnia Diagnosis, Hypersomnia Diagnosis, Sleep-Related-Movement Disorder Diagnosis, Sleep-Related-Breathing Disorder Diagnosis, Other Diagnosis, Other Event Detection
output_type (ai): "diagnose", "stage detection", "event detection" (can be multiple)
output_task_count (ai): how many different tasks are solvd by the algorithm
output_classes (ai): list of output types that are distinguished e.g.: obstructive apnea, mixed apnea, central apnea, hypopnea, arousal, N1, N2, N3, REM, LM, spindle, k-komplex, PLMN, Insomnia ...
output_counts (ai): output_counts: how many events can be distingusied
output_text (ai): what is the algorithm output?

output_text (human): binary osa/non-osa, osa severity, other (maybe specify)

inputs (ai): a list of the types of sources used: PSG, ECG, RR, age, ESS, ...
inputs (human): list of inputs: BMI, age, gender, bio-signals (specify), ...


metric_values (ai): 0.8 macro F1 score, 0.5 Cohens kappa, ...
used_metrics (ai): Kappa, F1, AUPRC, sensitivity, accuracy, ...
used_metrics (human): used metrics with scores, e.g. Kappa = 0.5, F1 = 0.8, AUPRC = 0.7, sensitivity = 0.9, accuracy = 0.85, ...

test_set_size (ai): test set size: with group and size such as 512 OSA patients or 123 healthy participants
test_set_size (human): size of the data the algorithm was validated against

training_set_size (ai): number of subjects and/or recordings used for training
training_set_size (human):  size of the training data (specify group records/patients/participants...)

impact_evaluation (ai): impact factors tested on the performance e.g.: sleep disorders ahi, age, sex, bmi, ...
impact_evaluation (human): tested bias of the algorithm (age, sex, bmi, sleep disorder, ...)


generalization_testing (ai): whether tested across different devices, populations, or sleep centers
generalization_testing (human): (ML/DL specific), was the data split by patient, recording, site ?

data_preprocessing (ai): types of preprocessing applied - "filtering", "artifact removal", "normalization", "feature extraction"
data_preprocessing (human): types of preprocessing/manipulation applied

validation_type (ai): cross-validation recordwise, cross-validation dataset wise, cross-validation segment wise, hold-out test set, ...
validation_type (human): cross-validation recordwise, cross-validation dataset wise, cross-validation segment wise, hold-out test set, ...


valid_paper (ai): does the paper abstract fit the content (yes/no)
valid_paper (human): does the abstract/title has anything todo with the data (sanity check for falely uploaded pdf)


comments (human): whats special/odd/outstanding
reject reason (human):
relevant for ap-3: yes/no (why not)


AIR Column check: correct, to much (haluzination), to few (missing facts), wrong, unknown (open for discussion)


## Manual Review

## Postprocessing
- discussed unclear
- fixed human typos
- cleaned retracted: https://doi.org/10.1007/s00521-019-04037-8