import pandas as pd

df = pd.read_excel('ap3-literature-review.xlsx')

df = df.drop(columns=['source_device', 'diagnose_source', 'clinical_conditions'])

# drop retracted 57528
index = df[(df["ID"] == 57528)].index[0]
df.iloc[[index, index+1, index+2]]
df = df.drop([index, index+1, index+2])




df[["data_base", "dataset", "population"]]

all_output_columns = [
       'data_base', 'dataset', 'population', 'demographic_specifics',
       'algorithm_approach', 'output_tasks', 'output_type',
       'output_task_count', 'output_classes', 'output_counts',
       'output_classes_category', 'output_text',
       'input_categories(demographics, antropohology, psg, questionairs)',
       'inputs', 'metric_values', 'used_metrics', 'test_set_size',
       'training_set_size', 'impact_evaluation', 'generalization_testing',
       'data_preprocessing', 'validation_type', 'valid_paper']

df["input_categories"] = df["input_categories(demographics, antropohology, psg, questionairs)"]

def map_input_to_categories(input_str):
    if not isinstance(input_str, str) or not input_str.strip():
        return None

    # Split by "+" and trim each word
    input_words = [word.strip().lower() for word in input_str.split('+')]

    categories = set()
    
    for input_word in input_words:
        # Demographics
        demo_keywords = [ # checked
            'age', 'gender', 'sex', 'biological sex', 'ethnicity',
            'patient demographics',
            'patient demographics (gender, age, bmi, neck circumference)',
            'demographic information (age, gender)',
            'demographic data'
        ]
        if input_word in demo_keywords:
            categories.add('demographics')

        # Anthropology/Physical measurements
        anthro_keywords = [ # checked
            'patient demographics (gender, age, bmi, neck circumference)', # added
            'weight', 'height', 'bmi', 'neck circumference', 'waist circumference',
            'hip circumference', 'abdominal circumference', 'craniofacial', 'mallampati', 'nc', 'ac', 'hc',
            'head circumference', 'buttock circumference', 'neck perimeter', 'head', 'neck', 'waist', 'buttock',
            'body mass index (bmi)', 'neck circumference to height ratio', 'waist circumference to height ratio',
            'hip circumference to height ratio', 'waist to hip ratio', 'z-score of bmi', 'bmi category',
            'body fat mass', 'muscle mass', 'visceral fat level', 'bone mass', 'fat-free mass', 'fat percentage',
            'muscle percentage', 'basal metabolic rate', 'physique rating', 'body water', '3d craniofacial scans',
            'mallampati class', 'mallampati score', 'subnasale to stomion (facial measurement)', 'hip measurement',
            'neck circumference (lying position)', 'neck circumference (sitting position)', 'neck size',
            'anthropometric measurements', 'anthropometric measurements (neck circumference, height, weight)',
            'anthropometric measures', 'physical measurements', 'tongue enlargement', 'craniofacial abnormalities',
            'palatine tonsil size', 'adenoid to nasopharynx ratio', 'tympanogram type', 'nasal cavity',
            'soft palate/uvula', 'tonsil size', 'lpw', 'tongue base', 'lingual tonsil', 'micrognathia',
            'bh', 'bw', 'foreheadc', 'neckc', 'waistc', 'hipc', 'friedman tongue position (ftp)',
            'friedman palate position class', 'brodsky scale (tonsillar hypertrophy)', 'dv% (pharyngeal collapsibility)',
            'nasopharyngeal volume', 'narrow palate', 'skeletal or dental malocclusion', 'physical examination findings'
        ]
        # lpw: Lateral Pharyngeal Wall Collapse (
        # micrognathia (also clincial condition maybe ?)
        if input_word in anthro_keywords:
            categories.add('anthropology')

        # Questionnaires
        quest_keywords = [
            'questionnaire', 'ess', 'berlin', 'psq', 'bq', 'survey', 'epworth', 'score', 'symptom', 'symptoms',
            'questionnaire answers', 'physio-psycho symptoms', 'questionnaires',
            'Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, NC, NP, SD, DS, NA, MD, CS, FG, LP, EM, TD)'.lower(),
            'STOP-BANG questionnaire'.lower(), 'questionnaire responses (sleep symptoms, behavior)'.lower(),
            'Berlin Questionnaire'.lower(), 'sleep duration', 'quality of sleep', 'physical activity level',
            'hads-a score', 'hads-d score', 'ess score', 'psqi score', 'sos score', 'ess scores', 'isi scores',
            'k-bdi-ii scores', 'psqi scores', 'sss scores', 'epworth scale', 'epworth sleepiness scale',
            'epworth sleepiness scale (ess)', 'pediatric sleep questionnaire (psq) features',
            'obstructive sleep apnea-18 (osa-18) features', 'hong kong children\'s sleep questionnaire (hk-csq) features',
            'severity score', 'conners abbreviated symptom questionnaire', 'brouillette questionnaire',
            'nosas questionnaire', 'psqi', 'sos', 'general health questionnaire scores', 'berlin questionnaire score',
            'epworth sleepiness scale score', 'fss total score (fatigue severity scale)', 'gsaq',
            'visual analog scale (vas) for snoring', 'berlin scores', 'berlin_sum', 'chq_anxiety', 'chq_somatic',
            'chq_depression', 'chq_total', 'cesd', 'irlssg', 'clinical score', 'depression score',
            'snoring (berlin questionnaire)', 'loudness of snoring (berlin questionnaire)',
            'falling asleep (berlin questionnaire)', 'snoring (questionnaire)', 'observed stop breathing (questionnaire)',
            'restless legs (questionnaire)', 'desire/interest in sex (questionnaire)', 'reported symptoms',
            'symptoms (nocturnal)', 'symptoms (daytime)', 'bed partner observations'
        ]
        if input_word in quest_keywords:
            categories.add('questionairs')

        # PSG parameters
        psg_keywords = [ # checked
            'psg', 'polysomnography', 'ahi', 'rdi', 'sleep stage', 'apnea', 'hypopnea', 'oxygen', 'spo2', 'desaturation',
            'mean spo2', 'pulse oxygen saturation', 'mean heart rate during sleep', 'rahi', 'hsi', 'cap',
            'psg, ecg, rr, age, ess, ...', 'sleep architecture parameters (tst, wakefulness, rem/nrem duration, latency, awakening)',
            'respiratory event counts (obstructive apnea, mixed apnea, central apnea, hypopnea during rem/nrem)',
            'cardiac parameters (heart rate, bradycardia, tachycardia)',
            'oxygen saturation parameters (desaturation below 90%, average/max/min desaturation)',
            'arousal counts', 'sleep position parameters (time on left/right side, prone, supine)', 'di3', 'di4', 'plm'
        ]
        # DI (frequency of desaturations)
        if input_word in psg_keywords:
            categories.add('psg-parameter')

        # Free text/Clinical notes
        text_keywords = [
            'clinical notes (text data)', 'icd-10 codes', 'surgical history (textual)',
            'family history (textual)', 'toxic history (textual)', 'laboratory blood reports', 'habitual sleep history', 
            'clinical history', 'comorbidity data', 'medical history', 'social history'
        ]
        # 'habitual sleep history'
        if input_word in text_keywords:
            categories.add('clinical-raw')

        # Medical conditions/Comorbidities
        medical_keywords = [
            'medical history (textual)', 'hypertension', 'diabetes mellitus', 'diabetes', 'depression', 'bruxism', 
            'transient ischaemic attacks, strokes', 'stroke/tia', 'heart disease', 'thyroid diseases',
            'dyslipidemia', 'gastric reflux', 'respiratory diseases', 'asthma', 'rhinitis history',
            'hypertension history', 'diabetes history', 'hyperlipidemia history', 'arrhythmia history',
            'coronary artery disease history', 'cerebrovascular disease history', 'chronic kidney disease history',
            'hyperuricemia history', 'fatty liver history', 'asthma history', 'copd history',
            'heart failure history', 'allergy', 'nasal obstruction', 'diagnosed conditions',
            'prescribed treatments', 'medication history', 'comorbidities', 'cad', 'chf', 'cva',
            'ckd', 'copd', 'hypothyroidism', 'atrial fibrillation', 'stroke', 'myocardial infarction',
            'pulmonary hypertension', 'congestive heart failure', 'renal failure', 'gastroesophageal reflux',
            'chronic headaches', 'chronic headache', 'neurological disease', 'respiratory disease',
            'history of hypertension', 'history of diabetes mellitus', 'medicine use', 'medications'
        ]
        if input_word in medical_keywords:
            categories.add('medical-conditions')

        # Lifestyle/Behavioral factors
        lifestyle_keywords = [
            'night-shift work', 'smoking habits', 'smoking status', 'smoking', 'current smoking',
            'alcohol consumption', 'drinking habits', 'occupation', 'stress level', 'daily steps',
            'family history of snoring', 'family history of osa', 'smoking in family',
            'educational level of parents', 'children\'s symptoms and living habits', 'consumption of hypnotics',
            'hypnotic use', 'tea consumption', 'cigarette consumption', 'cigarette smoking',
            'sedatives', 'driver status', 'coffee consumption', 'family history/genetics'
        ]
        if input_word in lifestyle_keywords:
            categories.add('lifestyle')

        # Vital signs/Laboratory values
        vitals_keywords = [
            'blood pressure', 'heart rate', 'respiratory rate', 'morning sbp', 'morning dbp',
            'evening sbp', 'evening dbp', 'average sbp', 'average dbp', 'sbp difference', 'dbp difference',
            'pulse pressure (morning)', 'pulse pressure (evening)', 'diastolic blood pressure',
            'lsm', 'avat', 'ldl', 'alt', 'ast', 'protein measures', 'dbp', 'avesbp', 'sbp',
            'ps_spb', 'ps_dpb'
        ]
        if input_word in vitals_keywords:
            categories.add('vitals-labs')

        # Sleep-related symptoms and behaviors
        sleep_symptoms_keywords = [
            'hours of sleep', 'minutes until falling asleep', 'prolonged intra-sleep awakenings',
            'feeling of unrefreshing sleep', 'daytime tiredness', 'morning dullness',
            'unjustified multiple awakenings', 'nocturia', 'breathless awakenings', 'reported apneas',
            'reported apnea incidents', 'snorer', 'high intensity snorer', 'snoring-related awakenings',
            'snoring', 'witnessed apnea', 'sleepiness while driving', 'severe snore', 'nocturia frequency',
            'awakening due to the sound of snoring', 'witnessed snore', 'back pain', 'restless sleep',
            'breathing stop in sleep', 'witnessed apneas', 'choking', 'vehicle crashes', 'refreshing sleep',
            'humor alterations', 'restless sleep', 'decreased libido', 'morning headaches',
            'concentration decrease', 'daytime sleepiness', 'depression/anxiety', 'history of snoring or gasping',
            'history of nasal obstruction', 'history of running nose', 'habitual sol (min)', 'unrefreshed sleep',
            'frequency of awakening in sleep', 'awakening at sleep >=3 times/night', 'eds',
            'frequency of nocturia', 'nocturia >=2 times/night', 'witnessed leg jerks in sleep',
            'morning headache', 'dry throat at waking up', 'sol <30 min', 'ds', 'falling asleep while driving',
            'snoring every night', 'stopping breathing during sleep', 'total number of symptoms',
            'feelings of sadness or anxiety'
        ]
        if input_word in sleep_symptoms_keywords:
            categories.add('sleep-symptoms')

        # If nothing matched, add to 'other' category
        if not categories:
            categories.add('other')

    return ', '.join(sorted(categories)) if categories else None

# Apply mapping only to rows where index % 3 == 0
mask = df.index % 3 == 0
df.loc[mask, 'input_categories'] = df.loc[mask, 'inputs'].apply(map_input_to_categories)

df[["inputs", "input_categories"]][mask]

import pandas as pd

# Load your data (assuming df is already loaded)
# df = pd.read_excel('ap3-literature-review.xlsx')

# 1. List all entries and count how many entries per human
# Extract human names from every third row starting from index 2 (0-indexed)
human_names = []
for i in range(2, len(df), 3):  # Start at index 2, step by 3
    if i < len(df):
        human_name = df.iloc[i, 0]  # First column of the human output row
        if pd.notna(human_name):  # Only add non-null values
            human_names.append(human_name)

# Count entries per human
from collections import Counter
entries_per_human = Counter(human_names)
print("Entries per human annotator:")
for human, count in entries_per_human.items():
    print(f"{human}: {count}")

# 2. Column-wise evaluation for "data_base", "dataset", "population"
columns_to_evaluate = ["data_base", "dataset", "population", "demographic_specifics"]
columns_to_evaluate = all_output_columns
evaluation_results = {}

for col in columns_to_evaluate:
    if col in df.columns:
        evaluation_results[col] = {
            'correct_count': 0,
            'wrong_count': 0,
            'other_count': 0,
            'other_values': []
        }
        
        # Analyze every third row starting from index 1 (human evaluation rows)
        for i in range(1, len(df), 3):  # Human evaluation rows
            if i < len(df):
                eval_value = df.iloc[i, df.columns.get_loc(col)]
                if pd.notna(eval_value):
                    eval_str = str(eval_value).lower()
                    if 'correct' in eval_str:
                        evaluation_results[col]['correct_count'] += 1
                    elif 'wrong' in eval_str:
                        evaluation_results[col]['wrong_count'] += 1
                    else:
                        evaluation_results[col]['other_count'] += 1
                        evaluation_results[col]['other_values'].append(eval_value)


# Print evaluation results with AI vs Human comparison for wrong entries
print("\n" + "="*50)
print("EVALUATION RESULTS")
print("="*50)

for col in columns_to_evaluate:
    if col in evaluation_results:
        print(f"\nColumn: '{col}'")
        print(f"  Correct: {evaluation_results[col]['correct_count']}")
        print(f"  Wrong: {evaluation_results[col]['wrong_count']}")
        print(f"  Other: {evaluation_results[col]['other_count']}")
        if evaluation_results[col]['other_values']:
            print(f"  Other values: {set(evaluation_results[col]['other_values'])}")
        
        # Show AI vs Human output for wrong entries
        if evaluation_results[col]['wrong_count'] > 0:
            print(f"\n  WRONG ENTRIES - AI vs Human Output for '{col}':")
            print("  " + "-"*60)
            
            wrong_count = 0
            for i in range(1, len(df), 3):  # Human evaluation rows
                if i < len(df):
                    eval_value = df.iloc[i, df.columns.get_loc(col)]
                    # if 'wrong' in str(eval_value).lower():
                    human_output = df.iloc[i+1, df.columns.get_loc(col)] if i+1 < len(df) else "N/A"
                    if pd.notna(human_output)  and human_output != "N/A" and "correct" not in str(eval_value).lower():
                        wrong_count += 1
                        
                        # Get AI output (row i-1) and human output (row i+1)
                        ai_output = df.iloc[i-1, df.columns.get_loc(col)] if i-1 >= 0 else "N/A"
                        human_name = df.iloc[i+1, 0] if i+1 < len(df) else "Unknown"
                        
                        print(f"  Entry {wrong_count}:")
                        print(f"    Eval ({human_name}): {eval_value}")
                        print(f"    AI Output:    '{ai_output}'")
                        print(f"    Human Output: '{human_output}'")
                        print()

# Create summary DataFrame
summary_data = []
for col in columns_to_evaluate:
    if col in evaluation_results:
        summary_data.append({
            'Column': col,
            'Correct': evaluation_results[col]['correct_count'],
            'Wrong': evaluation_results[col]['wrong_count'],
            'Other': evaluation_results[col]['other_count'],
            'Total': evaluation_results[col]['correct_count'] + 
                    evaluation_results[col]['wrong_count'] + 
                    evaluation_results[col]['other_count']
        })

summary_df = pd.DataFrame(summary_data)
print("\n" + "="*50)
print("SUMMARY TABLE")
print("="*50)
print(summary_df)

# Also create a detailed comparison DataFrame for wrong entries
wrong_entries_data = []
for col in columns_to_evaluate:
    if col in df.columns:
        for i in range(1, len(df), 3):  # Human evaluation rows
            if i < len(df):
                eval_value = df.iloc[i, df.columns.get_loc(col)]
                human_output = df.iloc[i+1, df.columns.get_loc(col)] if i+1 < len(df) else "N/A"
                if pd.notna(human_output)  and human_output != "N/A" and "correct" not in str(eval_value).lower():
                    ai_output = df.iloc[i-1, df.columns.get_loc(col)] if i-1 >= 0 else "N/A"
                    human_name = df.iloc[i+1, 0] if i+1 < len(df) else "Unknown"
                    
                    wrong_entries_data.append({
                        'Column': col,
                        'Annotator': human_name,
                        'AI_Output': ai_output,
                        'Human_Output': human_output,
                        'Entry_Index': i//3 + 1
                    })

if wrong_entries_data:
    wrong_entries_df = pd.DataFrame(wrong_entries_data)
    print("\n" + "="*50)
    print("DETAILED WRONG ENTRIES COMPARISON")
    print("="*50)
    print(wrong_entries_df.to_string(index=False))