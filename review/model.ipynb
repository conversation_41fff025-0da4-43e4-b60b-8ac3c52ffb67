{"cells": [{"cell_type": "code", "execution_count": 113, "id": "a2be3099", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_excel('ap3-literature-review.xlsx')"]}, {"cell_type": "code", "execution_count": 114, "id": "1406cc4f", "metadata": {}, "outputs": [], "source": ["df = df.drop(columns=['source_device', 'diagnose_source', 'clinical_conditions'])"]}, {"cell_type": "code", "execution_count": 115, "id": "2e986f1a", "metadata": {}, "outputs": [], "source": ["# drop retracted 57528\n", "index = df[(df[\"ID\"] == 57528)].index[0]\n", "df.iloc[[index, index+1, index+2]]\n", "df = df.drop([index, index+1, index+2])\n", "\n"]}, {"cell_type": "code", "execution_count": 116, "id": "c4b873a7", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "data_base", "rawType": "object", "type": "unknown"}, {"name": "dataset", "rawType": "object", "type": "unknown"}, {"name": "population", "rawType": "object", "type": "unknown"}], "ref": "9e4354e2-dd09-4b2c-a3c9-4903401e5399", "rows": [["0", "tabular data", "internal", "adults"], ["1", "correct", "correct", "correct"], ["2", "and polysomnography", "Policlinico Umberto I of Rome", "adult patients suspected of OSA"], ["3", "tabular data", "TMUH database", "adults"], ["4", "correct", "correct", "correct"], ["5", null, null, null], ["6", "tabular data", "Proprietary / Clinical", "adults"], ["7", "correct", "correct", "correct"], ["8", "overnight PSG", "E-Da Hospitla Taiwan", "patients aged 18 and o<PERSON>er with diagnosis of acute ischemic stroke"], ["9", "tabular data", "Internal institutional registries", "adults"], ["10", "correct", "correct", "correct"], ["11", "PSG", "Centre for Heart Rhythm Disorders, University of Adelaide, external validation cohort: Royal Melbourne Hosital", "patients with AF who had PSG"], ["12", "tabular data", "Fujian Medical University Sleep Medicine Center", "adults"], ["13", "correct", "correct", "correct"], ["14", "PSG", null, "participation in PSG, exclusion: OSA treatment, CPAP treatment, PSG time less than 4h, nighttime sleep duraltion < 3h"], ["15", "other", "adSLEEP corpus (UPMC clinical notes)", "elderly"], ["16", null, null, null], ["17", null, null, null], ["18", "tabular data", "National Health and Nutrition Examination Survey", "adults"], ["19", "correct", "correct", "correct"], ["20", null, null, null], ["21", "tabular data", "UNC Chapel Hill Sleep Lab data, SHHS, CFS", "adults"], ["22", "correct", "correct", "wrong"], ["23", null, "SHHS and CFS as external validation", "excluded younger than 13"], ["24", "tabular data", "proprietary", "adults"], ["25", "correct", null, "correct"], ["26", null, "Sleep Medicine Center of a tertiary hospital in southern  Taiwan", null], ["27", "tabular data", "local dataset", "adults"], ["28", "correct", "correct", "correct"], ["29", null, "Eulji University Hospital’s Department of Otorhinolaryngology sleep study laboratory", "19 and older"], ["30", "tabular data", "Beijing Children's Hospital (internal dataset)", "children"], ["31", "correct", "correct", "correct"], ["32", null, null, null], ["33", "tabular data", "Internal (<PERSON><PERSON><PERSON> Hospital EHR)", "adults"], ["34", "correct", "correct", "correct"], ["35", null, null, null], ["36", "tabular data", "TMUH (Taipei Medical University Hospital) sleep center Asians data", "adults"], ["37", "correct", "correct", "correct"], ["38", null, null, null], ["39", "tabular data", "proprietary", "adults"], ["40", "correct", "correct", "correct"], ["41", null, "Bariatric and  Metabolic Disease Surgery Center of Zhongnan Hospital of Wuhan University", null], ["42", "tabular data", "Sleep Health and Lifestyle dataset", "adults"], ["43", "correct", "correct", "correct"], ["44", null, null, null], ["45", "tabular data", "Proprietary (Samsung Medical Center)", "adults"], ["46", "correct", "correct", "correct"], ["47", null, null, null], ["48", "tabular data", "<PERSON><PERSON><PERSON>", "adults"], ["49", "correct", "correct", "correct"]], "shape": {"columns": 3, "rows": 180}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_base</th>\n", "      <th>dataset</th>\n", "      <th>population</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>tabular data</td>\n", "      <td>internal</td>\n", "      <td>adults</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>and polysomnography</td>\n", "      <td>Policlinico Umberto I of Rome</td>\n", "      <td>adult patients suspected of OSA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>tabular data</td>\n", "      <td>TMUH database</td>\n", "      <td>adults</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>tabular data</td>\n", "      <td>Loyola University Medical Center internal dataset</td>\n", "      <td>adults</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "      <td>correct</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 3 columns</p>\n", "</div>"], "text/plain": ["               data_base                                            dataset  \\\n", "0           tabular data                                           internal   \n", "1                correct                                            correct   \n", "2    and polysomnography                      Policlinico Umberto I of Rome   \n", "3           tabular data                                      TMUH database   \n", "4                correct                                            correct   \n", "..                   ...                                                ...   \n", "178              correct                                            correct   \n", "179                  NaN                                                NaN   \n", "180         tabular data  Loyola University Medical Center internal dataset   \n", "181              correct                                            correct   \n", "182                  NaN                                                NaN   \n", "\n", "                          population  \n", "0                             adults  \n", "1                            correct  \n", "2    adult patients suspected of OSA  \n", "3                             adults  \n", "4                            correct  \n", "..                               ...  \n", "178                          correct  \n", "179                              NaN  \n", "180                           adults  \n", "181                          correct  \n", "182                              NaN  \n", "\n", "[180 rows x 3 columns]"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df[[\"data_base\", \"dataset\", \"population\"]]"]}, {"cell_type": "code", "execution_count": 117, "id": "c90fef99", "metadata": {}, "outputs": [], "source": ["all_output_columns = [\n", "       'data_base', 'dataset', 'population', 'demographic_specifics',\n", "       'algorithm_approach', 'output_tasks', 'output_type',\n", "       'output_task_count', 'output_classes', 'output_counts',\n", "       'output_classes_category', 'output_text',\n", "       'input_categories(demographics, antropohology, psg, questionairs)',\n", "       'inputs', 'metric_values', 'used_metrics', 'test_set_size',\n", "       'training_set_size', 'impact_evaluation', 'generalization_testing',\n", "       'data_preprocessing', 'validation_type', 'valid_paper']"]}, {"cell_type": "code", "execution_count": 118, "id": "3da12f49", "metadata": {}, "outputs": [], "source": ["df[\"input_categories\"] = df[\"input_categories(demographics, antropohology, psg, questionairs)\"]"]}, {"cell_type": "code", "execution_count": 152, "id": "ac88472e", "metadata": {}, "outputs": [], "source": ["def map_input_to_categories(input_str):\n", "    if not isinstance(input_str, str) or not input_str.strip():\n", "        return None\n", "\n", "    # Split by \"+\" and trim each word\n", "    input_words = [word.strip().lower() for word in input_str.split('+')]\n", "\n", "    categories = set()\n", "    \n", "    for input_word in input_words:\n", "        # Demographics\n", "        demo_keywords = [ # checked\n", "            'age', 'gender', 'sex', 'biological sex', 'ethnicity',\n", "            'patient demographics',\n", "            'patient demographics (gender, age, bmi, neck circumference)',\n", "            'demographic information (age, gender)',\n", "            'demographic data'\n", "        ]\n", "        if input_word in demo_keywords:\n", "            categories.add('demographics')\n", "\n", "        # Anthropology/Physical measurements\n", "        anthro_keywords = [ # checked\n", "            'patient demographics (gender, age, bmi, neck circumference)', # added\n", "            'weight', 'height', 'bmi', 'neck circumference', 'waist circumference',\n", "            'hip circumference', 'abdominal circumference', 'craniofacial', 'mallampati', 'nc', 'ac', 'hc',\n", "            'head circumference', 'buttock circumference', 'neck perimeter', 'head', 'neck', 'waist', 'buttock',\n", "            'body mass index (bmi)', 'neck circumference to height ratio', 'waist circumference to height ratio',\n", "            'hip circumference to height ratio', 'waist to hip ratio', 'z-score of bmi', 'bmi category',\n", "            'body fat mass', 'muscle mass', 'visceral fat level', 'bone mass', 'fat-free mass', 'fat percentage',\n", "            'muscle percentage', 'basal metabolic rate', 'physique rating', 'body water', '3d craniofacial scans',\n", "            'mallampati class', 'mallampati score', 'subnasale to stomion (facial measurement)', 'hip measurement',\n", "            'neck circumference (lying position)', 'neck circumference (sitting position)', 'neck size',\n", "            'anthropometric measurements', 'anthropometric measurements (neck circumference, height, weight)',\n", "            'anthropometric measures', 'physical measurements', 'tongue enlargement', 'craniofacial abnormalities',\n", "            'palatine tonsil size', 'adenoid to nasopharynx ratio', 'tympanogram type', 'nasal cavity',\n", "            'soft palate/uvula', 'tonsil size', 'lpw', 'tongue base', 'lingual tonsil', 'micrognathia',\n", "            'bh', 'bw', 'foreheadc', 'neckc', 'waistc', 'hipc', 'friedman tongue position (ftp)',\n", "            'friedman palate position class', 'brodsky scale (tonsillar hypertrophy)', 'dv% (pharyngeal collapsibility)',\n", "            'nasopharyngeal volume', 'narrow palate', 'skeletal or dental malocclusion', 'physical examination findings'\n", "        ]\n", "        # lpw: Lateral Pharyngeal Wall Collapse (\n", "        # micrognathia (also clincial condition maybe ?)\n", "        if input_word in anthro_keywords:\n", "            categories.add('anthropology')\n", "\n", "        # Questionnaires\n", "        quest_keywords = [\n", "            'questionnaire', 'ess', 'berlin', 'psq', 'bq', 'survey', 'epworth', 'score', 'symptom', 'symptoms',\n", "            'questionnaire answers', 'physio-psycho symptoms', 'questionnaires',\n", "            'Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, NC, NP, SD, DS, NA, MD, CS, FG, LP, EM, TD)'.lower(),\n", "            'STOP-BANG questionnaire'.lower(), 'questionnaire responses (sleep symptoms, behavior)'.lower(),\n", "            'Berlin Questionnaire'.lower(), 'sleep duration', 'quality of sleep', 'physical activity level',\n", "            'hads-a score', 'hads-d score', 'ess score', 'psqi score', 'sos score', 'ess scores', 'isi scores',\n", "            'k-bdi-ii scores', 'psqi scores', 'sss scores', 'epworth scale', 'epworth sleepiness scale',\n", "            'epworth sleepiness scale (ess)', 'pediatric sleep questionnaire (psq) features',\n", "            'obstructive sleep apnea-18 (osa-18) features', 'hong kong children\\'s sleep questionnaire (hk-csq) features',\n", "            'severity score', 'conners abbreviated symptom questionnaire', 'brouillette questionnaire',\n", "            'nosas questionnaire', 'psqi', 'sos', 'general health questionnaire scores', 'berlin questionnaire score',\n", "            'epworth sleepiness scale score', 'fss total score (fatigue severity scale)', 'gsaq',\n", "            'visual analog scale (vas) for snoring', 'berlin scores', 'berlin_sum', 'chq_anxiety', 'chq_somatic',\n", "            'chq_depression', 'chq_total', 'cesd', 'irlssg', 'clinical score', 'depression score',\n", "            'snoring (berlin questionnaire)', 'loudness of snoring (berlin questionnaire)',\n", "            'falling asleep (berlin questionnaire)', 'snoring (questionnaire)', 'observed stop breathing (questionnaire)',\n", "            'restless legs (questionnaire)', 'desire/interest in sex (questionnaire)', 'reported symptoms',\n", "            'symptoms (nocturnal)', 'symptoms (daytime)', 'bed partner observations'\n", "        ]\n", "        if input_word in quest_keywords:\n", "            categories.add('questionairs')\n", "\n", "        # PSG parameters\n", "        psg_keywords = [ # checked\n", "            'psg', 'polysomnography', 'ahi', 'rdi', 'sleep stage', 'apnea', 'hypopnea', 'oxygen', 'spo2', 'desaturation',\n", "            'mean spo2', 'pulse oxygen saturation', 'mean heart rate during sleep', 'rahi', 'hsi', 'cap',\n", "            'psg, ecg, rr, age, ess, ...', 'sleep architecture parameters (tst, wakefulness, rem/nrem duration, latency, awakening)',\n", "            'respiratory event counts (obstructive apnea, mixed apnea, central apnea, hypopnea during rem/nrem)',\n", "            'cardiac parameters (heart rate, bradycardia, tachycardia)',\n", "            'oxygen saturation parameters (desaturation below 90%, average/max/min desaturation)',\n", "            'arousal counts', 'sleep position parameters (time on left/right side, prone, supine)', 'di3', 'di4', 'plm'\n", "        ]\n", "        # DI (frequency of desaturations)\n", "        if input_word in psg_keywords:\n", "            categories.add('psg-parameter')\n", "\n", "        # Free text/Clinical notes\n", "        text_keywords = [\n", "            'clinical notes (text data)', 'icd-10 codes', 'surgical history (textual)',\n", "            'family history (textual)', 'toxic history (textual)', 'laboratory blood reports', 'habitual sleep history', \n", "            'clinical history', 'comorbidity data', 'medical history', 'social history'\n", "        ]\n", "        # 'habitual sleep history'\n", "        if input_word in text_keywords:\n", "            categories.add('clinical-raw')\n", "\n", "        # Medical conditions/Comorbidities\n", "        medical_keywords = [\n", "            'medical history (textual)', 'hypertension', 'diabetes mellitus', 'diabetes', 'depression', 'bruxism', \n", "            'transient ischaemic attacks, strokes', 'stroke/tia', 'heart disease', 'thyroid diseases',\n", "            'dyslipidemia', 'gastric reflux', 'respiratory diseases', 'asthma', 'rhinitis history',\n", "            'hypertension history', 'diabetes history', 'hyperlipidemia history', 'arrhythmia history',\n", "            'coronary artery disease history', 'cerebrovascular disease history', 'chronic kidney disease history',\n", "            'hyperuricemia history', 'fatty liver history', 'asthma history', 'copd history',\n", "            'heart failure history', 'allergy', 'nasal obstruction', 'diagnosed conditions',\n", "            'prescribed treatments', 'medication history', 'comorbidities', 'cad', 'chf', 'cva',\n", "            'ckd', 'copd', 'hypothyroidism', 'atrial fibrillation', 'stroke', 'myocardial infarction',\n", "            'pulmonary hypertension', 'congestive heart failure', 'renal failure', 'gastroesophageal reflux',\n", "            'chronic headaches', 'chronic headache', 'neurological disease', 'respiratory disease',\n", "            'history of hypertension', 'history of diabetes mellitus', 'medicine use', 'medications'\n", "        ]\n", "        if input_word in medical_keywords:\n", "            categories.add('medical-conditions')\n", "\n", "        # Lifestyle/Behavioral factors\n", "        lifestyle_keywords = [\n", "            'night-shift work', 'smoking habits', 'smoking status', 'smoking', 'current smoking',\n", "            'alcohol consumption', 'drinking habits', 'occupation', 'stress level', 'daily steps',\n", "            'family history of snoring', 'family history of osa', 'smoking in family',\n", "            'educational level of parents', 'children\\'s symptoms and living habits', 'consumption of hypnotics',\n", "            'hypnotic use', 'tea consumption', 'cigarette consumption', 'cigarette smoking',\n", "            'sedatives', 'driver status', 'coffee consumption', 'family history/genetics'\n", "        ]\n", "        if input_word in lifestyle_keywords:\n", "            categories.add('lifestyle')\n", "\n", "        # Vital signs/Laboratory values\n", "        vitals_keywords = [\n", "            'blood pressure', 'heart rate', 'respiratory rate', 'morning sbp', 'morning dbp',\n", "            'evening sbp', 'evening dbp', 'average sbp', 'average dbp', 'sbp difference', 'dbp difference',\n", "            'pulse pressure (morning)', 'pulse pressure (evening)', 'diastolic blood pressure',\n", "            'lsm', 'avat', 'ldl', 'alt', 'ast', 'protein measures', 'dbp', 'avesbp', 'sbp',\n", "            'ps_spb', 'ps_dpb'\n", "        ]\n", "        if input_word in vitals_keywords:\n", "            categories.add('vitals-labs')\n", "\n", "        # Sleep-related symptoms and behaviors\n", "        sleep_symptoms_keywords = [\n", "            'hours of sleep', 'minutes until falling asleep', 'prolonged intra-sleep awakenings',\n", "            'feeling of unrefreshing sleep', 'daytime tiredness', 'morning dullness',\n", "            'unjustified multiple awakenings', 'nocturia', 'breathless awakenings', 'reported apneas',\n", "            'reported apnea incidents', 'snorer', 'high intensity snorer', 'snoring-related awakenings',\n", "            'snoring', 'witnessed apnea', 'sleepiness while driving', 'severe snore', 'nocturia frequency',\n", "            'awakening due to the sound of snoring', 'witnessed snore', 'back pain', 'restless sleep',\n", "            'breathing stop in sleep', 'witnessed apneas', 'choking', 'vehicle crashes', 'refreshing sleep',\n", "            'humor alterations', 'restless sleep', 'decreased libido', 'morning headaches',\n", "            'concentration decrease', 'daytime sleepiness', 'depression/anxiety', 'history of snoring or gasping',\n", "            'history of nasal obstruction', 'history of running nose', 'habitual sol (min)', 'unrefreshed sleep',\n", "            'frequency of awakening in sleep', 'awakening at sleep >=3 times/night', 'eds',\n", "            'frequency of nocturia', 'nocturia >=2 times/night', 'witnessed leg jerks in sleep',\n", "            'morning headache', 'dry throat at waking up', 'sol <30 min', 'ds', 'falling asleep while driving',\n", "            'snoring every night', 'stopping breathing during sleep', 'total number of symptoms',\n", "            'feelings of sadness or anxiety'\n", "        ]\n", "        if input_word in sleep_symptoms_keywords:\n", "            categories.add('sleep-symptoms')\n", "\n", "        # If nothing matched, add to 'other' category\n", "        if not categories:\n", "            categories.add('other')\n", "\n", "    return ', '.join(sorted(categories)) if categories else None\n", "\n", "# Apply mapping only to rows where index % 3 == 0\n", "mask = df.index % 3 == 0\n", "df.loc[mask, 'input_categories'] = df.loc[mask, 'inputs'].apply(map_input_to_categories)"]}, {"cell_type": "code", "execution_count": 150, "id": "30e951ec", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "inputs", "rawType": "object", "type": "string"}, {"name": "input_categories", "rawType": "object", "type": "string"}], "ref": "2f750db2-58bb-41d4-a418-028a85fcd89b", "rows": [["0", "age + gender + weight + height + BMI + neck circumference + waist circumference + hip circumference + night-shift work + smoking habits + hypertension + diabetes mellitus + depression + bruxism + transient ischaemic attacks, strokes + heart disease + thyroid diseases + dyslipidemia + gastric reflux + respiratory diseases", "anthropology, demographics, lifestyle, medical-conditions"], ["3", "BQ + ESS + Age + Gender + Height + Weight + BMI + Head circumference + Neck circumference + Waist circumference + Buttock circumference", "anthropology, demographics, questionairs"], ["6", "age + sex + BMI + ESS + neck circumference", "anthropology, demographics, questionairs"], ["9", "age + sex + BMI + diabetes + stroke/TIA", "anthropology, demographics, medical-conditions"], ["12", "age + gender + BMI + mean heart rate during sleep", "anthropology, demographics, psg-parameter"], ["15", "clinical notes (text data)", "free-text"], ["18", "age + gender + occupation + sleep duration + quality of sleep + physical activity level + stress level + BMI category + blood pressure + heart rate + daily steps + asthma", "anthropology, demographics, lifestyle, medical-conditions, questionairs, vitals-labs"], ["21", "age + sex + weight + height + pulse oxygen saturation + heart rate + respiratory rate", "anthropology, demographics, psg-parameter, vitals-labs"], ["24", "Age + Sex + BMI + Waist Circumference + Hip Circumference + Neck Circumference + Morning SBP + Morning DBP + Evening SBP + Evening DBP + Average SBP + Average DBP + SBP Difference + DBP Difference + Pulse Pressure (morning) + Pulse Pressure (evening) + Mean SpO2 + Rhinitis history + Hypertension history + Diabetes history + Hyperlipidemia history + Arrhythmia history + Coronary Artery Disease history + Cerebrovascular Disease history + Chronic Kidney Disease history + Hyperuricemia history + Fatty Liver history + Asthma history + COPD history + Heart Failure history + HADS-A score + HADS-D score + ESS score + PSQI score + SOS score", "anthropology, demographics, medical-conditions, psg-parameter, questionairs, vitals-labs"], ["27", "STOP-BANG questionnaire + BMI + Age + Neck circumference + Gender", "anthropology, demographics, questionairs"], ["30", "age + sex + neck circumference + abdominal circumference + hip circumference + Body mass index (BMI) + neck circumference to height ratio + waist circumference to height ratio + hip circumference to height ratio + waist to hip ratio + Pediatric Sleep Questionnaire (PSQ) features + Obstructive Sleep Apnea-18 (OSA-18) features + Hong Kong Children’s Sleep Questionnaire (HK-CSQ) features + family history of snoring + family history of OSA + smoking in family + educational level of parents + Children's symptoms and living habits", "anthropology, demographics, lifestyle, questionairs"], ["33", "age + BMI + neck perimeter + smoking status + alcohol consumption + medication history + comorbidities + reported symptoms + Epworth scale + reported apnea incidents", "anthropology, demographics, lifestyle, medical-conditions, questionairs, sleep-symptoms"], ["36", "BQ + ESS + BMI + Weight + Height + Head + Neck + Waist + Buttock + Age + Gender", "anthropology, demographics, questionairs"], ["39", "age + biological sex + BMI + LSM + aVAT + LDL", "anthropology, demographics, vitals-labs"], ["42", "gender + age + occupation + sleep duration + quality of sleep + physical activity level + stress level + BMI + blood pressure + heart rate + daily steps", "anthropology, demographics, lifestyle, questionairs, vitals-labs"], ["45", "Age + Sex + BMI + Height + Weight + Abdominal circumference + Head circumference + Hip circumference + Neck circumference (lying position) + Neck circumference (sitting position) + ESS scores + ISI scores + K-BDI-II scores + PSQI scores + SSS scores + Hours of sleep + Consumption of hypnotics", "anthropology, demographics, lifestyle, questionairs, sleep-symptoms"], ["48", "age + sex + weight + height + neck circumference + smoking habits + drinking habits + diagnosed conditions + prescribed treatments + hours of sleep + minutes until falling asleep + prolonged intra-sleep awakenings + feeling of unrefreshing sleep + daytime tiredness + morning dullness + unjustified multiple awakenings + nocturia + breathless awakenings + reported apneas + snorer + high intensity snorer + snoring-related awakenings", "anthropology, demographics, lifestyle, medical-conditions, sleep-symptoms"], ["51", "<PERSON>rodsky scale (tonsillar hypertrophy) + DV% (pharyngeal collapsibility) + age + sex + ethnicity + z-score of BMI + allergy + asthma + nasal obstruction + narrow palate + skeletal or dental malocclusion + Friedman Palate Position class + Severity Score + Epworth Sleepiness Scale + Conners Abbreviated Symptom Questionnaire + Brouillette questionnaire + neck circumference + nasopharyngeal volume", "anthropology, demographics, medical-conditions, questionairs"], ["54", "PSG, ECG, RR, age, ESS, ...", "psg-parameter"], ["57", "age + sex + BMI + neck circumference + waist circumference + body fat mass + muscle mass + visceral fat level + bone mass + fat-free mass + fat percentage + muscle percentage + basal metabolic rate + physique rating + body water", "anthropology, demographics"], ["60", "age + sex + BMI + ICD-10 codes + medical history (textual) + surgical history (textual) + family history (textual) + toxic history (textual)", "anthropology, demographics, free-text"], ["63", "gender + age + course of disease + snoring + apnea + daytime sleepiness + lack of energy + waking up early + dry mouth + nocturia + difficulty falling asleep + smoking + height + weight + neck circumference + chest circumference + abdominal circumference + BMI + nighttime systolic blood pressure + morning systolic blood pressure + morning diastolic blood pressure", "anthropology, demographics, lifestyle, psg-parameter, sleep-symptoms"], ["66", "3D craniofacial scans + age + BMI + neck circumference + waist circumference + hip circumference + Mallampati class + hypertension + witnessed apnea + sleepiness while driving + BERLIN questionnaire + NoSAS questionnaire", "anthropology, demographics, medical-conditions, questionairs, sleep-symptoms"], ["69", "age + gender + BMI + snoring + neck circumference + waist circumference + PSQI + ESS + SOS", "anthropology, demographics, questionairs, sleep-symptoms"], ["72", "rAHI + age + sex", "demographics, psg-parameter"], ["75", "BMI + sex + HSI + CAP + ALT + AST + diabetes", "anthropology, demographics, medical-conditions, psg-parameter, vitals-labs"], ["78", "patient demographics + laboratory blood reports + physical measurements + habitual sleep history + comorbidities + general health questionnaire scores", "anthropology, demographics, free-text, medical-conditions, questionairs"], ["81", "hypertension + waist circumference + subnasale to stomion (facial measurement) + snoring (Berlin Questionnaire) + loudness of snoring (Berlin Questionnaire) + falling asleep (Berlin Questionnaire) + FSS total score (Fatigue Severity Scale)", "anthropology, medical-conditions, questionairs"], ["84", "Questionnaires + Age + BMI + Sex + Neck circumference", "anthropology, demographics, questionairs"], ["87", "age + gender + waist circumference + neck circumference + snoring + witnessed apnea + SOL <30 min + BMI + current smoking + alcohol consumption + hypnotic use + hypertension + diabetes + CAD + CHF + CVA + CKD + COPD + asthma + hypothyroidism + habitual SOL (min) + sleep duration + unrefreshed sleep + frequency of awakening in sleep + awakening at sleep >=3 times/night + ESS + EDS + frequency of nocturia + nocturia >=2 times/night + witnessed leg jerks in sleep + morning headache + dry throat at waking up", "anthropology, demographics, lifestyle, medical-conditions, questionairs, sleep-symptoms"], ["90", "protein measures + age + gender + BMI", "anthropology, demographics, vitals-labs"], ["93", "age + smoking + nocturia frequency + depression score + body mass index + neck circumference + hip measurement + diastolic blood pressure", "anthropology, demographics, lifestyle, questionairs, sleep-symptoms, vitals-labs"], ["99", "severe snore + nocturia + awakening due to the sound of snoring + witnessed snore + witnessed apnea + back pain + restless sleep + BMI", "anthropology, sleep-symptoms"], ["102", "sex + age + BMI + neck circumference + snoring + smoking + hypertension + ESS", "anthropology, demographics, lifestyle, medical-conditions, questionairs, sleep-symptoms"], ["105", "patient demographics (gender, age, BMI, neck circumference) + sleep architecture parameters (TST, wakefulness, REM/nREM duration, latency, awakening) + respiratory event counts (obstructive apnea, mixed apnea, central apnea, hypopnea during REM/nREM) + cardiac parameters (heart rate, bradycardia, tachycardia) + oxygen saturation parameters (desaturation below 90%, average/max/min desaturation) + arousal counts + sleep position parameters (time on left/right side, prone, supine) + clinical history (smoking status, hypertension, diabetes, atrial fibrillation, depression, stroke, heart attack, hypothyroidism, hyperthyroidism, lipid-lowering medications) + questionnaire scores (Epworth Sleepiness Scale)", "demographics, psg-parameter"], ["108", "demographic data + physical exam data + clinical history + ESS + comorbidity data", "demographics, free-text, questionairs"], ["111", "BMI + age + tongue enlargement", "anthropology, demographics"], ["114", "PSG + age + gender + BMI + NC + AC + HC + Mallampati index", "anthropology, demographics, psg-parameter"], ["117", "questionnaire answers", "questionairs"], ["120", "age + sex + BMI + neck circumference + waist circumference + tea consumption + cigarette consumption + hypertension + chronic headaches + heart disease + respiratory disease + neurological disease + diabetes + Berlin Questionnaire score + Epworth Sleepiness Scale score + snoring status + breathing stop in sleep + feelings of sadness or anxiety", "anthropology, demographics, lifestyle, medical-conditions, questionairs, sleep-symptoms"], ["123", "age + gender + BMI + neck circumference + waist circumference + tea consumption + cigarette smoking + hypertension + chronic headache + heart disease + respiratory disease + neurological disease + diabetes + Berlin questionnaire score + ESS + GSAQ", "anthropology, demographics, lifestyle, medical-conditions, questionairs"], ["126", "age + gender + BMI + neck circumference + abdominal circumference + craniofacial abnormalities + snoring + witnessed apneas + choking + vehicle crashes + refreshing sleep + humor alterations + nocturia + restless sleep + decreased libido + morning headaches + alcohol consumption + smoking + sedatives + ESS + concentration decrease + atrial fibrillation + stroke + myocardial infarction + pulmonary hypertension + congestive heart failure + diabetes + dyslipidemia + renal failure + hypothyroidism + gastroesophageal reflux + hypertension + driver status + coffee consumption + daytime sleepiness + family history/genetics + depression/anxiety", "anthropology, demographics, lifestyle, medical-conditions, questionairs, sleep-symptoms"], ["129", "age + BMI + DBP + Mallampati score + Berlin scores", "anthropology, demographics, questionairs, vitals-labs"], ["132", "sex + age + BMI + history of snoring or gasping + history of nasal obstruction + history of running nose + palatine tonsil size + adenoid to nasopharynx ratio + tympanogram type", "anthropology, demographics, sleep-symptoms"], ["135", "BMI + age + gender + neck size + height + weight", "anthropology, demographics"], ["138", "age + gender + BMI + neck circumference + smoking + ESS + nasal cavity + soft palate/uvula + tonsil size + LPW + mallampati score + tongue base + lingual tonsil + micrognathia", "anthropology, demographics, lifestyle, questionairs"], ["141", "age + gender + BH + BW + ForeheadC + NeckC + WaistC + HipC + PS_SPB + PS_DPB + DS + CHQ_Anxiety + CHQ_Somatic + CHQ_Depression + CHQ_total + CESD + Berlin_sum + IRLSSG", "anthropology, demographics, questionairs, sleep-symptoms, vitals-labs"], ["144", "physio-psycho symptoms", "questionairs"], ["147", "Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, NC, NP, SD, DS, NA, MD, CS, FG, LP, EM, TD)", "questionairs"], ["150", "questionnaires + anthropometric measurements + age + sex + hypertension + medicine use", "anthropology, demographics, medical-conditions, questionairs"]], "shape": {"columns": 2, "rows": 60}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>inputs</th>\n", "      <th>input_categories</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>age + gender + weight + height + BMI + neck ci...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BQ + ESS + Age + Gender + Height + Weight + BM...</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>age + sex + BMI + ESS + neck circumference</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>age + sex + BMI + diabetes + stroke/TIA</td>\n", "      <td>anthropology, demographics, medical-conditions</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>age + gender + BMI + mean heart rate during sleep</td>\n", "      <td>anthropology, demographics, psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>clinical notes (text data)</td>\n", "      <td>free-text</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>age + gender + occupation + sleep duration + q...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>age + sex + weight + height + pulse oxygen sat...</td>\n", "      <td>anthropology, demographics, psg-parameter, vit...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Age + Sex + BMI + Waist Circumference + Hip Ci...</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>STOP-BANG questionnaire + BMI + Age + Neck cir...</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>age + sex + neck circumference + abdominal cir...</td>\n", "      <td>anthropology, demographics, lifestyle, questio...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>age + BMI + neck perimeter + smoking status + ...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>BQ + ESS + BMI + Weight + Height + Head + Neck...</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>age + biological sex + BMI + LSM + aVAT + LDL</td>\n", "      <td>anthropology, demographics, vitals-labs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>gender + age + occupation + sleep duration + q...</td>\n", "      <td>anthropology, demographics, lifestyle, questio...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>Age + Sex + BMI + Height + Weight + Abdominal ...</td>\n", "      <td>anthropology, demographics, lifestyle, questio...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>age + sex + weight + height + neck circumferen...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>Brodsky scale (tonsillar hypertrophy) + DV% (p...</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>PSG, ECG, RR, age, ESS, ...</td>\n", "      <td>psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>age + sex + BMI + neck circumference + waist c...</td>\n", "      <td>anthropology, demographics</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>age + sex + BMI + ICD-10 codes + medical histo...</td>\n", "      <td>anthropology, demographics, free-text</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>gender + age + course of disease + snoring + a...</td>\n", "      <td>anthropology, demographics, lifestyle, psg-par...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>3D craniofacial scans + age + BMI + neck circu...</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>age + gender + BMI + snoring + neck circumfere...</td>\n", "      <td>anthropology, demographics, questionairs, slee...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>rAHI + age + sex</td>\n", "      <td>demographics, psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>BMI + sex + HSI + CAP + ALT + AST + diabetes</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>patient demographics + laboratory blood report...</td>\n", "      <td>anthropology, demographics, free-text, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>hypertension + waist circumference + subnasale...</td>\n", "      <td>anthropology, medical-conditions, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>Questionnaires + Age + BMI + Sex + Neck circum...</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>age + gender + waist circumference + neck circ...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>protein measures + age + gender + BMI</td>\n", "      <td>anthropology, demographics, vitals-labs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>age + smoking + nocturia frequency + depressio...</td>\n", "      <td>anthropology, demographics, lifestyle, questio...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>severe snore + nocturia + awakening due to the...</td>\n", "      <td>anthropology, sleep-symptoms</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>sex + age + BMI + neck circumference + snoring...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>patient demographics (gender, age, BMI, neck c...</td>\n", "      <td>demographics, psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>demographic data + physical exam data + clinic...</td>\n", "      <td>demographics, free-text, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>BMI + age + tongue enlargement</td>\n", "      <td>anthropology, demographics</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>PSG + age + gender + BMI + NC + AC + HC + Mall...</td>\n", "      <td>anthropology, demographics, psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>questionnaire answers</td>\n", "      <td>questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>age + sex + BMI + neck circumference + waist c...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>age + gender + BMI + neck circumference + wais...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>age + gender + BMI + neck circumference + abdo...</td>\n", "      <td>anthropology, demographics, lifestyle, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>age + BMI + DBP + Mallampati score + Berlin sc...</td>\n", "      <td>anthropology, demographics, questionairs, vita...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132</th>\n", "      <td>sex + age + BMI + history of snoring or gaspin...</td>\n", "      <td>anthropology, demographics, sleep-symptoms</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>BMI + age + gender + neck size + height + weight</td>\n", "      <td>anthropology, demographics</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>age + gender + BMI + neck circumference + smok...</td>\n", "      <td>anthropology, demographics, lifestyle, questio...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>age + gender + BH + BW + ForeheadC + NeckC + W...</td>\n", "      <td>anthropology, demographics, questionairs, slee...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>physio-psycho symptoms</td>\n", "      <td>questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, ...</td>\n", "      <td>questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>questionnaires + anthropometric measurements +...</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>age + history of hypertension + history of dia...</td>\n", "      <td>anthropology, demographics, medical-conditions...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>age + sex + AveSBP</td>\n", "      <td>demographics, vitals-labs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>BMI + NC + ESS + gender</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>Gender + age + weight + height + BMI + SBP + D...</td>\n", "      <td>anthropology, demographics, psg-parameter, que...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>Berlin Questionnaire + Epworth Sleepiness Scal...</td>\n", "      <td>anthropology, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>AHI + ARI + SaO2 + PST</td>\n", "      <td>psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>AHI + ARI + SaO2 + PST</td>\n", "      <td>psg-parameter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>patient demographics + symptoms (nocturnal) + ...</td>\n", "      <td>anthropology, demographics, free-text, medical...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>questionnaire responses (sleep symptoms, behav...</td>\n", "      <td>anthropology, demographics, questionairs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>age + sex + BMI + falling asleep while driving...</td>\n", "      <td>anthropology, demographics, sleep-symptoms</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                inputs  \\\n", "0    age + gender + weight + height + BMI + neck ci...   \n", "3    BQ + ESS + Age + Gender + Height + Weight + BM...   \n", "6           age + sex + BMI + ESS + neck circumference   \n", "9              age + sex + BMI + diabetes + stroke/TIA   \n", "12   age + gender + BMI + mean heart rate during sleep   \n", "15                          clinical notes (text data)   \n", "18   age + gender + occupation + sleep duration + q...   \n", "21   age + sex + weight + height + pulse oxygen sat...   \n", "24   Age + Sex + BMI + Waist Circumference + Hip Ci...   \n", "27   STOP-BANG questionnaire + BMI + Age + Neck cir...   \n", "30   age + sex + neck circumference + abdominal cir...   \n", "33   age + BMI + neck perimeter + smoking status + ...   \n", "36   BQ + ESS + BMI + Weight + Height + Head + Neck...   \n", "39       age + biological sex + BMI + LSM + aVAT + LDL   \n", "42   gender + age + occupation + sleep duration + q...   \n", "45   Age + Sex + BMI + Height + Weight + Abdominal ...   \n", "48   age + sex + weight + height + neck circumferen...   \n", "51   Brodsky scale (tonsillar hypertrophy) + DV% (p...   \n", "54                         PSG, ECG, RR, age, ESS, ...   \n", "57   age + sex + BMI + neck circumference + waist c...   \n", "60   age + sex + BMI + ICD-10 codes + medical histo...   \n", "63   gender + age + course of disease + snoring + a...   \n", "66   3D craniofacial scans + age + BMI + neck circu...   \n", "69   age + gender + BMI + snoring + neck circumfere...   \n", "72                                    rAHI + age + sex   \n", "75        BMI + sex + HSI + CAP + ALT + AST + diabetes   \n", "78   patient demographics + laboratory blood report...   \n", "81   hypertension + waist circumference + subnasale...   \n", "84   Questionnaires + Age + BMI + Sex + Neck circum...   \n", "87   age + gender + waist circumference + neck circ...   \n", "90               protein measures + age + gender + BMI   \n", "93   age + smoking + nocturia frequency + depressio...   \n", "99   severe snore + nocturia + awakening due to the...   \n", "102  sex + age + BMI + neck circumference + snoring...   \n", "105  patient demographics (gender, age, BMI, neck c...   \n", "108  demographic data + physical exam data + clinic...   \n", "111                     BMI + age + tongue enlargement   \n", "114  PSG + age + gender + BMI + NC + AC + HC + Mall...   \n", "117                              questionnaire answers   \n", "120  age + sex + BMI + neck circumference + waist c...   \n", "123  age + gender + BMI + neck circumference + wais...   \n", "126  age + gender + BMI + neck circumference + abdo...   \n", "129  age + BMI + DBP + Mallampati score + Berlin sc...   \n", "132  sex + age + BMI + history of snoring or gaspin...   \n", "135   BMI + age + gender + neck size + height + weight   \n", "138  age + gender + BMI + neck circumference + smok...   \n", "141  age + gender + BH + BW + ForeheadC + NeckC + W...   \n", "144                             physio-psycho symptoms   \n", "147  Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, ...   \n", "150  questionnaires + anthropometric measurements +...   \n", "153  age + history of hypertension + history of dia...   \n", "156                                 age + sex + AveSBP   \n", "159                            BMI + NC + ESS + gender   \n", "162  Gender + age + weight + height + BMI + SBP + D...   \n", "165  Berlin Questionnaire + Epworth Sleepiness Scal...   \n", "168                             AHI + ARI + SaO2 + PST   \n", "171                             AHI + ARI + SaO2 + PST   \n", "174  patient demographics + symptoms (nocturnal) + ...   \n", "177  questionnaire responses (sleep symptoms, behav...   \n", "180  age + sex + BMI + falling asleep while driving...   \n", "\n", "                                      input_categories  \n", "0    anthropology, demographics, lifestyle, medical...  \n", "3             anthropology, demographics, questionairs  \n", "6             anthropology, demographics, questionairs  \n", "9       anthropology, demographics, medical-conditions  \n", "12           anthropology, demographics, psg-parameter  \n", "15                                           free-text  \n", "18   anthropology, demographics, lifestyle, medical...  \n", "21   anthropology, demographics, psg-parameter, vit...  \n", "24   anthropology, demographics, medical-conditions...  \n", "27            anthropology, demographics, questionairs  \n", "30   anthropology, demographics, lifestyle, questio...  \n", "33   anthropology, demographics, lifestyle, medical...  \n", "36            anthropology, demographics, questionairs  \n", "39             anthropology, demographics, vitals-labs  \n", "42   anthropology, demographics, lifestyle, questio...  \n", "45   anthropology, demographics, lifestyle, questio...  \n", "48   anthropology, demographics, lifestyle, medical...  \n", "51   anthropology, demographics, medical-conditions...  \n", "54                                       psg-parameter  \n", "57                          anthropology, demographics  \n", "60               anthropology, demographics, free-text  \n", "63   anthropology, demographics, lifestyle, psg-par...  \n", "66   anthropology, demographics, medical-conditions...  \n", "69   anthropology, demographics, questionairs, slee...  \n", "72                         demographics, psg-parameter  \n", "75   anthropology, demographics, medical-conditions...  \n", "78   anthropology, demographics, free-text, medical...  \n", "81      anthropology, medical-conditions, questionairs  \n", "84            anthropology, demographics, questionairs  \n", "87   anthropology, demographics, lifestyle, medical...  \n", "90             anthropology, demographics, vitals-labs  \n", "93   anthropology, demographics, lifestyle, questio...  \n", "99                        anthropology, sleep-symptoms  \n", "102  anthropology, demographics, lifestyle, medical...  \n", "105                        demographics, psg-parameter  \n", "108              demographics, free-text, questionairs  \n", "111                         anthropology, demographics  \n", "114          anthropology, demographics, psg-parameter  \n", "117                                       questionairs  \n", "120  anthropology, demographics, lifestyle, medical...  \n", "123  anthropology, demographics, lifestyle, medical...  \n", "126  anthropology, demographics, lifestyle, medical...  \n", "129  anthropology, demographics, questionairs, vita...  \n", "132         anthropology, demographics, sleep-symptoms  \n", "135                         anthropology, demographics  \n", "138  anthropology, demographics, lifestyle, questio...  \n", "141  anthropology, demographics, questionairs, slee...  \n", "144                                       questionairs  \n", "147                                       questionairs  \n", "150  anthropology, demographics, medical-conditions...  \n", "153  anthropology, demographics, medical-conditions...  \n", "156                          demographics, vitals-labs  \n", "159           anthropology, demographics, questionairs  \n", "162  anthropology, demographics, psg-parameter, que...  \n", "165                         anthropology, questionairs  \n", "168                                      psg-parameter  \n", "171                                      psg-parameter  \n", "174  anthropology, demographics, free-text, medical...  \n", "177           anthropology, demographics, questionairs  \n", "180         anthropology, demographics, sleep-symptoms  "]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["df[[\"inputs\", \"input_categories\"]][mask]"]}, {"cell_type": "code", "execution_count": 121, "id": "961aad2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Entries per human annotator:\n", "<PERSON>: 20\n", "Franz: 20\n", "Tony: 20\n"]}], "source": ["import pandas as pd\n", "\n", "# Load your data (assuming df is already loaded)\n", "# df = pd.read_excel('ap3-literature-review.xlsx')\n", "\n", "# 1. List all entries and count how many entries per human\n", "# Extract human names from every third row starting from index 2 (0-indexed)\n", "human_names = []\n", "for i in range(2, len(df), 3):  # Start at index 2, step by 3\n", "    if i < len(df):\n", "        human_name = df.iloc[i, 0]  # First column of the human output row\n", "        if pd.notna(human_name):  # Only add non-null values\n", "            human_names.append(human_name)\n", "\n", "# Count entries per human\n", "from collections import Counter\n", "entries_per_human = Counter(human_names)\n", "print(\"Entries per human annotator:\")\n", "for human, count in entries_per_human.items():\n", "    print(f\"{human}: {count}\")\n", "\n", "# 2. Column-wise evaluation for \"data_base\", \"dataset\", \"population\"\n", "columns_to_evaluate = [\"data_base\", \"dataset\", \"population\", \"demographic_specifics\"]\n", "columns_to_evaluate = all_output_columns\n", "evaluation_results = {}\n", "\n", "for col in columns_to_evaluate:\n", "    if col in df.columns:\n", "        evaluation_results[col] = {\n", "            'correct_count': 0,\n", "            'wrong_count': 0,\n", "            'other_count': 0,\n", "            'other_values': []\n", "        }\n", "        \n", "        # Analyze every third row starting from index 1 (human evaluation rows)\n", "        for i in range(1, len(df), 3):  # Human evaluation rows\n", "            if i < len(df):\n", "                eval_value = df.iloc[i, df.columns.get_loc(col)]\n", "                if pd.notna(eval_value):\n", "                    eval_str = str(eval_value).lower()\n", "                    if 'correct' in eval_str:\n", "                        evaluation_results[col]['correct_count'] += 1\n", "                    elif 'wrong' in eval_str:\n", "                        evaluation_results[col]['wrong_count'] += 1\n", "                    else:\n", "                        evaluation_results[col]['other_count'] += 1\n", "                        evaluation_results[col]['other_values'].append(eval_value)\n"]}, {"cell_type": "code", "execution_count": 122, "id": "250f5603", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "EVALUATION RESULTS\n", "==================================================\n", "\n", "Column: 'data_base'\n", "  Correct: 57\n", "  Wrong: 0\n", "  Other: 0\n", "\n", "Column: 'dataset'\n", "  Correct: 56\n", "  Wrong: 1\n", "  Other: 0\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'dataset':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'proprietary'\n", "    Human Output: 'Sleep Medicine Center of a tertiary hospital in southern  Taiwan'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'other'\n", "    Human Output: 'institutional database'\n", "\n", "\n", "Column: 'population'\n", "  Correct: 57\n", "  Wrong: 1\n", "  Other: 0\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'population':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'adults'\n", "    Human Output: 'excluded younger than 13'\n", "\n", "\n", "Column: 'demographic_specifics'\n", "  Correct: 47\n", "  Wrong: 1\n", "  Other: 6\n", "  Other values: {'unknown', 'to few', 'to much'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'demographic_specifics':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'aged 3–18 years, 1665 boys, 799 girls'\n", "    Human Output: 'age, sex, neck circumference, abdominal circumference, hip circumference, and Body mass index (BMI)'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (Franz): unknown\n", "    AI Output:    'age (grouped by decade), sex, BMI'\n", "    Human Output: 'not specified'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): to much\n", "    AI Output:    'Gender (M/F), Age, BMI, Neck circumference'\n", "    Human Output: 'non actually reported'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'not specified'\n", "    Human Output: '95 patients'\n", "\n", "  Entry 5:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    '19-90 years, mixed sex, suspected OSA patients'\n", "    Human Output: '1042'\n", "\n", "  Entry 6:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Age: 48.5-50.7 yrs (mean); Sex: ~50% male; BMI: 35.8-36.7 kg/m2 (mean)'\n", "    Human Output: '133 HST group, 532 PSG group'\n", "\n", "  Entry 7:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Taiwanese adults, mean age 45.1±14.2 years, 59.6% men, BMI 24.5±4.3 kg/m2'\n", "    Human Output: '540'\n", "\n", "  Entry 8:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Age: 50.7 +/- 12.2 years, BMI: 33.3 +/- 6.7 kg/m2, Sex: 2061 men, 629 women, NC: 41.9 +/- 3.6 cm'\n", "    Human Output: '2690'\n", "\n", "\n", "Column: 'algorithm_approach'\n", "  Correct: 50\n", "  Wrong: 4\n", "  Other: 3\n", "  Other values: {'to much', '?'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'algorithm_approach':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'classical ML + ensemble'\n", "    Human Output: 'classical ML, RF wird vermutlich als ensemble gesehen'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'classical ML + ensemble'\n", "    Human Output: 'classical ML, ensemble is RF, boosting'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'classical ML + ensemble'\n", "    Human Output: 'classical ML (s.o.), ANN'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'hybrid'\n", "    Human Output: 'classical ML (HMM)'\n", "\n", "  Entry 5:\n", "    <PERSON><PERSON> (<PERSON>): to much\n", "    AI Output:    'classical ML + signal processing'\n", "    Human Output: 'classical ML'\n", "\n", "  Entry 6:\n", "    <PERSON><PERSON> (<PERSON>): ?\n", "    AI Output:    'classical ML'\n", "    Human Output: 'multivariate regression analysis'\n", "\n", "  Entry 7:\n", "    <PERSON><PERSON> (<PERSON>): ?\n", "    AI Output:    'classical ML + hybrid'\n", "    Human Output: 'Rough Set and Mahalanob<PERSON> distance'\n", "\n", "\n", "Column: 'output_tasks'\n", "  Correct: 56\n", "  Wrong: 0\n", "  Other: 1\n", "  Other values: {'to much'}\n", "\n", "Column: 'output_type'\n", "  Correct: 57\n", "  Wrong: 0\n", "  Other: 0\n", "\n", "Column: 'output_task_count'\n", "  Correct: 0\n", "  Wrong: 0\n", "  Other: 0\n", "\n", "Column: 'output_classes'\n", "  Correct: 50\n", "  Wrong: 3\n", "  Other: 2\n", "  Other values: {'to few'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'output_classes':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'Obstructive Sleep Apnea'\n", "    Human Output: 'no or mild OSA, moderate to severe OSA'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'Normal OSAS + Mild OSAS + Moderate OSAS + Severe OSAS'\n", "    Human Output: 'mild, moderate and severe'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'moderate or severe OSA + non-moderate or severe OSA'\n", "    Human Output: 'confusing output, its AHI < 15 and AHI > 15'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Obstructive Sleep Apnea (OSA)'\n", "    Human Output: 'plus non-osa'\n", "\n", "  Entry 5:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Obstructive Sleep Apnea'\n", "    Human Output: 'RDI>10,<10'\n", "\n", "\n", "Column: 'output_counts'\n", "  Correct: 1\n", "  Wrong: 1\n", "  Other: 0\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'output_counts':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    '2'\n", "    Human Output: ' '\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'N/A (continuous output)'\n", "    Human Output: '>10, >15, >20'\n", "\n", "\n", "Column: 'output_classes_category'\n", "  Correct: 0\n", "  Wrong: 0\n", "  Other: 0\n", "\n", "Column: 'output_text'\n", "  Correct: 50\n", "  Wrong: 3\n", "  Other: 2\n", "  Other values: {'to few'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'output_text':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'Moderate to severe OSA risk, Severe OSA risk, Normal to mild OSA, Moderate OSA, Severe OSA'\n", "    Human Output: 'too much'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Prediction of presence or absence of Obstructive Sleep Apnea (OSA)'\n", "    Human Output: 'cost predictions'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'Predicted Apnea-Hypopnea Index (AHIpred)'\n", "    Human Output: 'and OSA Diagnoses AHI > 15/h'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'prediction of Apnea-Hypopnea Index (AHI)'\n", "    Human Output: 'AHI >10, >15, >20'\n", "\n", "\n", "Column: 'input_categories(demographics, antropohology, psg, questionairs)'\n", "  Correct: 0\n", "  Wrong: 0\n", "  Other: 0\n", "\n", "Column: 'inputs'\n", "  Correct: 43\n", "  Wrong: 0\n", "  Other: 13\n", "  Other values: {'to few', 'to much'}\n", "\n", "Column: 'metric_values'\n", "  Correct: 52\n", "  Wrong: 1\n", "  Other: 3\n", "  Other values: {'to few'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'metric_values':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (Franz): to few\n", "    AI Output:    '0.761 Accuracy, 0.721 Sensitivity, 0.808 Specificity, 0.805 PPV, 0.724 NPV, 0.815 AUC (Model 2; using only protein measures)'\n", "    Human Output: 'model 1 is missing with all inputs'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'TAN39: AUC 78.6%, sensitivity 81.7%, specificity 54.8%, PPV 78.5%, NPV 62.5%, accuracy 72.6%; TAN13: AUC 75.5%, sensitivity 81.2%, specificity 47.8%, PPV 75.5%, NPV 58.6%, accuracy 69.9%'\n", "    Human Output: 'missing values for NB13 and NB39'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    '0.71 true positive rate (highest), 0.15 false positive rate (lowest) across different settings and methods.'\n", "    Human Output: 'other values'\n", "\n", "\n", "Column: 'used_metrics'\n", "  Correct: 55\n", "  Wrong: 0\n", "  Other: 1\n", "  Other values: {'too much'}\n", "\n", "Column: 'test_set_size'\n", "  Correct: 53\n", "  Wrong: 3\n", "  Other: 0\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'test_set_size':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'https://doi.org/10.1 records'\n", "    Human Output: '5208 patient entries but used SMOTE to balance dataset and get 11535 patients resulting in 2308 test samples'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    '267 participants'\n", "    Human Output: 'even so in the end 267 patients where evaluated, each model (4-fold) only tested on 25% of it'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'https://doi.org/10.2 suspected OSA patients'\n", "    Human Output: '1042'\n", "\n", "\n", "Column: 'training_set_size'\n", "  Correct: 54\n", "  Wrong: 2\n", "  Other: 0\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'training_set_size':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    '4166 records'\n", "    Human Output: '9227'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'nan'\n", "    Human Output: '1042'\n", "\n", "\n", "Column: 'impact_evaluation'\n", "  Correct: 19\n", "  Wrong: 30\n", "  Other: 4\n", "  Other values: {'unknown', 'to few'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'impact_evaluation':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'age, sex, BMI, ESS, neck circumference'\n", "    Human Output: 'important features using LR, no impact evaluation'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'AHI + age + sex + BMI + diabetes + stroke/TIA'\n", "    Human Output: 'none  '\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): wrong (?)\n", "    AI Output:    'no'\n", "    Human Output: 'not sure if impact evaluation is the correct term here, checked relationship between participant characteristics and OSA (gender, age, BMI) in data, not for the model; calibration curves'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'comparison across AHI thresholds (5 vs 10), comparison with PSQ features, feature importance analysis'\n", "    Human Output: 'impact evaluation not correct word '\n", "\n", "  Entry 5:\n", "    <PERSON><PERSON> (Franz): unknown\n", "    AI Output:    'sex + age + BMI + ICD-10 codes + alcohol abuse history'\n", "    Human Output: 'SHAP analysis of those parameter, but not a performance test'\n", "\n", "  Entry 6:\n", "    <PERSON><PERSON> (Franz): unknown\n", "    AI Output:    'age, gender, BMI, neck circumference, abdominal circumference, nocturia, smoking, course of disease, blood pressure, various symptoms'\n", "    Human Output: 'importance analysis'\n", "\n", "  Entry 7:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'combining 3D morphometrics with anthropometrics and questionnaire data'\n", "    Human Output: 'no impact on finished model was tested'\n", "\n", "  Entry 8:\n", "    <PERSON><PERSON> (Franz): unknown\n", "    AI Output:    'CPAP adherence, leaks'\n", "    Human Output: 'not sure if impact evaluation is the correct term here'\n", "\n", "  Entry 9:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'factors (sex, BMI, HSI, CAP) are used as inputs to the model for OSA prediction, but performance variation across different subgroups (e.g., varying AHI levels, age groups) is not explicitly reported.'\n", "    Human Output: 'sex, BMI, the CAP, HSI was tested (in a table)'\n", "\n", "  Entry 10:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'age, sex, BMI, waist circumference, neck circumference, EDS, snoring frequency, snoring volume, fasting plasma glucose, LAP, uric acid, VAI, hypertension, heart attack comorbidity, TyG, triglycerides, systolic blood pressure'\n", "    Human Output: 'none'\n", "\n", "  Entry 11:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'feature importance (waist circumference, loudness of snoring)'\n", "    Human Output: 'statement is correct but ist not impact evaluation'\n", "\n", "  Entry 12:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'sleep disorders (OAHI severity), age, sex, BMI'\n", "    Human Output: 'none'\n", "\n", "  Entry 13:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'evaluated impact of age, body mass, sleep habits, depression, and data imbalance on algorithm performance.'\n", "    Human Output: 'none'\n", "\n", "  Entry 14:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'impact of BMI on AHI, effectiveness of STOP-BANG questionnaire, feature relevance for OSA classification'\n", "    Human Output: 'none'\n", "\n", "  Entry 15:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'gender, age, neck circumference, witnessed apneas, craniofacial and upper-airway abnormalities, nocturia'\n", "    Human Output: 'none'\n", "\n", "  Entry 16:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'age, BMI, tongue enlargement (as features in the algorithm)'\n", "    Human Output: 'first/third trimister'\n", "\n", "  Entry 17:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'gender + age + BMI + NC + AC + Mallampati index'\n", "    Human Output: 'none'\n", "\n", "  Entry 18:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'sleep disorders ahi'\n", "    Human Output: 'none'\n", "\n", "  Entry 19:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'age, sex, BMI, neck circumference, waist circumference, hypertension, snoring status, feelings of anxiety or sadness, Epworth Sleepiness Scale score'\n", "    Human Output: 'none'\n", "\n", "  Entry 20:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'poor'\n", "    Human Output: ' '\n", "\n", "  Entry 21:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'nan'\n", "    Human Output: 'was done'\n", "\n", "  Entry 22:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'unknown'\n", "    Human Output: 'none'\n", "\n", "  Entry 23:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'sleep disorders (OSA severity), age, sex, BMI, hypertension, routine medicine use'\n", "    Human Output: 'none'\n", "\n", "\n", "Column: 'generalization_testing'\n", "  Correct: 48\n", "  Wrong: 4\n", "  Other: 2\n", "  Other values: {'no'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'generalization_testing':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'unknown'\n", "    Human Output: 'none'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'across different sleep centers'\n", "    Human Output: 'external validation'\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): nan\n", "    AI Output:    'across populations + across sleep centers'\n", "    Human Output: 'external validation'\n", "\n", "  Entry 4:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'unknown'\n", "    Human Output: 'no'\n", "\n", "  Entry 5:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'unknown'\n", "    Human Output: 'none'\n", "\n", "\n", "Column: 'data_preprocessing'\n", "  Correct: 51\n", "  Wrong: 1\n", "  Other: 3\n", "  Other values: {'yes', 'to few'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'data_preprocessing':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'filtering + one-hot encoding + normalization + feature extraction'\n", "    Human Output: 'no normalization, no feature extraction, SMOTE'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (Franz): to few\n", "    AI Output:    'filtering + normalization + feature extraction'\n", "    Human Output: 'removing missin  and repeated records, SMOTE, Doc2Vec '\n", "\n", "  Entry 3:\n", "    <PERSON><PERSON> (<PERSON>): to few\n", "    AI Output:    'categorization + normalization'\n", "    Human Output: 'feature extraction?'\n", "\n", "\n", "Column: 'validation_type'\n", "  Correct: 50\n", "  Wrong: 3\n", "  Other: 1\n", "  Other values: {'yes'}\n", "\n", "  WRONG ENTRIES - AI vs Human Output for 'validation_type':\n", "  ------------------------------------------------------------\n", "  Entry 1:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'five-fold cross validation + hold-out test set + cross-validation dataset wise'\n", "    Human Output: '5fold cv + hold out test + external'\n", "\n", "  Entry 2:\n", "    <PERSON><PERSON> (<PERSON>): wrong\n", "    AI Output:    'unknown'\n", "    Human Output: 'cross-validation'\n", "\n", "\n", "Column: 'valid_paper'\n", "  Correct: 22\n", "  Wrong: 0\n", "  Other: 33\n", "  Other values: {'yes'}\n", "\n", "==================================================\n", "SUMMARY TABLE\n", "==================================================\n", "                                               Column  Correct  Wrong  Other  \\\n", "0                                           data_base       57      0      0   \n", "1                                             dataset       56      1      0   \n", "2                                          population       57      1      0   \n", "3                               demographic_specifics       47      1      6   \n", "4                                  algorithm_approach       50      4      3   \n", "5                                        output_tasks       56      0      1   \n", "6                                         output_type       57      0      0   \n", "7                                   output_task_count        0      0      0   \n", "8                                      output_classes       50      3      2   \n", "9                                       output_counts        1      1      0   \n", "10                            output_classes_category        0      0      0   \n", "11                                        output_text       50      3      2   \n", "12  input_categories(demographics, antropohology, ...        0      0      0   \n", "13                                             inputs       43      0     13   \n", "14                                      metric_values       52      1      3   \n", "15                                       used_metrics       55      0      1   \n", "16                                      test_set_size       53      3      0   \n", "17                                  training_set_size       54      2      0   \n", "18                                  impact_evaluation       19     30      4   \n", "19                             generalization_testing       48      4      2   \n", "20                                 data_preprocessing       51      1      3   \n", "21                                    validation_type       50      3      1   \n", "22                                        valid_paper       22      0     33   \n", "\n", "    Total  \n", "0      57  \n", "1      57  \n", "2      58  \n", "3      54  \n", "4      57  \n", "5      57  \n", "6      57  \n", "7       0  \n", "8      55  \n", "9       2  \n", "10      0  \n", "11     55  \n", "12      0  \n", "13     56  \n", "14     56  \n", "15     56  \n", "16     56  \n", "17     56  \n", "18     53  \n", "19     54  \n", "20     55  \n", "21     54  \n", "22     55  \n", "\n", "==================================================\n", "DETAILED WRONG ENTRIES COMPARISON\n", "==================================================\n", "                 Column Annotator                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            AI_Output                                                                                                                                                                                     Human_Output  Entry_Index\n", "                dataset    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          proprietary                                                                                                                                 Sleep Medicine Center of a tertiary hospital in southern  Taiwan            9\n", "                dataset      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                other                                                                                                                                                                           institutional database           47\n", "             population    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               adults                                                                                                                                                                         excluded younger than 13            8\n", "  demographic_specifics    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                aged 3–18 years, 1665 boys, 799 girls                                                                                              age, sex, neck circumference, abdominal circumference, hip circumference, and Body mass index (BMI)           11\n", "  demographic_specifics     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    age (grouped by decade), sex, BMI                                                                                                                                                                                    not specified           21\n", "  demographic_specifics     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           <PERSON> (M/F), Age, BMI, Neck circumference                                                                                                                                                                            non actually reported           35\n", "  demographic_specifics      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        not specified                                                                                                                                                                                      95 patients           49\n", "  demographic_specifics      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       19-90 years, mixed sex, suspected OSA patients                                                                                                                                                                                             1042           50\n", "  demographic_specifics      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Age: 48.5-50.7 yrs (mean); Sex: ~50% male; BMI: 35.8-36.7 kg/m2 (mean)                                                                                                                                                                     133 HST group, 532 PSG group           51\n", "  demographic_specifics      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Taiwanese adults, mean age 45.1±14.2 years, 59.6% men, BMI 24.5±4.3 kg/m2                                                                                                                                                                                              540           52\n", "  demographic_specifics      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Age: 50.7 +/- 12.2 years, BMI: 33.3 +/- 6.7 kg/m2, Sex: 2061 men, 629 women, NC: 41.9 +/- 3.6 cm                                                                                                                                                                                             2690           53\n", "     algorithm_approach    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              classical ML + ensemble                                                                                                                                            classical ML, RF wird vermutlich als ensemble gesehen            1\n", "     algorithm_approach    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              classical ML + ensemble                                                                                                                                                           classical ML, ensemble is RF, boosting            3\n", "     algorithm_approach    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              classical ML + ensemble                                                                                                                                                                         classical ML (s.o.), ANN            5\n", "     algorithm_approach     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               hybrid                                                                                                                                                                               classical ML (HMM)           25\n", "     algorithm_approach     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     classical ML + signal processing                                                                                                                                                                                     classical ML           39\n", "     algorithm_approach      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         classical ML                                                                                                                                                                 multivariate regression analysis           53\n", "     algorithm_approach      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                classical ML + hybrid                                                                                                                                                               Rough Set and <PERSON><PERSON><PERSON><PERSON> distance           54\n", "           output_tasks     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Sleep-Related-Breathing Disorder Diagnosis + Other Diagnosis Sleep-Related-Breathing Disorder Diagnosis is not correct, the real category does not exist in our specification, most accurate would be Other Diagnosis, recheck if this is from abstract/paper           25\n", "      output_task_count      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1                                                                                                                                                                                                            41\n", "         output_classes    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              Obstructive Sleep Apnea                                                                                                                                                           no or mild OSA, moderate to severe OSA            3\n", "         output_classes    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Normal OSAS + Mild OSAS + Moderate OSAS + Severe OSAS                                                                                                                                                                        mild, moderate and severe           16\n", "         output_classes     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  moderate or severe OSA + non-moderate or severe OSA                                                                                                                                                      confusing output, its AHI < 15 and AHI > 15           24\n", "         output_classes      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Obstructive Sleep Apnea (OSA)                                                                                                                                                                                     plus non-osa           41\n", "         output_classes      <PERSON>tructive Sleep Apnea                                                                                                                                                                                       RDI>10,<10           60\n", "          output_counts      <PERSON>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    2                                                                                                                                                                                                            41\n", "          output_counts      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              <PERSON> (continuous output)                                                                                                                                                                                    >10, >15, >20           59\n", "output_classes_category    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  NaN                                                                                                                                                             AHI (>= 5), AHI (>= 15), AHI (>= 30)            1\n", "output_classes_category    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  NaN                                                                                                                                                                                                4            2\n", "            output_text    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Moderate to severe OSA risk, Severe OSA risk, Normal to mild OSA, Moderate OSA, Severe OSA                                                                                                                                                                                         too much           20\n", "            output_text      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Prediction of presence or absence of Obstructive Sleep Apnea (OSA)                                                                                                                                                                                 cost predictions           51\n", "            output_text      <PERSON>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Predicted Apnea-Hypopnea Index (AHIpred)                                                                                                                                                                     and OSA Diagnoses AHI > 15/h           53\n", "            output_text      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             prediction of Apnea-Hypopnea Index (AHI)                                                                                                                                                                                AHI >10, >15, >20           59\n", "                 inputs     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     rAHI + age + sex                                                                                                                                                                                             rAHI           25\n", "                 inputs     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           PSG + age + gender + BMI + NC + AC + HC + Mallampati index                                                                                                                                                                                        no PSG !?           38\n", "                 inputs      Tony age + gender + BMI + neck circumference + abdominal circumference + craniofacial abnormalities + snoring + witnessed apneas + choking + vehicle crashes + refreshing sleep + humor alterations + nocturia + restless sleep + decreased libido + morning headaches + alcohol consumption + smoking + sedatives + ESS + concentration decrease + atrial fibrillation + stroke + myocardial infarction + pulmonary hypertension + congestive heart failure + diabetes + dyslipidemia + renal failure + hypothyroidism + gastroesophageal reflux + hypertension + driver status + coffee consumption + daytime sleepiness + family history/genetics + depression/anxiety                                                                                                                                                            race, upper-airway abnoramlities, ..?           42\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   age + BMI + DBP + Mallampati score + Berlin scores                                                                                                                                                      cigarette smoking, diabetes, hypertension,…           43\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               physio-psycho symptoms                                                                                                                     41 symptoms (physical, psychological, cognitive and motor funtion diability)           48\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Symptoms (HB, DT, BW, IHR, BT, HD, US, TG/TC, NC, NP, SD, DS, NA, MD, CS, FG, LP, EM, TD)                                                                                                                     41 symptoms (physical, psychological, cognitive and motor funtion diability)           49\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               questionnaires + anthropometric measurements + age + sex + hypertension + medicine use                                                                                                                                                                                    more detailed           50\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                 age + history of hypertension + history of diabetes mellitus + BMI + neck circumference + snoring (questionnaire) + observed stop breathing (questionnaire) + restless legs (questionnaire) + desire/interest in sex (questionnaire)                                                                                                                                                                                 see supopliments           51\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   age + sex + AveSBP                                                                                                                                                                                      see Table 2           52\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                   patient demographics + symptoms (nocturnal) + symptoms (daytime) + bed partner observations + medical history + medications + social history + anthropometric measures + physical examination findings + clinical score + Epworth Sleepiness Scale                                                                                                                                                                                        see paper           58\n", "                 inputs      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    age + sex + BMI + falling asleep while driving + snoring every night + stopping breathing during sleep + total number of symptoms                                                                                                                                                               physically examination results HNO           60\n", "          metric_values     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         0.761 Accuracy, 0.721 Sensitivity, 0.808 Specificity, 0.805 PPV, 0.724 NPV, 0.815 AUC (Model 2; using only protein measures)                                                                                                                                                               model 1 is missing with all inputs           31\n", "          metric_values      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                           TAN39: AUC 78.6%, sensitivity 81.7%, specificity 54.8%, PPV 78.5%, NPV 62.5%, accuracy 72.6%; TAN13: AUC 75.5%, sensitivity 81.2%, specificity 47.8%, PPV 75.5%, NPV 58.6%, accuracy 69.9%                                                                                                                                                                 missing values for NB13 and NB39           42\n", "          metric_values      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          0.71 true positive rate (highest), 0.15 false positive rate (lowest) across different settings and methods.                                                                                                                                                                                     other values           46\n", "           used_metrics    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          accuracy, sensitivity, specificity, F1 score, ROC AUC score                                                                                                                                                                                 no ROC AUC score           10\n", "          test_set_size    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         https://doi.org/10.1 records                                                                                     5208 patient entries but used SMOTE to balance dataset and get 11535 patients resulting in 2308 test samples            2\n", "          test_set_size     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     267 participants                                                                                                    even so in the end 267 patients where evaluated, each model (4-fold) only tested on 25% of it           23\n", "          test_set_size      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          https://doi.org/10.2 suspected OSA patients                                                                                                                                                                                             1042           50\n", "      training_set_size    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         4166 records                                                                                                                                                                                             9227            2\n", "      training_set_size      <PERSON>                                                                                                                                                                                             1042           50\n", "      impact_evaluation    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               age, sex, BMI, ESS, neck circumference                                                                                                                                                important features using LR, no impact evaluation            3\n", "      impact_evaluation    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        AHI + age + sex + BMI + diabetes + stroke/TIA                                                                                                                                                                                           none              4\n", "      impact_evaluation    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   no       not sure if impact evaluation is the correct term here, checked relationship between participant characteristics and OSA (gender, age, BMI) in data, not for the model; calibration curves            5\n", "      impact_evaluation    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                comparison across AHI thresholds (5 vs 10), comparison with PSQ features, feature importance analysis                                                                                                                                                              impact evaluation not correct word            11\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               sex + age + BMI + ICD-10 codes + alcohol abuse history                                                                                                                                     SHAP analysis of those parameter, but not a performance test           21\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                age, gender, BMI, neck circumference, abdominal circumference, nocturia, smoking, course of disease, blood pressure, various symptoms                                                                                                                                                                              importance analysis           22\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               combining 3D morphometrics with anthropometrics and questionnaire data                                                                                                                                                           no impact on finished model was tested           23\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                CPAP adherence, leaks                                                                                                                                           not sure if impact evaluation is the correct term here           25\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                             factors (sex, BMI, HSI, CAP) are used as inputs to the model for OSA prediction, but performance variation across different subgroups (e.g., varying AHI levels, age groups) is not explicitly reported.                                                                                                                                                   sex, BMI, the CAP, HSI was tested (in a table)           26\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                     age, sex, BMI, waist circumference, neck circumference, EDS, snoring frequency, snoring volume, fasting plasma glucose, LAP, uric acid, VAI, hypertension, heart attack comorbidity, TyG, triglycerides, systolic blood pressure                                                                                                                                                                                             none           27\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        feature importance (waist circumference, loudness of snoring)                                                                                                                                               statement is correct but ist not impact evaluation           28\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       sleep disorders (OAHI severity), age, sex, BMI                                                                                                                                                                                             none           31\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           evaluated impact of age, body mass, sleep habits, depression, and data imbalance on algorithm performance.                                                                                                                                                                                             none           32\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             impact of BMI on AHI, effectiveness of STOP-BANG questionnaire, feature relevance for OSA classification                                                                                                                                                                                             none           35\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             gender, age, neck circumference, witnessed apneas, craniofacial and upper-airway abnormalities, nocturia                                                                                                                                                                                             none           36\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          age, BMI, tongue enlargement (as features in the algorithm)                                                                                                                                                                            first/third trimister           37\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      gender + age + BMI + NC + AC + Mallampati index                                                                                                                                                                                             none           38\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  sleep disorders ahi                                                                                                                                                                                             none           39\n", "      impact_evaluation     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 age, sex, BMI, neck circumference, waist circumference, hypertension, snoring status, feelings of anxiety or sadness, Epworth Sleepiness Scale score                                                                                                                                                                                             none           40\n", "      impact_evaluation      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 poor                                                                                                                                                                                                            41\n", "      impact_evaluation      <PERSON>                                                                                                                                                                                         was done           45\n", "      impact_evaluation      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              unknown                                                                                                                                                                                             none           48\n", "      impact_evaluation      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    sleep disorders (OSA severity), age, sex, BMI, hypertension, routine medicine use                                                                                                                                                                                             none           50\n", " generalization_testing    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              unknown                                                                                                                                                                                             none            2\n", " generalization_testing    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       across different sleep centers                                                                                                                                                                              external validation            4\n", " generalization_testing    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            across populations + across sleep centers                                                                                                                                                                              external validation            8\n", " generalization_testing      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              unknown                                                                                                                                                                                               no           41\n", " generalization_testing      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              unknown                                                                                                                                                                                             none           48\n", "     data_preprocessing    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    filtering + one-hot encoding + normalization + feature extraction                                                                                                                                                   no normalization, no feature extraction, SMOTE            2\n", "     data_preprocessing     Franz                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       filtering + normalization + feature extraction                                                                                                                                           removing missin  and repeated records, SMOTE, Doc2Vec            21\n", "     data_preprocessing      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       categorization + normalization                                                                                                                                                                              feature extraction?           50\n", "        validation_type    Miriam                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       five-fold cross validation + hold-out test set + cross-validation dataset wise                                                                                                                                                              5fold cv + hold out test + external            8\n", "        validation_type      Tony                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              unknown                                                                                                                                                                                 cross-validation           50\n"]}], "source": ["# Print evaluation results with AI vs Human comparison for wrong entries\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"EVALUATION RESULTS\")\n", "print(\"=\"*50)\n", "\n", "for col in columns_to_evaluate:\n", "    if col in evaluation_results:\n", "        print(f\"\\nColumn: '{col}'\")\n", "        print(f\"  Correct: {evaluation_results[col]['correct_count']}\")\n", "        print(f\"  Wrong: {evaluation_results[col]['wrong_count']}\")\n", "        print(f\"  Other: {evaluation_results[col]['other_count']}\")\n", "        if evaluation_results[col]['other_values']:\n", "            print(f\"  Other values: {set(evaluation_results[col]['other_values'])}\")\n", "        \n", "        # Show AI vs Human output for wrong entries\n", "        if evaluation_results[col]['wrong_count'] > 0:\n", "            print(f\"\\n  WRONG ENTRIES - AI vs Human Output for '{col}':\")\n", "            print(\"  \" + \"-\"*60)\n", "            \n", "            wrong_count = 0\n", "            for i in range(1, len(df), 3):  # Human evaluation rows\n", "                if i < len(df):\n", "                    eval_value = df.iloc[i, df.columns.get_loc(col)]\n", "                    # if 'wrong' in str(eval_value).lower():\n", "                    human_output = df.iloc[i+1, df.columns.get_loc(col)] if i+1 < len(df) else \"N/A\"\n", "                    if pd.notna(human_output)  and human_output != \"N/A\" and \"correct\" not in str(eval_value).lower():\n", "                        wrong_count += 1\n", "                        \n", "                        # Get AI output (row i-1) and human output (row i+1)\n", "                        ai_output = df.iloc[i-1, df.columns.get_loc(col)] if i-1 >= 0 else \"N/A\"\n", "                        human_name = df.iloc[i+1, 0] if i+1 < len(df) else \"Unknown\"\n", "                        \n", "                        print(f\"  Entry {wrong_count}:\")\n", "                        print(f\"    Eval ({human_name}): {eval_value}\")\n", "                        print(f\"    AI Output:    '{ai_output}'\")\n", "                        print(f\"    Human Output: '{human_output}'\")\n", "                        print()\n", "\n", "# Create summary DataFrame\n", "summary_data = []\n", "for col in columns_to_evaluate:\n", "    if col in evaluation_results:\n", "        summary_data.append({\n", "            'Column': col,\n", "            'Correct': evaluation_results[col]['correct_count'],\n", "            'Wrong': evaluation_results[col]['wrong_count'],\n", "            'Other': evaluation_results[col]['other_count'],\n", "            'Total': evaluation_results[col]['correct_count'] + \n", "                    evaluation_results[col]['wrong_count'] + \n", "                    evaluation_results[col]['other_count']\n", "        })\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"SUMMARY TABLE\")\n", "print(\"=\"*50)\n", "print(summary_df)\n", "\n", "# Also create a detailed comparison DataFrame for wrong entries\n", "wrong_entries_data = []\n", "for col in columns_to_evaluate:\n", "    if col in df.columns:\n", "        for i in range(1, len(df), 3):  # Human evaluation rows\n", "            if i < len(df):\n", "                eval_value = df.iloc[i, df.columns.get_loc(col)]\n", "                human_output = df.iloc[i+1, df.columns.get_loc(col)] if i+1 < len(df) else \"N/A\"\n", "                if pd.notna(human_output)  and human_output != \"N/A\" and \"correct\" not in str(eval_value).lower():\n", "                    ai_output = df.iloc[i-1, df.columns.get_loc(col)] if i-1 >= 0 else \"N/A\"\n", "                    human_name = df.iloc[i+1, 0] if i+1 < len(df) else \"Unknown\"\n", "                    \n", "                    wrong_entries_data.append({\n", "                        'Column': col,\n", "                        'Annotator': human_name,\n", "                        'AI_Output': ai_output,\n", "                        'Human_Output': human_output,\n", "                        'Entry_Index': i//3 + 1\n", "                    })\n", "\n", "if wrong_entries_data:\n", "    wrong_entries_df = pd.DataFrame(wrong_entries_data)\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"DETAILED WRONG ENTRIES COMPARISON\")\n", "    print(\"=\"*50)\n", "    print(wrong_entries_df.to_string(index=False))"]}], "metadata": {"kernelspec": {"display_name": "sleepy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}