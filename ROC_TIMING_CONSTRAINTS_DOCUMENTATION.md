# Enhanced ROC Analysis with Timing Constraints

## Overview

The `create_model_gender_roc.py` script now supports timing constraints for diagnoses and medications relative to sleep studies. This enhancement allows for more clinically relevant analysis by only including medical events that occurred within reasonable timeframes around the sleep study.

## New Features

### 1. Timing Constraint Modes

**Strict Mode (Default)**
- No limit on diagnoses/medications before sleep study
- Includes diagnoses/medications up to 1 year after sleep study
- Usage: `--timing-mode strict` (or omit for default)

**Moderate Mode**
- Includes diagnoses/medications from 6 months before sleep study
- Includes diagnoses/medications up to 1 year after sleep study
- Usage: `--timing-mode moderate`

**Custom Mode**
- Specify exact number of days before and after sleep study
- Usage: `--timing-mode custom --days-before 90 --days-after 730`

**No Timing Constraints**
- Traditional approach: use all available data regardless of timing
- Usage: `--no-timing-constraints`

### 2. Command Line Options

```bash
--model {stop_bang,random_forest,xgboost}
    Model to analyze (default: random_forest)

--no-timing-constraints
    Disable timing constraints (use all data)

--timing-mode {strict,moderate,custom}
    Timing constraint mode (default: strict)

--days-before N
    Days before sleep study to include (for custom mode)

--days-after N  
    Days after sleep study to include (default: 365)

--font-size N
    Font size for plots (default: 16)
```

## Usage Examples

### Basic Usage with Timing Constraints

```bash
# Default: Random Forest with strict timing
python create_model_gender_roc.py

# Random Forest with moderate timing
python create_model_gender_roc.py --timing-mode moderate

# STOP-BANG with strict timing
python create_model_gender_roc.py --model stop_bang
```

### Custom Timing Constraints

```bash
# 3 months before, 2 years after sleep study
python create_model_gender_roc.py --timing-mode custom --days-before 90 --days-after 730

# Only medications/diagnoses after sleep study (no before limit)
python create_model_gender_roc.py --timing-mode custom --days-after 365
```

### Comparison Studies

```bash
# Traditional approach (no timing constraints)
python create_model_gender_roc.py --no-timing-constraints

# Strict timing for comparison
python create_model_gender_roc.py --timing-mode strict
```

## Clinical Rationale

### Why Timing Constraints Matter

1. **Temporal Relationship**: Ensures diagnoses and medications are temporally related to the sleep study
2. **Data Quality**: Reduces noise from unrelated medical events
3. **Clinical Relevance**: Focuses on conditions likely related to sleep disorders
4. **Research Validity**: Improves the validity of predictive models

### Recommended Time Windows

Based on clinical practice and sleep medicine guidelines:

- **Hypertension**: Often develops or worsens due to untreated OSA
- **Antihypertensive Medications**: May be started after OSA diagnosis
- **Diabetes**: Can be related to OSA but may predate sleep study
- **Cardiovascular Disease**: Often has complex relationship with OSA

## Expected Impact on Results

### With Timing Constraints (Stricter)
- **Smaller sample sizes**: Fewer patients meet timing criteria
- **Higher data quality**: More relevant medical events
- **Potentially better AUC**: Less noise in the data
- **More conservative estimates**: Focus on clear temporal relationships

### Without Timing Constraints (Traditional)
- **Larger sample sizes**: All patients with any relevant diagnosis
- **Lower data quality**: May include unrelated medical events
- **Potentially lower AUC**: More noise in the data
- **Less conservative estimates**: Includes all historical data

## Output Differences

The script generates the same plots but with different underlying data:

1. **ROC Curves**: May show different AUC values based on timing constraints
2. **Sample Sizes**: Displayed patient counts will differ
3. **Subgroup Analysis**: Sensitivity/specificity may change
4. **Quality Checks**: Agreement between diagnoses and medications may improve

## Implementation Details

### Data Flow

1. **Database Connection**: Connects to patient database
2. **Timing Mode Selection**: Chooses appropriate extraction method
3. **Data Extraction**: Uses timing-constrained queries
4. **Model Training**: Trains models on filtered data
5. **Visualization**: Creates ROC curves and analysis plots

### Database Queries

The timing constraints are implemented at the SQL level:

```sql
-- Example: Hypertension diagnosis within 1 year after sleep study
JULIANDAY(diagnosis_date) - JULIANDAY(sleep_study_date) BETWEEN 0 AND 365
```

### Backward Compatibility

- Default behavior now uses timing constraints (strict mode)
- Use `--no-timing-constraints` to get traditional behavior
- All existing functionality remains available

## Troubleshooting

### Small Sample Sizes
If timing constraints result in very small sample sizes:
- Use `--timing-mode moderate` for more inclusive timing
- Use `--no-timing-constraints` for maximum sample size
- Consider `--timing-mode custom` with larger day ranges

### Missing Dependencies
- XGBoost model requires `pip install xgboost`
- Script will automatically disable XGBoost if not available

### Performance
- Timing-constrained queries may take longer to execute
- Consider using smaller day ranges for faster execution
- Database indexes on date fields improve performance

## Future Enhancements

1. **Multiple Sleep Studies**: Handle patients with multiple sleep studies
2. **Medication Duration**: Consider ongoing vs. discontinued medications
3. **Diagnosis Hierarchy**: Weight primary vs. secondary diagnoses differently
4. **Automated Threshold Selection**: Optimize timing windows based on data
