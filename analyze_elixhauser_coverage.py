#!/usr/bin/env python3
"""
Elixhauser ICD Code Coverage Analysis
------------------------------------
Analyzes how many ICD codes are available for each Elixhauser category
and how many are actually found in the dataset.
"""

import pandas as pd
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor

def analyze_icd_coverage():
    """Perform comprehensive ICD code coverage analysis."""
    print("ELIXHAUSER ICD CODE COVERAGE ANALYSIS")
    print("=" * 60)
    
    try:
        # Connect to database
        print("Connecting to database...")
        db_connector = DatabaseConnector('../patient_management.db')
        conn = db_connector.get_connection()
        
        # Extract data
        print("Extracting diagnosis data...")
        extractor = DataExtractor(conn)
        
        # Get coverage analysis
        coverage_df, detailed_mapping = extractor.analyze_elixhauser_icd_coverage()
        
        print(f"Analysis complete for {len(coverage_df)} Elixhauser categories")
        
        # Display summary statistics
        print(f"\nSUMMARY STATISTICS")
        print("-" * 40)
        
        total_patterns = coverage_df['Total_ICD_Patterns'].sum()
        total_matched = coverage_df['Matched_Patterns'].sum()
        total_patients = coverage_df['Patients_Affected'].sum()
        categories_with_patients = (coverage_df['Patients_Affected'] > 0).sum()
        
        print(f"Total ICD patterns defined: {total_patterns}")
        print(f"Total patterns matched in dataset: {total_matched}")
        print(f"Overall pattern coverage: {(total_matched/total_patterns*100):.1f}%")
        print(f"Categories with patients: {categories_with_patients}/{len(coverage_df)}")
        print(f"Total patient-comorbidity instances: {total_patients}")
        
        # Display detailed coverage by category
        print(f"\nDETAILED COVERAGE BY CATEGORY")
        print("-" * 80)
        print(f"{'Category':<35} {'Patterns':<8} {'Matched':<8} {'Codes':<6} {'Patients':<8} {'Coverage':<8}")
        print("-" * 80)
        
        for _, row in coverage_df.iterrows():
            print(f"{row['Comorbidity']:<35} "
                  f"{row['Total_ICD_Patterns']:<8} "
                  f"{row['Matched_Patterns']:<8} "
                  f"{row['Unique_Codes_Found']:<6} "
                  f"{row['Patients_Affected']:<8} "
                  f"{row['Pattern_Coverage_Pct']:<7.1f}%")
        
        # Show categories with highest patient counts
        print(f"\nTOP 10 CATEGORIES BY PATIENT COUNT")
        print("-" * 50)
        top_categories = coverage_df.head(10)
        for i, (_, row) in enumerate(top_categories.iterrows(), 1):
            print(f"{i:2d}. {row['Comorbidity']:<30} {row['Patients_Affected']:>4} patients")
        
        # Show categories with no patients found
        no_patients = coverage_df[coverage_df['Patients_Affected'] == 0]
        if len(no_patients) > 0:
            print(f"\nCATEGORIES WITH NO PATIENTS FOUND ({len(no_patients)} categories)")
            print("-" * 50)
            for _, row in no_patients.iterrows():
                print(f"• {row['Comorbidity']:<30} ({row['Total_ICD_Patterns']} ICD patterns defined)")
        
        # Show categories with best coverage
        good_coverage = coverage_df[coverage_df['Pattern_Coverage_Pct'] >= 50].sort_values('Pattern_Coverage_Pct', ascending=False)
        if len(good_coverage) > 0:
            print(f"\nCATEGORIES WITH GOOD PATTERN COVERAGE (≥50%)")
            print("-" * 60)
            for _, row in good_coverage.iterrows():
                print(f"• {row['Comorbidity']:<30} {row['Pattern_Coverage_Pct']:>5.1f}% "
                      f"({row['Matched_Patterns']}/{row['Total_ICD_Patterns']} patterns)")
        
        # Show sample ICD codes for top categories
        print(f"\nSAMPLE ICD CODES FOR TOP 5 CATEGORIES")
        print("-" * 60)
        for i, (_, row) in enumerate(coverage_df.head(5).iterrows(), 1):
            print(f"\n{i}. {row['Comorbidity']} ({row['Patients_Affected']} patients)")
            print(f"   Defined patterns: {row['Sample_Patterns']}")
            if row['Matched_ICD_Codes']:
                print(f"   Found in dataset: {row['Matched_ICD_Codes']}")
            else:
                print(f"   Found in dataset: None")
        
        # Detailed breakdown for specific high-impact categories
        print(f"\nDETAILED BREAKDOWN FOR KEY CATEGORIES")
        print("-" * 60)
        
        key_categories = ['Hypertension Uncomplicated', 'Depression', 'Diabetes Uncomplicated', 
                         'Cardiac Arrhythmias', 'Obesity']
        
        for category in key_categories:
            row = coverage_df[coverage_df['Comorbidity'] == category]
            if not row.empty:
                row = row.iloc[0]
                print(f"\n{category}:")
                print(f"  • Total ICD patterns defined: {row['Total_ICD_Patterns']}")
                print(f"  • Patterns matched in dataset: {row['Matched_Patterns']}")
                print(f"  • Unique codes found: {row['Unique_Codes_Found']}")
                print(f"  • Patients affected: {row['Patients_Affected']}")
                print(f"  • Coverage percentage: {row['Pattern_Coverage_Pct']:.1f}%")
                
                # Get detailed mapping for this category
                category_key = None
                for key, info in detailed_mapping.items():
                    if key == category:
                        category_key = key
                        break
                
                if category_key and category_key in detailed_mapping:
                    codes = detailed_mapping[category_key]['icd_codes']
                    print(f"  • All defined ICD codes: {', '.join(codes[:10])}")
                    if len(codes) > 10:
                        print(f"    ... and {len(codes)-10} more codes")
        
        # Close database connection
        db_connector.close_connection()
        
        print(f"\n" + "=" * 60)
        print("ANALYSIS COMPLETE")
        print("=" * 60)
        print("Key findings:")
        print(f"• {categories_with_patients}/{len(coverage_df)} Elixhauser categories have patients in the dataset")
        print(f"• {total_matched}/{total_patterns} ICD patterns are matched ({(total_matched/total_patterns*100):.1f}% coverage)")
        print(f"• {total_patients} total patient-comorbidity instances found")
        print("• Most common: " + ", ".join([row['Comorbidity'] for _, row in coverage_df.head(3).iterrows()]))
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_icd_coverage()
