# Pregnancy During Sleep Study Subcategory

## Overview

This subcategory identifies patients who were **pregnant during their sleep study**, based on:
1. **Pregnancy duration diagnosis codes** (O09) indicating specific gestational weeks
2. **Abortion/miscarriage codes** (O02) indicating pregnancy loss
3. **Sleep study procedures** (OPS 1-790) performed around the same time

This is clinically significant as it identifies sleep studies performed during active pregnancy, which is important for understanding pregnancy-related sleep disorders.

## Medical Background

### Pregnancy Duration Codes (O09)
O09 codes in the ICD-10 classification represent supervision of high-risk pregnancy with specific gestational week ranges:

- **O09.1**: 5-13 weeks (first trimester)
- **O09.2**: 14-19 weeks (second trimester early)  
- **O09.3**: 20-25 weeks (second trimester late)
- **O09.4**: 26-33 weeks (third trimester early)
- **O09.5**: 34-36 weeks (third trimester late)
- **O09.6**: 37-41 weeks (term pregnancy)

### Abortion/Miscarriage Codes (O02)
O02 codes represent abnormal products of conception:
- **O02.1**: Missed abortion (retained fetal death)
- **O02.8**: Other specified abnormal products of conception
- **O02.9**: Abnormal product of conception, unspecified

### Clinical Relevance
This subcategory helps identify:
- **Pregnancy-related sleep apnea**: Sleep studies performed during pregnancy
- **Gestational sleep disorders**: Sleep issues specific to pregnancy stages
- **High-risk pregnancy monitoring**: Sleep studies in complicated pregnancies
- **Pregnancy-specific sleep medicine**: Understanding sleep health during pregnancy

## Implementation

### New Function Added
```python
def get_pregnant_during_sleep_study_patients(self, days_window=30):
    """
    Get patients who were pregnant during their sleep study based on pregnancy duration codes (O09) 
    and abortion codes (O02). These codes indicate active pregnancy status at the time of diagnosis.
    
    Args:
        days_window (int): Number of days before/after sleep study to look for pregnancy codes (default: 30)
    
    Returns:
        DataFrame: Patient IDs with pregnancy diagnosis, sleep study date, and pregnancy details
    """
```

### Query Logic
The function:
1. Searches for O09 (pregnancy duration) and O02 (abortion) diagnoses in both primary and secondary diagnosis fields
2. Finds sleep study procedures (OPS 1-790) for the same patients
3. Uses a bidirectional time window (±days) around the sleep study date
4. Calculates time difference between pregnancy diagnosis and sleep study
5. Categorizes pregnancies by type and gestational week range
6. Returns detailed information including pregnancy stage and timing

### Output Columns
- `PID`: Patient ID
- `pregnancy_diagnosis_date`: Date of pregnancy/abortion diagnosis
- `primary_icd`: Primary diagnosis ICD code
- `primary_text`: Primary diagnosis description
- `secondary_icd`: Secondary diagnosis ICD code (if pregnancy code was secondary)
- `secondary_text`: Secondary diagnosis description
- `sleep_study_date`: Date of sleep study procedure
- `days_between`: Number of days between pregnancy diagnosis and sleep study (negative = before study, positive = after study)
- `pregnancy_category`: Type of pregnancy code ('pregnancy_duration' or 'abortion')
- `pregnancy_week_range`: Gestational week range for O09 codes ('5-13 weeks', '14-19 weeks', etc.)

## Usage Examples

### Basic Usage
```python
from osa_analysis.data.queries import DataExtractor
import sqlite3

conn = sqlite3.connect("patient_management.db")
extractor = DataExtractor(conn)

# Get patients who were pregnant during sleep study within 30 days
pregnant_patients = extractor.get_pregnant_during_sleep_study_patients(days_window=30)
```

### Different Time Windows
```python
# Within 7 days (very close timing)
pregnant_patients_7d = extractor.get_pregnant_during_sleep_study_patients(7)

# Within 14 days (close timing)
pregnant_patients_14d = extractor.get_pregnant_during_sleep_study_patients(14)

# Within 60 days (broader window)
pregnant_patients_60d = extractor.get_pregnant_during_sleep_study_patients(60)

# Within 90 days (very broad window)
pregnant_patients_90d = extractor.get_pregnant_during_sleep_study_patients(90)
```

## Analysis Scripts

### 1. `analyze_o0_sleep_study.py`
Comprehensive analysis script that:
- Identifies patients with O0 diagnoses followed by sleep studies
- Provides statistical summaries
- Shows most common O0 diagnosis types
- Creates visualizations
- Saves results to CSV

### 2. `example_o0_usage.py`
Simple example script demonstrating:
- Basic function usage
- Comparison with different time frames
- Integration with existing patient groups (OSA patients, etc.)

## Visualizations Created

The analysis script creates:
1. **Time Distribution Histogram**: Days between O0 diagnosis and sleep study
2. **Common Diagnoses Bar Chart**: Most frequent O0 diagnosis codes
3. **Timeline Scatter Plot**: O0 diagnosis dates vs. days to sleep study
4. **Box Plot by Diagnosis**: Time distribution grouped by O0 diagnosis type

## Clinical Applications

This subcategory could be valuable for:

### Research Questions
- How common are sleep disorders in patients with pregnancy complications?
- What is the typical timeline for sleep study referrals after O0 diagnoses?
- Are certain O0 diagnoses more likely to be followed by sleep studies?

### Quality Improvement
- Identifying patterns in sleep disorder screening after pregnancy complications
- Optimizing timing of sleep study referrals
- Understanding follow-up care patterns

### Population Health
- Monitoring sleep health in reproductive-age women
- Identifying high-risk groups for sleep disorders
- Tracking pregnancy-related sleep disorder outcomes

## Technical Notes

### Database Requirements
- Requires `Diagnosen` table with O0 diagnosis codes
- Requires `Prozeduren` table with sleep study procedures (OPS 1-790)
- Both tables must have date fields for timing analysis

### Performance Considerations
- Query uses JOIN between Diagnosen and Prozeduren tables
- Date calculations use SQLite JULIANDAY function
- Results are ordered by patient ID and dates for consistency

### Limitations
- Only captures sleep studies that occurred AFTER the O0 diagnosis
- Requires complete date information (NULL dates are excluded)
- Limited to procedural sleep studies (OPS 1-790) - may miss other sleep assessments

## Future Enhancements

Potential improvements could include:
1. **Extended O codes**: Include O1-O9 codes for broader pregnancy-related analysis
2. **Sleep disorder outcomes**: Link to specific sleep disorder diagnoses
3. **Medication analysis**: Examine sleep medications prescribed to this population
4. **Longitudinal tracking**: Follow patients through multiple pregnancies
5. **Comorbidity analysis**: Examine other conditions associated with this pattern
