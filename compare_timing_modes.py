#!/usr/bin/env python3
"""
Timing Constraints Comparison Tool
==================================

This script compares the results of different timing constraint modes
to help understand the impact of temporal filtering on OSA analysis.
"""

import argparse
import pandas as pd
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor

def compare_timing_modes():
    """Compare different timing constraint modes and show the differences."""
    
    print("OSA Analysis - Timing Constraints Comparison")
    print("=" * 50)
    
    # Connect to database
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()
    extractor = DataExtractor(conn)
    
    # Define timing modes to compare
    timing_modes = {
        'No Constraints': {
            'method': 'traditional',
            'description': 'All available data (traditional approach)'
        },
        'Strict': {
            'method': 'extract_all_data_with_strict_timing',
            'description': 'No limit before, 1 year after sleep study'
        },
        'Moderate': {
            'method': 'extract_all_data_with_moderate_timing',
            'description': '6 months before, 1 year after sleep study'
        },
        'Custom (90 days)': {
            'method': 'custom',
            'days_before': 90,
            'days_after': 365,
            'description': '3 months before, 1 year after sleep study'
        }
    }
    
    results = {}
    
    for mode_name, config in timing_modes.items():
        print(f"\nExtracting data for {mode_name}...")
        print(f"Description: {config['description']}")
        
        try:
            df = None
            if config['method'] == 'traditional':
                # Traditional approach - get individual datasets
                osa_patients = extractor.get_osa_patients()
                suspected_osa_patients = extractor.get_suspected_osa_patients()
                unsuspected_osa_patients = extractor.get_unsuspected_osa_patients()
                sleep_study_patients = extractor.get_sleep_study_patients()
                demographics = extractor.get_patient_demographics()
                hypertension_patients = extractor.get_hypertension_patients()
                antihypertensive_patients = extractor.get_patients_on_antihypertensives()
                diabetes_patients = extractor.get_diabetes_patients()
                cardiovascular_patients = extractor.get_cardiovascular_patients()
                bmi_data = extractor.get_bmi_data()
                med_classes, atc_classes = extractor.get_medication_by_atc_class()
                
                # Use DataProcessor to merge
                from osa_analysis.analysis.processor import DataProcessor
                processor = DataProcessor()
                df = processor.merge_data(
                    sleep_study_patients, osa_patients, suspected_osa_patients, 
                    unsuspected_osa_patients, demographics, hypertension_patients,
                    antihypertensive_patients, med_classes, diabetes_patients,
                    cardiovascular_patients, bmi_data
                )
                
                # Get medication data for top meds calculation
                med_data = extractor.get_medication_data()
                top_osa_meds = processor.get_top_medications(med_data, osa_patients)
                top_osa_atc = processor.get_top_atc_codes(med_data, osa_patients)
                
            elif config['method'] == 'extract_all_data_with_strict_timing':
                df, top_osa_meds, top_osa_atc, atc_classes = extractor.extract_all_data_with_timing()
                
            elif config['method'] == 'extract_all_data_with_moderate_timing':
                df, top_osa_meds, top_osa_atc, atc_classes = extractor.extract_all_data_with_moderate_timing()
                
            elif config['method'] == 'custom':
                df, top_osa_meds, top_osa_atc, atc_classes = extractor.extract_all_data(
                    use_sleep_study_timing=True,
                    days_before_sleep_study=config['days_before'],
                    days_after_sleep_study=config['days_after']
                )
            
            # Calculate summary statistics
            if df is not None:
                total_patients = len(df)
                osa_patients_count = df['has_osa'].sum()
                suspected_osa_count = df['has_suspected_osa'].sum()
                unsuspected_osa_count = df['has_unsuspected_osa'].sum()
                hypertension_count = df['has_diagnosed_hypertension'].sum()
                on_antihypertensives_count = df['on_antihypertensives'].sum()
                diabetes_count = df['has_diabetes'].sum()
                cardiovascular_count = df['has_cardiovascular'].sum()
                
                # Calculate percentages
                osa_rate = (osa_patients_count / total_patients * 100) if total_patients > 0 else 0
                hypertension_rate = (hypertension_count / total_patients * 100) if total_patients > 0 else 0
                diabetes_rate = (diabetes_count / total_patients * 100) if total_patients > 0 else 0
                cardiovascular_rate = (cardiovascular_count / total_patients * 100) if total_patients > 0 else 0
                
                results[mode_name] = {
                    'total_patients': total_patients,
                    'osa_patients': osa_patients_count,
                    'suspected_osa': suspected_osa_count,
                    'unsuspected_osa': unsuspected_osa_count,
                    'hypertension': hypertension_count,
                    'on_antihypertensives': on_antihypertensives_count,
                    'diabetes': diabetes_count,
                    'cardiovascular': cardiovascular_count,
                    'osa_rate': osa_rate,
                    'hypertension_rate': hypertension_rate,
                    'diabetes_rate': diabetes_rate,
                    'cardiovascular_rate': cardiovascular_rate,
                    'description': config['description']
                }
                
                print(f"  ✅ Success: {total_patients} patients")
            else:
                raise ValueError("DataFrame is None")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            results[mode_name] = {'error': str(e)}
    
    # Close database connection
    db_connector.close_connection()
    
    # Display comparison table
    print(f"\n{'='*80}")
    print("TIMING CONSTRAINTS COMPARISON RESULTS")
    print("="*80)
    
    # Create comparison DataFrame
    comparison_data = []
    for mode_name, data in results.items():
        if 'error' not in data:
            comparison_data.append({
                'Timing Mode': mode_name,
                'Description': data['description'],
                'Total Patients': data['total_patients'],
                'OSA Patients': data['osa_patients'],
                'OSA Rate (%)': f"{data['osa_rate']:.1f}%",
                'Hypertension': data['hypertension'],
                'HTN Rate (%)': f"{data['hypertension_rate']:.1f}%",
                'Diabetes': data['diabetes'],
                'DM Rate (%)': f"{data['diabetes_rate']:.1f}%",
                'Cardiovascular': data['cardiovascular'],
                'CVD Rate (%)': f"{data['cardiovascular_rate']:.1f}%"
            })
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        
        # Display key metrics
        print("\nKEY METRICS COMPARISON:")
        print("-" * 40)
        for _, row in comparison_df.iterrows():
            print(f"\n{row['Timing Mode']}:")
            print(f"  Description: {row['Description']}")
            print(f"  Total Patients: {row['Total Patients']}")
            print(f"  OSA Patients: {row['OSA Patients']} ({row['OSA Rate (%)']})")
            print(f"  Hypertension: {row['Hypertension']} ({row['HTN Rate (%)']})")
            print(f"  Diabetes: {row['Diabetes']} ({row['DM Rate (%)']})")
            print(f"  Cardiovascular: {row['Cardiovascular']} ({row['CVD Rate (%)']})")
        
        # Calculate differences from baseline (No Constraints)
        baseline = None
        for mode_name, data in results.items():
            if mode_name == 'No Constraints' and 'error' not in data:
                baseline = data
                break
        
        if baseline:
            print(f"\n{'='*80}")
            print("DIFFERENCES FROM BASELINE (No Constraints)")
            print("="*80)
            
            for mode_name, data in results.items():
                if mode_name != 'No Constraints' and 'error' not in data:
                    patient_diff = data['total_patients'] - baseline['total_patients']
                    patient_pct_diff = (patient_diff / baseline['total_patients'] * 100) if baseline['total_patients'] > 0 else 0
                    
                    htn_diff = data['hypertension'] - baseline['hypertension']
                    dm_diff = data['diabetes'] - baseline['diabetes']
                    cvd_diff = data['cardiovascular'] - baseline['cardiovascular']
                    
                    print(f"\n{mode_name}:")
                    print(f"  Patient count change: {patient_diff:+d} ({patient_pct_diff:+.1f}%)")
                    print(f"  Hypertension change: {htn_diff:+d}")
                    print(f"  Diabetes change: {dm_diff:+d}")
                    print(f"  Cardiovascular change: {cvd_diff:+d}")
        
        print(f"\n{'='*80}")
        print("SUMMARY AND RECOMMENDATIONS")
        print("="*80)
        print("\nKey Observations:")
        print("- More restrictive timing generally reduces patient counts")
        print("- Comorbidity rates may change due to temporal filtering")
        print("- Stricter timing improves clinical relevance of associations")
        print("- Sample size vs. data quality trade-off is evident")
        
        print("\nRecommendations:")
        print("- Use 'Strict' or 'Moderate' for clinical research")
        print("- Use 'No Constraints' for comprehensive epidemiological studies")
        print("- Consider 'Custom' timing based on specific research questions")
        print("- Monitor sample sizes to ensure adequate statistical power")
    
    else:
        print("No successful data extractions to compare.")

def main():
    """Main function to run the timing constraints comparison."""
    parser = argparse.ArgumentParser(description='Compare different timing constraint modes for OSA analysis')
    parser.parse_args()  # Parse but don't store since we don't use any arguments yet
    
    try:
        compare_timing_modes()
    except Exception as e:
        print(f"Error running comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
