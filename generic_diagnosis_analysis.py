#!/usr/bin/env python3
"""
Generic Diagnosis Around Sleep Study Analysis
===========================================

This script demonstrates how to use the generic function to find patients with any diagnosis
around their sleep study with flexible time windows.

Examples include:
- Pregnancy diagnoses (O codes)
- Sleep disorders (G47 codes) 
- Hypertension (I10-I15 codes)
- Diabetes (E10-E14 codes)
- Any custom diagnosis pattern
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Import the data extractor
from osa_analysis.data.queries import DataExtractor

def analyze_generic_diagnosis_sleep_study(db_path="patient_management.db", 
                                        diagnosis_pattern="O09%", 
                                        days_before=30, 
                                        days_after=30,
                                        diagnosis_field="both",
                                        diagnosis_type=None,
                                        analysis_name="Generic"):
    """
    Analyze patients with specified diagnosis around their sleep study.
    
    Args:
        db_path (str): Path to the SQLite database
        diagnosis_pattern (str): SQL LIKE pattern for diagnosis codes
        days_before (int): Days before sleep study to look for diagnosis
        days_after (int): Days after sleep study to look for diagnosis
        diagnosis_field (str): Which diagnosis field to search ('primary', 'secondary', 'both')
        diagnosis_type (str): Filter by diagnosis type (optional)
        analysis_name (str): Name for the analysis (used in output files)
    """
    print(f"{analysis_name} Diagnosis Around Sleep Study Analysis")
    print("=" * 60)
    print(f"Diagnosis pattern: {diagnosis_pattern}")
    print(f"Time window: -{days_before} to +{days_after} days around sleep study")
    print(f"Diagnosis field: {diagnosis_field}")
    if diagnosis_type:
        print(f"Diagnosis type filter: {diagnosis_type}")
    print()
    
    # Connect to database
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    extractor = DataExtractor(conn)
    
    try:
        # Get patients with diagnosis around sleep study
        results = extractor.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern=diagnosis_pattern,
            days_before=days_before,
            days_after=days_after,
            diagnosis_field=diagnosis_field,
            diagnosis_type=diagnosis_type
        )
        
        if results.empty:
            print("No patients found with specified criteria.")
            return
        
        print(f"Found {len(results)} records matching criteria.")
        print(f"Unique patients: {results['PID'].nunique()}")
        print()
        
        # Display timing statistics
        print("Timing between diagnosis and sleep study:")
        print(f"  Mean: {results['days_between'].mean():.1f} days")
        print(f"  Median: {results['days_between'].median():.1f} days")
        print(f"  Range: {results['days_between'].min():.0f} to {results['days_between'].max():.0f} days")
        print()
        
        # Analyze timing categories
        print("Timing categories:")
        timing_counts = results['timing_category'].value_counts()
        for category, count in timing_counts.items():
            percentage = count/len(results)*100
            print(f"  {category}: {count} cases ({percentage:.1f}%)")
        print()
        
        # Analyze diagnosis position
        print("Diagnosis position:")
        position_counts = results['diagnosis_position'].value_counts()
        for position, count in position_counts.items():
            percentage = count/len(results)*100
            print(f"  {position}: {count} cases ({percentage:.1f}%)")
        print()
        
        # Show most common specific diagnoses
        print("Most common specific diagnoses:")
        primary_diagnoses = results[results['diagnosis_position'] == 'primary']
        if not primary_diagnoses.empty:
            primary_counts = primary_diagnoses['primary_icd'].value_counts().head(10)
            for icd, count in primary_counts.items():
                if icd is not None and str(icd) != 'nan':
                    text = primary_diagnoses[primary_diagnoses['primary_icd'] == icd]['primary_text'].iloc[0]
                    print(f"  {icd}: {count} cases - {text}")
        
        secondary_diagnoses = results[results['diagnosis_position'] == 'secondary']
        if not secondary_diagnoses.empty:
            print("\nSecondary diagnoses:")
            secondary_counts = secondary_diagnoses['secondary_icd'].value_counts().head(5)
            for icd, count in secondary_counts.items():
                if icd is not None and str(icd) != 'nan':
                    text = secondary_diagnoses[secondary_diagnoses['secondary_icd'] == icd]['secondary_text'].iloc[0]
                    print(f"  {icd}: {count} cases - {text}")
        print()
        
        # Analyze diagnosis types if available
        if 'diagnosis_type' in results.columns and results['diagnosis_type'].notna().any():
            print("Diagnosis types:")
            type_counts = results['diagnosis_type'].value_counts()
            for diag_type, count in type_counts.items():
                percentage = count/len(results)*100
                print(f"  {diag_type}: {count} cases ({percentage:.1f}%)")
            print()
        
        # Show sample cases
        print("Sample cases (first 5):")
        sample_cases = results.head()
        for _, case in sample_cases.iterrows():
            timing = "before" if case['days_between'] < 0 else ("same day" if case['days_between'] == 0 else "after")
            print(f"  Patient {case['PID']}:")
            print(f"    Diagnosis: {case['diagnosis_date']} - {case['primary_icd']} ({case['diagnosis_position']})")
            print(f"    Sleep Study: {case['sleep_study_date']} ({abs(case['days_between']):.0f} days {timing})")
            print()
        
        # Create visualization
        create_generic_visualizations(results, analysis_name, diagnosis_pattern)
        
        # Save results to CSV
        safe_pattern = diagnosis_pattern.replace('%', '').replace('*', '').replace('?', '')
        output_file = f"diagnosis_{safe_pattern}_{analysis_name.lower().replace(' ', '_')}_b{days_before}_a{days_after}.csv"
        results.to_csv(output_file, index=False)
        print(f"Results saved to: {output_file}")
        
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
    finally:
        conn.close()

def create_generic_visualizations(results, analysis_name, diagnosis_pattern):
    """Create visualizations for generic diagnosis analysis."""
    
    plt.style.use('default')
    sns.set_palette("husl")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'{analysis_name} Diagnosis Around Sleep Study Analysis\nPattern: {diagnosis_pattern}', 
                 fontsize=14, fontweight='bold')
    
    # 1. Distribution of days between diagnosis and sleep study
    axes[0, 0].hist(results['days_between'], bins=30, alpha=0.7, edgecolor='black')
    axes[0, 0].set_xlabel('Days between diagnosis and sleep study')
    axes[0, 0].set_ylabel('Number of patients')
    axes[0, 0].set_title('Time Distribution')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Sleep study date')
    axes[0, 0].legend()
    
    # 2. Timing categories pie chart
    timing_counts = results['timing_category'].value_counts()
    if len(timing_counts) > 0:
        axes[0, 1].pie(timing_counts.values, labels=timing_counts.index, autopct='%1.1f%%')
        axes[0, 1].set_title('Timing Categories')
    
    # 3. Most common diagnoses
    primary_diagnoses = results[results['diagnosis_position'] == 'primary']
    if not primary_diagnoses.empty:
        top_diagnoses = primary_diagnoses['primary_icd'].value_counts().head(8)
        if len(top_diagnoses) > 0:
            axes[1, 0].barh(range(len(top_diagnoses)), top_diagnoses.values)
            axes[1, 0].set_yticks(range(len(top_diagnoses)))
            axes[1, 0].set_yticklabels(top_diagnoses.index)
            axes[1, 0].set_xlabel('Number of cases')
            axes[1, 0].set_title('Most Common Diagnoses')
            axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Timeline scatter plot
    if not results.empty:
        results['diagnosis_date_numeric'] = pd.to_datetime(results['diagnosis_date']).map(pd.Timestamp.timestamp)
        axes[1, 1].scatter(results['diagnosis_date_numeric'], results['days_between'], 
                          alpha=0.6, c=results['timing_category'].astype('category').cat.codes, 
                          cmap='viridis')
        axes[1, 1].set_xlabel('Diagnosis Date')
        axes[1, 1].set_ylabel('Days between diagnosis and sleep study')
        axes[1, 1].set_title('Timeline Analysis')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5, label='Sleep study date')
        axes[1, 1].legend()
    
    plt.tight_layout()
    
    # Save plot
    safe_pattern = diagnosis_pattern.replace('%', '').replace('*', '').replace('?', '')
    plot_filename = f'diagnosis_{safe_pattern}_{analysis_name.lower().replace(" ", "_")}_analysis.png'
    plt.savefig(f'plots/{plot_filename}', dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: plots/{plot_filename}")
    
    plt.show()

def run_example_analyses():
    """Run several example analyses to demonstrate the generic function."""
    
    print("Generic Diagnosis Around Sleep Study - Example Analyses")
    print("=" * 60)
    print()
    
    # Example 1: Pregnancy diagnoses
    print("Example 1: Pregnancy Duration Diagnoses (O09)")
    analyze_generic_diagnosis_sleep_study(
        diagnosis_pattern="O09%",
        days_before=35,
        days_after=7,
        analysis_name="Pregnancy"
    )
    print("\n" + "="*60 + "\n")
    
    # Example 2: Sleep disorders
    print("Example 2: Sleep Disorders (G47)")
    analyze_generic_diagnosis_sleep_study(
        diagnosis_pattern="G47%",
        days_before=90,
        days_after=90,
        analysis_name="Sleep_Disorders"
    )
    print("\n" + "="*60 + "\n")
    
    # Example 3: Hypertension - admission diagnoses only
    print("Example 3: Hypertension (I1) - Admission Diagnoses Only")
    analyze_generic_diagnosis_sleep_study(
        diagnosis_pattern="I1%",
        days_before=180,
        days_after=180,
        diagnosis_type="Aufnahmediagnose",
        analysis_name="Hypertension_Admission"
    )
    print("\n" + "="*60 + "\n")
    
    # Example 4: Diabetes - primary diagnoses only
    print("Example 4: Diabetes (E1) - Primary Diagnoses Only")
    analyze_generic_diagnosis_sleep_study(
        diagnosis_pattern="E1%",
        days_before=365,
        days_after=90,
        diagnosis_field="primary",
        analysis_name="Diabetes_Primary"
    )

def interactive_analysis():
    """Interactive mode for custom analysis."""
    
    print("Interactive Generic Diagnosis Analysis")
    print("=" * 40)
    
    # Get user inputs
    diagnosis_pattern = input("Enter diagnosis pattern (e.g., O09%, G47%, I1%): ").strip()
    if not diagnosis_pattern:
        diagnosis_pattern = "O09%"
    
    try:
        days_before = int(input("Days before sleep study to search (default 30): ") or "30")
        days_after = int(input("Days after sleep study to search (default 30): ") or "30")
    except ValueError:
        days_before, days_after = 30, 30
    
    diagnosis_field = input("Search in which field? (primary/secondary/both, default both): ").strip().lower()
    if diagnosis_field not in ['primary', 'secondary', 'both']:
        diagnosis_field = 'both'
    
    diagnosis_type = input("Filter by diagnosis type? (e.g., Aufnahmediagnose, Entlassdiagnose, or leave empty): ").strip()
    if not diagnosis_type:
        diagnosis_type = None
    
    analysis_name = input("Analysis name (default Custom): ").strip()
    if not analysis_name:
        analysis_name = "Custom"
    
    db_path = input("Database path (default patient_management.db): ").strip()
    if not db_path:
        db_path = "patient_management.db"
    
    # Run analysis
    analyze_generic_diagnosis_sleep_study(
        db_path=db_path,
        diagnosis_pattern=diagnosis_pattern,
        days_before=days_before,
        days_after=days_after,
        diagnosis_field=diagnosis_field,
        diagnosis_type=diagnosis_type,
        analysis_name=analysis_name
    )

def main():
    """Main function."""
    print("Generic Diagnosis Around Sleep Study Analysis Tool")
    print("=" * 50)
    print()
    print("Choose an option:")
    print("1. Run example analyses")
    print("2. Interactive custom analysis")
    print("3. Quick pregnancy analysis (O09)")
    print()
    
    choice = input("Enter your choice (1/2/3): ").strip()
    
    if choice == "1":
        run_example_analyses()
    elif choice == "2":
        interactive_analysis()
    elif choice == "3":
        analyze_generic_diagnosis_sleep_study(
            diagnosis_pattern="O09%",
            days_before=35,
            days_after=7,
            analysis_name="Pregnancy_Quick"
        )
    else:
        print("Invalid choice. Running example analyses...")
        run_example_analyses()

if __name__ == "__main__":
    main()
