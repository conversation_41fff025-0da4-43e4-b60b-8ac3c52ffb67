#!/usr/bin/env python3
"""
Test script for the generic diagnosis around sleep study function
"""

import sqlite3
from osa_analysis.data.queries import DataExtractor

def test_generic_function():
    """Test the new generic diagnosis around sleep study function."""
    
    try:
        # Connect to database
        conn = sqlite3.connect("patient_management.db")
        extractor = DataExtractor(conn)
        
        print("Testing generic diagnosis around sleep study function...")
        print("=" * 60)
        
        # Test 1: Pregnancy diagnoses
        print("Test 1: Pregnancy diagnoses (O09%) within ±30 days")
        result1 = extractor.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern="O09%",
            days_before=30,
            days_after=30
        )
        print(f"  Found {len(result1)} records, {result1['PID'].nunique() if not result1.empty else 0} unique patients")
        
        if not result1.empty:
            timing_counts = result1['timing_category'].value_counts()
            for category, count in timing_counts.items():
                print(f"    {category}: {count}")
        print()
        
        # Test 2: Sleep disorders
        print("Test 2: Sleep disorders (G47%) within ±60 days")
        result2 = extractor.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern="G47%",
            days_before=60,
            days_after=60
        )
        print(f"  Found {len(result2)} records, {result2['PID'].nunique() if not result2.empty else 0} unique patients")
        print()
        
        # Test 3: Hypertension - primary diagnoses only
        print("Test 3: Hypertension (I1%) - primary diagnoses only, ±90 days")
        result3 = extractor.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern="I1%",
            days_before=90,
            days_after=90,
            diagnosis_field="primary"
        )
        print(f"  Found {len(result3)} records, {result3['PID'].nunique() if not result3.empty else 0} unique patients")
        print()
        
        # Test 4: Admission diagnoses with specific pattern
        print("Test 4: Any admission diagnoses starting with O (O%) within ±14 days")
        result4 = extractor.get_patients_with_diagnosis_around_sleep_study(
            diagnosis_pattern="O%",
            days_before=14,
            days_after=14,
            diagnosis_type="Aufnahmediagnose"
        )
        print(f"  Found {len(result4)} records, {result4['PID'].nunique() if not result4.empty else 0} unique patients")
        print()
        
        # Show sample data from largest result
        largest_result = max([result1, result2, result3, result4], key=len)
        if not largest_result.empty:
            print("Sample data from largest result:")
            print("Columns:", list(largest_result.columns))
            print("\nFirst 2 records:")
            sample = largest_result[['PID', 'diagnosis_date', 'primary_icd', 'sleep_study_date', 
                                   'days_between', 'timing_category', 'diagnosis_position']].head(2)
            print(sample.to_string(index=False))
        
        # Test different time windows on same diagnosis
        print("\nTime window comparison for O09% pattern:")
        for window in [7, 14, 30, 60]:
            test_result = extractor.get_patients_with_diagnosis_around_sleep_study(
                diagnosis_pattern="O09%",
                days_before=window,
                days_after=window
            )
            print(f"  ±{window} days: {len(test_result)} records")
        
        conn.close()
        print("\nGeneric function test completed successfully!")
        
    except Exception as e:
        print(f"Error during test: {e}")

if __name__ == "__main__":
    test_generic_function()
