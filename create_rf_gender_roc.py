#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create gender-stratified ROC curves for the Random Forest model.
"""

import pandas as pd
from osa_analysis.data.loader import DataLoader
from osa_analysis.analysis.statistics import StatisticsGenerator

def main():
    """Create gender-stratified ROC curves for Random Forest model."""
    
    # Load the data
    print("Loading data...")
    loader = DataLoader()
    df = loader.load_and_merge_data()
    
    # Get ATC classes for model features
    from osa_analysis.data.database import DatabaseConnector
    from osa_analysis.data.queries import DataExtractor
    
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()
    extractor = DataExtractor(conn)
    _, atc_classes = extractor.get_medication_by_atc_class()
    db_connector.close_connection()
    
    # Create gender-stratified ROC curves
    print("Creating gender-stratified ROC curves for Random Forest model...")
    stats_generator = StatisticsGenerator()
    
    # Generate ROC curve for Random Forest model
    gender_metrics = stats_generator.create_gender_stratified_roc_curve_rf(
        df=df,
        atc_classes=atc_classes,
        save_path='plots/rf_gender_roc.png'
    )
    
    print("\nAnalysis complete!")
    print("Check the 'plots' directory for the generated ROC curve.")

if __name__ == "__main__":
    main()
