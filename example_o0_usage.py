#!/usr/bin/env python3
"""
Example: Using the new pregnancy during sleep study subcategory
============================================================

This script demonstrates how to use the new get_pregnant_during_sleep_study_patients function
that was added to the DataExtractor class.
"""

import sqlite3
import pandas as pd
from osa_analysis.data.queries import DataExtractor

def example_usage():
    """Example of how to use the new pregnancy during sleep study function with pregnancy-specific time windows."""
    
    # Connect to the database
    db_path = "patient_management.db"  # Adjust path as needed
    conn = sqlite3.connect(db_path)
    
    # Create data extractor instance
    extractor = DataExtractor(conn)
    
    # Example 1: Get patients pregnant during sleep studies with default windows
    print("Example 1: Patients pregnant during sleep studies (default time windows)")
    print("- O09.1 (5-13 weeks): 35 days before diagnosis")
    print("- O09.2+ (14+ weeks): 35 days before diagnosis") 
    print("- O02 (abortion): ±35 days around diagnosis")
    print("-" * 70)
    
    pregnant_patients_default = extractor.get_pregnant_during_sleep_study_patients(days_window=35)
    print(f"Found {len(pregnant_patients_default)} records")
    print(f"Unique patients: {pregnant_patients_default['PID'].nunique()}")
    print()
    
    # Example 2: Get patients with stricter time windows
    print("Example 2: Patients pregnant during sleep studies (stricter: 21 days)")
    print("- O09.1 (5-13 weeks): 35 days before diagnosis (fixed)")
    print("- O09.2+ (14+ weeks): 21 days before diagnosis") 
    print("- O02 (abortion): ±21 days around diagnosis")
    print("-" * 70)
    
    pregnant_patients_strict = extractor.get_pregnant_during_sleep_study_patients(days_window=21)
    print(f"Found {len(pregnant_patients_strict)} records")
    print(f"Unique patients: {pregnant_patients_strict['PID'].nunique()}")
    print()
    
    # Example 3: Get patients with broader time windows
    print("Example 3: Patients pregnant during sleep studies (broader: 60 days)")
    print("- O09.1 (5-13 weeks): 35 days before diagnosis (fixed)")
    print("- O09.2+ (14+ weeks): 60 days before diagnosis") 
    print("- O02 (abortion): ±60 days around diagnosis")
    print("-" * 70)
    
    pregnant_patients_broad = extractor.get_pregnant_during_sleep_study_patients(days_window=60)
    print(f"Found {len(pregnant_patients_broad)} records")
    print(f"Unique patients: {pregnant_patients_broad['PID'].nunique()}")
    print()
    
    # Display some sample data if available
    if not pregnant_patients_default.empty:
        print("Sample data (first 3 records from default analysis):")
        print(pregnant_patients_default[['PID', 'pregnancy_diagnosis_date', 'primary_icd', 'primary_text', 
                                       'sleep_study_date', 'days_between', 'pregnancy_week_range']].head(3).to_string(index=False))
        print()
        
        # Analyze pregnancy week ranges with new logic
        print("Pregnancy week ranges and their time windows:")
        for week_range in pregnant_patients_default['pregnancy_week_range'].unique():
            if week_range != 'unknown':
                stage_data = pregnant_patients_default[pregnant_patients_default['pregnancy_week_range'] == week_range]
                window_used = "35 days (fixed)" if week_range == '5-13 weeks' else "35 days (variable)"
                print(f"  {week_range}: {len(stage_data)} cases, using {window_used}")
                if not stage_data.empty:
                    avg_days = stage_data['days_between'].mean()
                    print(f"    Average days between diagnosis and sleep study: {avg_days:.1f}")
        print()
    
    # Compare with other patient groups for context
    print("Comparison with other patient groups:")
    print("-" * 40)
    
    # Get all sleep study patients
    all_sleep_study = extractor.get_sleep_study_patients()
    print(f"Total patients with sleep studies: {len(all_sleep_study)}")
    
    # Get OSA patients
    osa_patients = extractor.get_osa_patients()
    print(f"Total patients with OSA diagnosis: {len(osa_patients)}")
    
    # Check overlap
    if not pregnant_patients_default.empty:
        pregnant_pids = set(pregnant_patients_default['PID'].unique())
        osa_pids = set(osa_patients['PID'].unique())
        overlap = pregnant_pids.intersection(osa_pids)
        print(f"Pregnant during sleep study patients who also have OSA: {len(overlap)}")
        
        if overlap:
            print(f"Overlap percentage: {len(overlap)/len(pregnant_pids)*100:.1f}% of pregnant during sleep study patients")
            
        # Show time window effectiveness
        print("\nTime window effectiveness comparison:")
        print(f"  Default (35 days): {len(pregnant_patients_default)} cases")
        print(f"  Strict (21 days): {len(pregnant_patients_strict)} cases")
        print(f"  Broad (60 days): {len(pregnant_patients_broad)} cases")
    
    conn.close()

def analyze_pregnancy_codes_only():
    """Analyze just the pregnancy diagnosis codes without requiring sleep studies."""
    
    db_path = "patient_management.db"
    conn = sqlite3.connect(db_path)
    
    # Simple query to find all pregnancy duration and abortion diagnoses
    query = """
    SELECT DISTINCT 
        PID,
        Feststellungsdatum,
        PrimaerdiagnoseICD,
        PrimaerdiagnoseText,
        SekundaerdiagnoseICD,
        SekundaerdiagnoseText
    FROM Diagnosen 
    WHERE PrimaerdiagnoseICD LIKE 'O09%' OR SekundaerdiagnoseICD LIKE 'O09%'
       OR PrimaerdiagnoseICD LIKE 'O02%' OR SekundaerdiagnoseICD LIKE 'O02%'
    ORDER BY Feststellungsdatum
    """
    
    pregnancy_diagnoses = pd.read_sql_query(query, conn)
    
    print("All pregnancy duration and abortion diagnoses in the database:")
    print("-" * 60)
    print(f"Total pregnancy diagnosis records: {len(pregnancy_diagnoses)}")
    print(f"Unique patients with pregnancy diagnoses: {pregnancy_diagnoses['PID'].nunique()}")
    print()
    
    # Show most common pregnancy codes
    primary_pregnancy = pregnancy_diagnoses[pregnancy_diagnoses['PrimaerdiagnoseICD'].str.startswith(('O09', 'O02'), na=False)]
    if not primary_pregnancy.empty:
        print("Most common primary pregnancy diagnoses:")
        primary_counts = primary_pregnancy['PrimaerdiagnoseICD'].value_counts().head(10)
        for icd, count in primary_counts.items():
            # Get description
            text = primary_pregnancy[primary_pregnancy['PrimaerdiagnoseICD'] == icd]['PrimaerdiagnoseText'].iloc[0]
            print(f"  {icd}: {count} cases - {text}")
    
    conn.close()

if __name__ == "__main__":
    print("Pregnancy During Sleep Study Function Examples")
    print("=" * 55)
    print()
    
    try:
        # First show all pregnancy diagnoses
        analyze_pregnancy_codes_only()
        print("\n" + "=" * 55 + "\n")
        
        # Then show the new subcategory analysis
        example_usage()
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure the database exists and the osa_analysis module is properly installed.")
