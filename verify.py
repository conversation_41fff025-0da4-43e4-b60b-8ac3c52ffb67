import sqlite3
import pandas as pd

def verify_data_loading(db_path):
    """Verify that data was loaded correctly into the SQLite database"""
    conn = sqlite3.connect(db_path)
    
    # Get list of tables
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("Database Tables:")
    for table in tables:
        table_name = table[0]
        if table_name != 'sqlite_sequence':  # Skip internal SQLite table
            # Count rows in the table
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            # Get sample data (first 3 rows)
            df = pd.read_sql_query(f"SELECT * FROM {table_name} LIMIT 3", conn)
            
            print(f"\n{'-'*50}")
            print(f"Table: {table_name}")
            print(f"Row count: {row_count}")
            print(f"Sample data:")
            print(df.head())
    
    conn.close()

if __name__ == "__main__":
    db_path = "patient_management.db"
    verify_data_loading(db_path)
