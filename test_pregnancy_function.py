#!/usr/bin/env python3
"""
Test script for the pregnancy during sleep study function
"""

import sqlite3
from osa_analysis.data.queries import DataExtractor

def test_pregnancy_function():
    """Test the new pregnancy during sleep study function with pregnancy-specific time windows."""
    
    try:
        # Connect to database
        conn = sqlite3.connect("patient_management.db")
        extractor = DataExtractor(conn)
        
        print("Testing pregnancy during sleep study function with pregnancy-specific time windows...")
        print("=" * 75)
        print("Logic:")
        print("- O09.1 (5-13 weeks): Sleep study within 35 days before pregnancy diagnosis")
        print("- O09.2+ (14+ weeks): Sleep study within days_window before pregnancy diagnosis")
        print("- O02 (abortion): Sleep study within ±days_window of diagnosis")
        print()
        
        # Test the function with a 35-day window
        result = extractor.get_pregnant_during_sleep_study_patients(35)
        
        print("Results with 35-day window (for O09.2+):")
        print(f"  Total records: {len(result)}")
        print(f"  Unique patients: {result['PID'].nunique() if not result.empty else 0}")
        
        if not result.empty:
            print(f"  Columns: {list(result.columns)}")
            print(f"  Date range: {result['sleep_study_date'].min()} to {result['sleep_study_date'].max()}")
            
            # Show pregnancy categories
            print("  Pregnancy categories:")
            for category, count in result['pregnancy_category'].value_counts().items():
                print(f"    {category}: {count}")
                
            # Show pregnancy week ranges and validate time windows
            print("  Pregnancy week ranges with time window validation:")
            for week_range, count in result['pregnancy_week_range'].value_counts().items():
                if week_range != 'unknown':
                    stage_data = result[result['pregnancy_week_range'] == week_range]
                    expected_window = 35 if week_range == '5-13 weeks' else 35  # Using 35 for test
                    
                    # Check if all cases are within expected window
                    valid_cases = stage_data[(stage_data['days_between'] >= 0) & 
                                           (stage_data['days_between'] <= expected_window)]
                    if week_range != '5-13 weeks':  # O02 can be before or after
                        valid_cases = stage_data[abs(stage_data['days_between']) <= expected_window]
                    
                    print(f"    {week_range}: {count} cases, {len(valid_cases)}/{count} within {expected_window}-day window")
                    if not stage_data.empty:
                        print(f"      Days between range: {stage_data['days_between'].min():.0f} to {stage_data['days_between'].max():.0f}")
            
            print("\nFirst 3 records by pregnancy stage:")
            for week_range in ['5-13 weeks', '14-19 weeks', '20-25 weeks', '26-33 weeks', '34-36 weeks', '37-41 weeks']:
                stage_data = result[result['pregnancy_week_range'] == week_range]
                if not stage_data.empty:
                    print(f"\n  {week_range}:")
                    sample = stage_data.head(1)
                    for _, case in sample.iterrows():
                        print(f"    PID: {case['PID']}, Pregnancy: {case['pregnancy_diagnosis_date']}, " +
                              f"Sleep Study: {case['sleep_study_date']}, Days: {case['days_between']:.0f}")
                        
        else:
            print("  No records found.")
        
        # Test with different time windows to see the effect
        print("\nTesting different time windows:")
        for test_window in [21, 35, 60]:
            test_result = extractor.get_pregnant_during_sleep_study_patients(test_window)
            print(f"  {test_window}-day window: {len(test_result)} records, {test_result['PID'].nunique() if not test_result.empty else 0} patients")
        
        conn.close()
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Error during test: {e}")

if __name__ == "__main__":
    test_pregnancy_function()
